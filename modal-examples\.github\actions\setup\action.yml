name: setup

description: Set up a Python environment for the examples.

inputs:
  version:
    description: Which Python version to install
    required: false
    default: "3.11"
  devDependencies:
    description: Whether to skip dependencies
    required: false
    default: "no-skip"

runs:
  using: composite
  steps:
    - name: Install Python
      uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5
      with:
        python-version: ${{ inputs.version }}

    - name: Install base packages
      shell: bash
      run: |
        pip install uv
        uv pip install --system setuptools wheel

    - name: Install development Python packages
      if: ${{ inputs.devDependencies != 'skip' }}
      shell: bash
      run: uv pip install --system -r internal/requirements.txt

    - name: Install the modal client
      shell: bash
      run: uv pip install --system modal
