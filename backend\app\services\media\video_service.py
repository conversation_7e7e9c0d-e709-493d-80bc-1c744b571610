"""
Video processing service using FFmpeg
"""

import asyncio
import subprocess
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import json
import uuid

from app.core.config import settings
from app.core.database import SessionLocal
from app.models.course import Course, Module, Lesson
from app.services.ai.whisper_service import WhisperService


class VideoService:
    """Service for video processing using FFmpeg"""
    
    def __init__(self):
        self.output_dir = Path("media/generated/videos")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir = Path("media/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.whisper_service = WhisperService()
    
    async def assemble_course_videos(
        self,
        course_id: str,
        audio_files: List[str],
        slide_files: List[str]
    ) -> List[str]:
        """Assemble videos for all lessons in a course"""
        
        db = SessionLocal()
        try:
            course = db.query(Course).filter(Course.id == course_id).first()
            if not course:
                raise ValueError(f"Course {course_id} not found")
            
            video_files = []
            
            # Process each lesson
            lesson_index = 0
            for module in course.modules:
                for lesson in module.lessons:
                    if lesson_index < len(audio_files):
                        audio_file = audio_files[lesson_index]
                        slide_file = slide_files[lesson_index] if lesson_index < len(slide_files) else None
                        
                        video_file = await self.create_lesson_video(
                            lesson.id,
                            audio_file,
                            slide_file,
                            lesson.title
                        )
                        
                        if video_file:
                            video_files.append(video_file)
                            
                            # Update lesson with video URL
                            lesson.video_url = f"/media/generated/videos/{Path(video_file).name}"
                            
                            # Generate transcript and subtitles
                            transcript_file = await self.whisper_service.generate_subtitles(
                                audio_file, "srt"
                            )
                            if transcript_file:
                                lesson.transcript_url = f"/media/generated/transcripts/{Path(transcript_file).name}"
                            
                            db.commit()
                        
                        lesson_index += 1
            
            return video_files
            
        finally:
            db.close()
    
    async def create_lesson_video(
        self,
        lesson_id: str,
        audio_file: str,
        slide_file: Optional[str] = None,
        title: str = "Lesson"
    ) -> Optional[str]:
        """Create video for a single lesson"""
        
        try:
            # Generate unique filename
            video_filename = f"lesson_{lesson_id}_{uuid.uuid4().hex[:8]}.mp4"
            video_path = self.output_dir / video_filename
            
            if slide_file and Path(slide_file).exists():
                # Create video with slides
                success = await self._create_video_with_slides(
                    audio_file, slide_file, video_path, title
                )
            else:
                # Create video with static background
                success = await self._create_video_with_static_background(
                    audio_file, video_path, title
                )
            
            if success:
                return str(video_path)
            else:
                return None
                
        except Exception as e:
            print(f"Error creating video for lesson {lesson_id}: {e}")
            return None
    
    async def _create_video_with_slides(
        self,
        audio_file: str,
        slide_file: str,
        output_path: Path,
        title: str
    ) -> bool:
        """Create video with slide presentation"""
        
        try:
            # Get audio duration
            audio_duration = await self._get_audio_duration(audio_file)
            
            if audio_duration <= 0:
                return False
            
            # Convert slides to images if needed
            slide_images = await self._convert_slides_to_images(slide_file)
            
            if not slide_images:
                return await self._create_video_with_static_background(
                    audio_file, output_path, title
                )
            
            # Calculate duration per slide
            duration_per_slide = audio_duration / len(slide_images)
            
            # Create video with slides
            ffmpeg_cmd = [
                "ffmpeg", "-y",
                "-f", "concat",
                "-safe", "0",
                "-i", await self._create_slide_list(slide_images, duration_per_slide),
                "-i", audio_file,
                "-c:v", "libx264",
                "-c:a", "aac",
                "-pix_fmt", "yuv420p",
                "-shortest",
                str(output_path)
            ]
            
            result = await asyncio.create_subprocess_exec(
                *ffmpeg_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"Error creating video with slides: {e}")
            return False
    
    async def _create_video_with_static_background(
        self,
        audio_file: str,
        output_path: Path,
        title: str
    ) -> bool:
        """Create video with static background and title"""
        
        try:
            # Get audio duration
            audio_duration = await self._get_audio_duration(audio_file)
            
            if audio_duration <= 0:
                return False
            
            # Create video with static background and title overlay
            ffmpeg_cmd = [
                "ffmpeg", "-y",
                "-f", "lavfi",
                "-i", f"color=c=blue:size=1920x1080:duration={audio_duration}",
                "-i", audio_file,
                "-vf", f"drawtext=text='{title}':fontcolor=white:fontsize=60:x=(w-text_w)/2:y=(h-text_h)/2",
                "-c:v", "libx264",
                "-c:a", "aac",
                "-pix_fmt", "yuv420p",
                "-shortest",
                str(output_path)
            ]
            
            result = await asyncio.create_subprocess_exec(
                *ffmpeg_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"Error creating video with static background: {e}")
            return False
    
    async def _get_audio_duration(self, audio_file: str) -> float:
        """Get duration of audio file in seconds"""
        
        try:
            ffprobe_cmd = [
                "ffprobe",
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                audio_file
            ]
            
            result = await asyncio.create_subprocess_exec(
                *ffprobe_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                data = json.loads(stdout.decode())
                return float(data["format"]["duration"])
            else:
                return 0.0
                
        except Exception as e:
            print(f"Error getting audio duration: {e}")
            return 0.0
    
    async def _convert_slides_to_images(self, slide_file: str) -> List[str]:
        """Convert slide file to individual images"""
        
        try:
            slide_path = Path(slide_file)
            
            if slide_path.suffix.lower() == '.pdf':
                return await self._convert_pdf_to_images(slide_file)
            else:
                # For other formats, create a single image
                return [slide_file]
                
        except Exception as e:
            print(f"Error converting slides to images: {e}")
            return []
    
    async def _convert_pdf_to_images(self, pdf_file: str) -> List[str]:
        """Convert PDF slides to individual images"""
        
        try:
            pdf_path = Path(pdf_file)
            output_pattern = self.temp_dir / f"{pdf_path.stem}_%03d.png"
            
            # Use ImageMagick to convert PDF to images
            convert_cmd = [
                "convert",
                "-density", "300",
                pdf_file,
                "-quality", "90",
                str(output_pattern)
            ]
            
            result = await asyncio.create_subprocess_exec(
                *convert_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                # Find generated images
                images = list(self.temp_dir.glob(f"{pdf_path.stem}_*.png"))
                return [str(img) for img in sorted(images)]
            else:
                return []
                
        except Exception as e:
            print(f"Error converting PDF to images: {e}")
            return []
    
    async def _create_slide_list(
        self,
        slide_images: List[str],
        duration_per_slide: float
    ) -> str:
        """Create FFmpeg concat file for slides"""
        
        try:
            list_filename = self.temp_dir / f"slides_{uuid.uuid4().hex[:8]}.txt"
            
            with open(list_filename, 'w') as f:
                for image in slide_images:
                    f.write(f"file '{image}'\n")
                    f.write(f"duration {duration_per_slide}\n")
                
                # Add last image again for proper ending
                if slide_images:
                    f.write(f"file '{slide_images[-1]}'\n")
            
            return str(list_filename)
            
        except Exception as e:
            print(f"Error creating slide list: {e}")
            return ""
    
    async def add_subtitles_to_video(
        self,
        video_file: str,
        subtitle_file: str
    ) -> Optional[str]:
        """Add subtitles to video"""
        
        try:
            video_path = Path(video_file)
            output_filename = f"{video_path.stem}_with_subs{video_path.suffix}"
            output_path = self.output_dir / output_filename
            
            ffmpeg_cmd = [
                "ffmpeg", "-y",
                "-i", video_file,
                "-i", subtitle_file,
                "-c", "copy",
                "-c:s", "mov_text",
                str(output_path)
            ]
            
            result = await asyncio.create_subprocess_exec(
                *ffmpeg_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                return str(output_path)
            else:
                return None
                
        except Exception as e:
            print(f"Error adding subtitles: {e}")
            return None
    
    async def create_course_trailer(
        self,
        course_id: str,
        video_files: List[str],
        max_duration: int = 60
    ) -> Optional[str]:
        """Create a trailer video from course content"""
        
        try:
            if not video_files:
                return None
            
            trailer_filename = f"course_{course_id}_trailer_{uuid.uuid4().hex[:8]}.mp4"
            trailer_path = self.output_dir / trailer_filename
            
            # Extract short clips from each video
            clip_duration = min(5, max_duration // len(video_files))
            
            # Create concat file for trailer
            concat_file = self.temp_dir / f"trailer_{uuid.uuid4().hex[:8]}.txt"
            
            with open(concat_file, 'w') as f:
                for video_file in video_files[:max_duration//clip_duration]:
                    f.write(f"file '{video_file}'\n")
                    f.write(f"inpoint 0\n")
                    f.write(f"outpoint {clip_duration}\n")
            
            # Create trailer
            ffmpeg_cmd = [
                "ffmpeg", "-y",
                "-f", "concat",
                "-safe", "0",
                "-i", str(concat_file),
                "-c", "copy",
                str(trailer_path)
            ]
            
            result = await asyncio.create_subprocess_exec(
                *ffmpeg_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                return str(trailer_path)
            else:
                return None
                
        except Exception as e:
            print(f"Error creating trailer: {e}")
            return None
