import React, { useState } from 'react'
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Switch,
  FormControlLabel,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Avatar,
  Rating,
  Divider,
  Stack,
  useTheme,
  useMediaQuery,
} from '@mui/material'
import {
  CheckCircle,
  Close,
  Star,
  TrendingUp,
  Security,
  Support,
  Rocket,
  Business,
  School,
  AutoAwesome,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'

import { useAppSelector } from '../hooks/redux'
import { selectIsAuthenticated } from '../store/slices/authSlice'

interface PricingPlan {
  id: string
  name: string
  price: {
    monthly: number
    yearly: number
  }
  description: string
  features: string[]
  limitations: string[]
  recommended?: boolean
  icon: React.ReactElement
  color: string
  buttonText: string
  trialDays: number
}

const PricingPage: React.FC = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const navigate = useNavigate()
  const isAuthenticated = useAppSelector(selectIsAuthenticated)
  const [isYearly, setIsYearly] = useState(false)

  const plans: PricingPlan[] = [
    {
      id: 'free',
      name: 'FREE',
      price: { monthly: 0, yearly: 0 },
      description: 'Perfect for trying out our AI course creation',
      features: [
        'Smart course creation only',
        '5-minute course maximum',
        '1 course per month',
        'Basic templates',
        'Community support',
        'Standard quality export',
      ],
      limitations: [
        'No avatar instructors',
        'Limited customization',
        'Watermarked videos',
      ],
      icon: <School />,
      color: '#9E9E9E',
      buttonText: 'Get Started Free',
      trialDays: 0,
    },
    {
      id: 'pro',
      name: 'PRO',
      price: { monthly: 49, yearly: 39 },
      description: 'Ideal for educators and content creators',
      features: [
        '30-minute avatar courses',
        '10 smart courses per month',
        'Premium templates',
        'Priority support',
        'HD video export',
        'Custom branding',
        'Analytics dashboard',
        'Multi-language TTS',
      ],
      limitations: [],
      recommended: true,
      icon: <AutoAwesome />,
      color: '#667eea',
      buttonText: 'Start Pro Trial',
      trialDays: 3,
    },
    {
      id: 'creator',
      name: 'CREATOR',
      price: { monthly: 99, yearly: 79 },
      description: 'For professional course creators and trainers',
      features: [
        '3 avatar courses per month',
        'Unlimited smart courses',
        'Advanced templates',
        'Custom branding',
        'Advanced analytics',
        '4K video export',
        'API access',
        'Team collaboration',
        'Custom voice training',
      ],
      limitations: [],
      icon: <TrendingUp />,
      color: '#4CAF50',
      buttonText: 'Start Creator Trial',
      trialDays: 3,
    },
    {
      id: 'enterprise',
      name: 'ENTERPRISE',
      price: { monthly: 289, yearly: 231 },
      description: 'Complete solution for organizations',
      features: [
        'Unlimited avatar courses',
        'Unlimited smart courses',
        'White-label solution',
        'Dedicated support',
        'Custom integrations',
        'Advanced security',
        'SLA guarantee',
        'Training & onboarding',
        'Custom AI models',
      ],
      limitations: [],
      icon: <Business />,
      color: '#FF9800',
      buttonText: 'Contact Sales',
      trialDays: 7,
    },
  ]

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Online Educator',
      avatar: '/avatars/sarah.jpg',
      rating: 5,
      comment: 'Koursia transformed how I create courses. What used to take weeks now takes hours!',
    },
    {
      name: 'Michael Chen',
      role: 'Corporate Trainer',
      avatar: '/avatars/michael.jpg',
      rating: 5,
      comment: 'The AI avatars are incredibly realistic. Our employees love the engaging content.',
    },
    {
      name: 'Emily Rodriguez',
      role: 'Course Creator',
      avatar: '/avatars/emily.jpg',
      rating: 5,
      comment: 'The ROI is amazing. I can create 10x more courses with the same effort.',
    },
  ]

  const handlePlanSelect = (planId: string) => {
    if (planId === 'free') {
      if (isAuthenticated) {
        navigate('/dashboard')
      } else {
        navigate('/register')
      }
    } else if (planId === 'enterprise') {
      // Contact sales logic
      window.open('mailto:<EMAIL>?subject=Enterprise Plan Inquiry', '_blank')
    } else {
      // Start trial logic
      navigate('/checkout', { state: { planId, isYearly } })
    }
  }

  const getPrice = (plan: PricingPlan) => {
    return isYearly ? plan.price.yearly : plan.price.monthly
  }

  const getSavings = (plan: PricingPlan) => {
    if (plan.price.monthly === 0) return 0
    const yearlyTotal = plan.price.yearly * 12
    const monthlyTotal = plan.price.monthly * 12
    return Math.round(((monthlyTotal - yearlyTotal) / monthlyTotal) * 100)
  }

  return (
    <Box sx={{ py: 8 }}>
      <Container maxWidth="lg">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Box textAlign="center" sx={{ mb: 8 }}>
            <Typography 
              variant="h2" 
              component="h1" 
              gutterBottom
              sx={{ fontWeight: 'bold', mb: 2 }}
            >
              Choose Your Plan
            </Typography>
            <Typography 
              variant="h6" 
              color="text.secondary" 
              paragraph
              sx={{ maxWidth: 600, mx: 'auto', mb: 4 }}
            >
              Start with a free trial and scale as you grow. All plans include our core AI features.
            </Typography>

            {/* Billing Toggle */}
            <Paper
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                p: 1,
                borderRadius: 3,
                bgcolor: 'grey.100',
              }}
            >
              <Typography sx={{ mx: 2, fontWeight: isYearly ? 'normal' : 'bold' }}>
                Monthly
              </Typography>
              <Switch
                checked={isYearly}
                onChange={(e) => setIsYearly(e.target.checked)}
                color="primary"
              />
              <Typography sx={{ mx: 2, fontWeight: isYearly ? 'bold' : 'normal' }}>
                Yearly
              </Typography>
              <Chip
                label="Save 20%"
                color="primary"
                size="small"
                sx={{ ml: 1 }}
              />
            </Paper>
          </Box>
        </motion.div>

        {/* Pricing Cards */}
        <Grid container spacing={4} sx={{ mb: 8 }}>
          {plans.map((plan, index) => (
            <Grid item xs={12} sm={6} lg={3} key={plan.id}>
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    border: plan.recommended ? `2px solid ${plan.color}` : '1px solid',
                    borderColor: plan.recommended ? plan.color : 'divider',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: theme.shadows[20],
                    },
                  }}
                >
                  {plan.recommended && (
                    <Chip
                      label="RECOMMENDED"
                      color="primary"
                      sx={{
                        position: 'absolute',
                        top: -12,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        fontWeight: 'bold',
                        bgcolor: plan.color,
                      }}
                    />
                  )}

                  <CardContent sx={{ flexGrow: 1, p: 3 }}>
                    {/* Plan Header */}
                    <Box textAlign="center" sx={{ mb: 3 }}>
                      <Avatar
                        sx={{
                          bgcolor: `${plan.color}15`,
                          color: plan.color,
                          width: 60,
                          height: 60,
                          mx: 'auto',
                          mb: 2,
                        }}
                      >
                        {plan.icon}
                      </Avatar>
                      <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                        {plan.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {plan.description}
                      </Typography>
                    </Box>

                    {/* Pricing */}
                    <Box textAlign="center" sx={{ mb: 3 }}>
                      <Typography variant="h3" sx={{ fontWeight: 'bold', color: plan.color }}>
                        ${getPrice(plan)}
                        <Typography component="span" variant="h6" color="text.secondary">
                          {plan.price.monthly === 0 ? '' : '/month'}
                        </Typography>
                      </Typography>
                      {isYearly && plan.price.monthly > 0 && (
                        <Typography variant="body2" color="text.secondary">
                          ${plan.price.monthly}/month billed monthly
                        </Typography>
                      )}
                      {plan.trialDays > 0 && (
                        <Chip
                          label={`${plan.trialDays}-day free trial`}
                          size="small"
                          color="success"
                          sx={{ mt: 1 }}
                        />
                      )}
                    </Box>

                    {/* Features */}
                    <List dense>
                      {plan.features.map((feature, idx) => (
                        <ListItem key={idx} sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <CheckCircle sx={{ color: 'success.main', fontSize: 20 }} />
                          </ListItemIcon>
                          <ListItemText 
                            primary={feature}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                      {plan.limitations.map((limitation, idx) => (
                        <ListItem key={idx} sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <Close sx={{ color: 'error.main', fontSize: 20 }} />
                          </ListItemIcon>
                          <ListItemText 
                            primary={limitation}
                            primaryTypographyProps={{ 
                              variant: 'body2',
                              color: 'text.secondary',
                            }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </CardContent>

                  <CardActions sx={{ p: 3, pt: 0 }}>
                    <Button
                      fullWidth
                      variant={plan.recommended ? 'contained' : 'outlined'}
                      size="large"
                      onClick={() => handlePlanSelect(plan.id)}
                      sx={{
                        py: 1.5,
                        fontWeight: 'bold',
                        ...(plan.recommended && {
                          bgcolor: plan.color,
                          '&:hover': {
                            bgcolor: plan.color,
                            opacity: 0.9,
                          },
                        }),
                      }}
                    >
                      {plan.buttonText}
                    </Button>
                  </CardActions>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Feature Comparison Table */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Typography variant="h4" textAlign="center" gutterBottom sx={{ mb: 4, fontWeight: 'bold' }}>
            Compare Features
          </Typography>
          
          <Paper sx={{ overflow: 'hidden', mb: 8 }}>
            {/* Feature comparison table would go here */}
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Detailed Feature Comparison
              </Typography>
              <Typography color="text.secondary">
                See our complete feature matrix and make an informed decision
              </Typography>
              <Button variant="outlined" sx={{ mt: 2 }}>
                View Full Comparison
              </Button>
            </Box>
          </Paper>
        </motion.div>

        {/* Testimonials */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Typography variant="h4" textAlign="center" gutterBottom sx={{ mb: 6, fontWeight: 'bold' }}>
            What Our Users Say
          </Typography>
          
          <Grid container spacing={4}>
            {testimonials.map((testimonial, index) => (
              <Grid item xs={12} md={4} key={index}>
                <Card sx={{ height: '100%', p: 3 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar src={testimonial.avatar} sx={{ mr: 2 }}>
                        {testimonial.name.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          {testimonial.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {testimonial.role}
                        </Typography>
                      </Box>
                    </Box>
                    <Rating value={testimonial.rating} readOnly sx={{ mb: 2 }} />
                    <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                      "{testimonial.comment}"
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* FAQ Section */}
        <Box sx={{ mt: 10, textAlign: 'center' }}>
          <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
            Frequently Asked Questions
          </Typography>
          <Typography variant="h6" color="text.secondary" paragraph>
            Have questions? We're here to help.
          </Typography>
          <Button variant="outlined" size="large" sx={{ mt: 2 }}>
            View FAQ
          </Button>
        </Box>
      </Container>
    </Box>
  )
}

export default PricingPage
