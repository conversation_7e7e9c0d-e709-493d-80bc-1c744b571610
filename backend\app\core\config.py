"""
Application configuration settings
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "Koursia"
    APP_VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # Security
    JWT_SECRET_KEY: str = "your-super-secret-jwt-key-change-this-in-production"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Database
    DATABASE_URL: str = "postgresql://koursia_user:koursia_password@localhost:5432/koursia"
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379"
    
    # CORS
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # AWS S3
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_S3_BUCKET: Optional[str] = None
    AWS_REGION: str = "us-east-1"
    
    # AI Services
    GEMINI_API_KEY: Optional[str] = None
    CHATTERBOX_API_KEY: Optional[str] = None
    KOKORO_API_KEY: Optional[str] = None
    OPENAI_API_KEY: Optional[str] = None
    
    # Stock Media APIs
    PEXELS_API_KEY: Optional[str] = None
    PIXABAY_API_KEY: Optional[str] = None
    
    # Ecomimic V2
    ECOMIMIC_API_URL: str = "http://localhost:8003"
    ECOMIMIC_API_KEY: Optional[str] = None
    
    # File Upload
    MAX_FILE_SIZE_MB: int = 100
    ALLOWED_FILE_TYPES: List[str] = [
        "mp4", "mp3", "wav", "jpg", "jpeg", "png", "gif", "pdf", "pptx"
    ]
    
    # Video Processing
    FFMPEG_THREADS: int = 4
    VIDEO_QUALITY: str = "high"
    DEFAULT_VIDEO_FORMAT: str = "mp4"
    DEFAULT_AUDIO_FORMAT: str = "mp3"
    
    # TTS Configuration
    DEFAULT_TTS_PROVIDER: str = "chatterbox"
    DEFAULT_VOICE: str = "en-US-AriaNeural"
    SPEECH_RATE: float = 1.0
    SPEECH_PITCH: int = 0
    
    # Course Generation
    DEFAULT_COURSE_DURATION_MINUTES: int = 30
    MAX_MODULES_PER_COURSE: int = 10
    MAX_LESSONS_PER_MODULE: int = 20
    
    # Monitoring
    LOG_LEVEL: str = "INFO"
    SENTRY_DSN: Optional[str] = None
    
    # Email
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    FROM_EMAIL: str = "<EMAIL>"
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_PER_HOUR: int = 1000
    
    # Cache
    CACHE_TTL_SECONDS: int = 3600
    CACHE_MAX_SIZE: int = 1000
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()


# Validate required settings
def validate_settings():
    """Validate that required settings are present"""
    required_for_production = [
        "JWT_SECRET_KEY",
        "DATABASE_URL",
        "GEMINI_API_KEY"
    ]
    
    if settings.ENVIRONMENT == "production":
        missing = []
        for setting in required_for_production:
            if not getattr(settings, setting) or getattr(settings, setting) == "your-super-secret-jwt-key-change-this-in-production":
                missing.append(setting)
        
        if missing:
            raise ValueError(f"Missing required production settings: {', '.join(missing)}")


# Validate settings on import
if settings.ENVIRONMENT == "production":
    validate_settings()
