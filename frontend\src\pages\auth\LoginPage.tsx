import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material'
import { useForm } from 'react-hook-form'
import { toast } from 'react-toastify'

import { useAppDispatch, useAppSelector } from '../../hooks/redux'
import { loginStart, loginSuccess, loginFailure, selectAuthLoading, selectAuthError } from '../../store/slices/authSlice'

interface LoginFormData {
  email_or_username: string
  password: string
}

const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const isLoading = useAppSelector(selectAuthLoading)
  const error = useAppSelector(selectAuthError)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>()

  const onSubmit = async (data: LoginFormData) => {
    dispatch(loginStart())

    try {
      const response = await fetch('/api/v1/auth/login/json', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        const result = await response.json()
        
        // Get user info
        const userResponse = await fetch('/api/v1/auth/me', {
          headers: {
            'Authorization': `Bearer ${result.access_token}`,
          },
        })

        if (userResponse.ok) {
          const user = await userResponse.json()
          dispatch(loginSuccess({ user, token: result.access_token }))
          toast.success('Login successful!')
          navigate('/dashboard')
        } else {
          throw new Error('Failed to get user information')
        }
      } else {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Login failed')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed'
      dispatch(loginFailure(errorMessage))
      toast.error(errorMessage)
    }
  }

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 1 }}>
      <Typography variant="h5" component="h2" gutterBottom>
        Sign In
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TextField
        margin="normal"
        required
        fullWidth
        id="email_or_username"
        label="Email or Username"
        autoComplete="email"
        autoFocus
        error={!!errors.email_or_username}
        helperText={errors.email_or_username?.message}
        {...register('email_or_username', {
          required: 'Email or username is required',
        })}
      />
      
      <TextField
        margin="normal"
        required
        fullWidth
        label="Password"
        type="password"
        id="password"
        autoComplete="current-password"
        error={!!errors.password}
        helperText={errors.password?.message}
        {...register('password', {
          required: 'Password is required',
        })}
      />

      <Button
        type="submit"
        fullWidth
        variant="contained"
        sx={{ mt: 3, mb: 2 }}
        disabled={isLoading}
      >
        {isLoading ? <CircularProgress size={24} /> : 'Sign In'}
      </Button>

      <Divider sx={{ my: 2 }} />

      <Box textAlign="center">
        <Typography variant="body2">
          Don't have an account?{' '}
          <Link to="/register" style={{ textDecoration: 'none', color: 'inherit' }}>
            <Typography component="span" color="primary" sx={{ fontWeight: 'medium' }}>
              Sign up here
            </Typography>
          </Link>
        </Typography>
      </Box>
    </Box>
  )
}

export default LoginPage
