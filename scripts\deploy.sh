#!/bin/bash

# Koursia Deployment Script
# This script handles the deployment of the Koursia platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-development}
PROJECT_NAME="koursia"
BACKUP_DIR="./backups"

echo -e "${BLUE}🚀 Starting Koursia deployment for ${ENVIRONMENT} environment${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_status "Docker is running"
}

# Check if required files exist
check_files() {
    local required_files=(
        "docker-compose.yml"
        ".env"
        "backend/Dockerfile"
        "frontend/Dockerfile"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            print_error "Required file $file not found"
            exit 1
        fi
    done
    print_status "All required files found"
}

# Load environment variables
load_env() {
    if [[ -f ".env" ]]; then
        export $(cat .env | grep -v '^#' | xargs)
        print_status "Environment variables loaded"
    else
        print_warning "No .env file found. Using default values."
    fi
}

# Create necessary directories
create_directories() {
    local dirs=(
        "media/uploads"
        "media/generated"
        "media/temp"
        "logs"
        "backups"
        "nginx/ssl"
    )
    
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
    done
    print_status "Directories created"
}

# Backup database (production only)
backup_database() {
    if [[ "$ENVIRONMENT" == "production" ]]; then
        print_status "Creating database backup..."
        
        # Create backup directory with timestamp
        local backup_timestamp=$(date +"%Y%m%d_%H%M%S")
        local backup_file="${BACKUP_DIR}/koursia_backup_${backup_timestamp}.sql"
        
        mkdir -p "$BACKUP_DIR"
        
        # Create database backup
        docker-compose exec -T postgres pg_dump -U "${POSTGRES_USER:-koursia_user}" "${POSTGRES_DB:-koursia}" > "$backup_file"
        
        if [[ $? -eq 0 ]]; then
            print_status "Database backup created: $backup_file"
        else
            print_error "Database backup failed"
            exit 1
        fi
        
        # Keep only last 7 backups
        find "$BACKUP_DIR" -name "koursia_backup_*.sql" -type f -mtime +7 -delete
    fi
}

# Build and start services
deploy_services() {
    print_status "Building and starting services..."
    
    # Pull latest images
    docker-compose pull
    
    # Build services
    docker-compose build --no-cache
    
    # Start services
    if [[ "$ENVIRONMENT" == "production" ]]; then
        docker-compose up -d
    else
        docker-compose up -d
    fi
    
    print_status "Services started"
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    # Wait for database to be ready
    sleep 10
    
    # Run migrations
    docker-compose exec backend alembic upgrade head
    
    if [[ $? -eq 0 ]]; then
        print_status "Database migrations completed"
    else
        print_error "Database migrations failed"
        exit 1
    fi
}

# Health check
health_check() {
    print_status "Performing health checks..."
    
    local services=("backend" "frontend" "postgres" "redis")
    local max_attempts=30
    local attempt=1
    
    for service in "${services[@]}"; do
        print_status "Checking $service..."
        
        while [[ $attempt -le $max_attempts ]]; do
            if docker-compose ps "$service" | grep -q "Up"; then
                print_status "$service is healthy"
                break
            fi
            
            if [[ $attempt -eq $max_attempts ]]; then
                print_error "$service failed to start"
                docker-compose logs "$service"
                exit 1
            fi
            
            sleep 2
            ((attempt++))
        done
        
        attempt=1
    done
    
    # Test API endpoint
    sleep 5
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        print_status "API health check passed"
    else
        print_warning "API health check failed - service may still be starting"
    fi
}

# Setup SSL certificates (production only)
setup_ssl() {
    if [[ "$ENVIRONMENT" == "production" ]]; then
        print_status "Setting up SSL certificates..."
        
        if [[ ! -f "nginx/ssl/cert.pem" ]] || [[ ! -f "nginx/ssl/key.pem" ]]; then
            print_warning "SSL certificates not found. Please add your SSL certificates to nginx/ssl/"
            print_warning "For Let's Encrypt, you can use: certbot --nginx -d your-domain.com"
        else
            print_status "SSL certificates found"
        fi
    fi
}

# Cleanup old containers and images
cleanup() {
    print_status "Cleaning up old containers and images..."
    
    # Remove stopped containers
    docker container prune -f
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes (be careful in production)
    if [[ "$ENVIRONMENT" != "production" ]]; then
        docker volume prune -f
    fi
    
    print_status "Cleanup completed"
}

# Show deployment summary
show_summary() {
    echo -e "\n${GREEN}🎉 Deployment completed successfully!${NC}\n"
    
    echo -e "${BLUE}Service URLs:${NC}"
    echo -e "  Frontend: http://localhost:3000"
    echo -e "  Backend API: http://localhost:8000"
    echo -e "  API Docs: http://localhost:8000/docs"
    echo -e "  AI Services: http://localhost:8001"
    echo -e "  Media Processing: http://localhost:8002"
    
    echo -e "\n${BLUE}Useful Commands:${NC}"
    echo -e "  View logs: docker-compose logs -f [service_name]"
    echo -e "  Stop services: docker-compose down"
    echo -e "  Restart service: docker-compose restart [service_name]"
    echo -e "  Shell access: docker-compose exec [service_name] /bin/bash"
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        echo -e "\n${YELLOW}Production Notes:${NC}"
        echo -e "  - Monitor logs regularly"
        echo -e "  - Set up automated backups"
        echo -e "  - Configure SSL certificates"
        echo -e "  - Set up monitoring and alerting"
    fi
}

# Main deployment flow
main() {
    echo -e "${BLUE}Starting deployment process...${NC}\n"
    
    check_docker
    check_files
    load_env
    create_directories
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        backup_database
    fi
    
    deploy_services
    run_migrations
    health_check
    setup_ssl
    cleanup
    show_summary
}

# Handle script arguments
case "$1" in
    "production"|"staging"|"development"|"")
        main
        ;;
    "backup")
        backup_database
        ;;
    "cleanup")
        cleanup
        ;;
    "health")
        health_check
        ;;
    *)
        echo "Usage: $0 [environment|backup|cleanup|health]"
        echo "  environment: production, staging, development (default)"
        echo "  backup: Create database backup"
        echo "  cleanup: Clean up Docker resources"
        echo "  health: Run health checks"
        exit 1
        ;;
esac
