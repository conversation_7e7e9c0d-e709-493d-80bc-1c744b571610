"""
Authentication endpoint tests
"""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session

from app.models.user import User


class TestAuthEndpoints:
    """Test authentication endpoints"""
    
    def test_register_user_success(self, client: TestClient, db_session: Session):
        """Test successful user registration"""
        user_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "full_name": "Test User",
            "password": "testpassword123"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 201
        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["username"] == user_data["username"]
        assert data["full_name"] == user_data["full_name"]
        assert "id" in data
        assert "hashed_password" not in data
        
        # Verify user was created in database
        user = db_session.query(User).filter(User.email == user_data["email"]).first()
        assert user is not None
        assert user.email == user_data["email"]
    
    def test_register_user_duplicate_email(self, client: TestClient, test_user: User):
        """Test registration with duplicate email"""
        user_data = {
            "email": test_user.email,
            "username": "newuser",
            "full_name": "New User",
            "password": "testpassword123"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "Email already registered" in response.json()["detail"]
    
    def test_register_user_duplicate_username(self, client: TestClient, test_user: User):
        """Test registration with duplicate username"""
        user_data = {
            "email": "<EMAIL>",
            "username": test_user.username,
            "full_name": "New User",
            "password": "testpassword123"
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 400
        assert "Username already taken" in response.json()["detail"]
    
    def test_register_user_invalid_data(self, client: TestClient):
        """Test registration with invalid data"""
        user_data = {
            "email": "invalid-email",
            "username": "ab",  # Too short
            "full_name": "",   # Empty
            "password": "123"  # Too short
        }
        
        response = client.post("/api/v1/auth/register", json=user_data)
        
        assert response.status_code == 422
    
    def test_login_success(self, client: TestClient, test_user: User):
        """Test successful login"""
        login_data = {
            "email_or_username": test_user.email,
            "password": "secret"
        }
        
        response = client.post("/api/v1/auth/login/json", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data
    
    def test_login_with_username(self, client: TestClient, test_user: User):
        """Test login with username instead of email"""
        login_data = {
            "email_or_username": test_user.username,
            "password": "secret"
        }
        
        response = client.post("/api/v1/auth/login/json", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
    
    def test_login_invalid_credentials(self, client: TestClient, test_user: User):
        """Test login with invalid credentials"""
        login_data = {
            "email_or_username": test_user.email,
            "password": "wrongpassword"
        }
        
        response = client.post("/api/v1/auth/login/json", json=login_data)
        
        assert response.status_code == 401
        assert "Incorrect email/username or password" in response.json()["detail"]
    
    def test_login_nonexistent_user(self, client: TestClient):
        """Test login with nonexistent user"""
        login_data = {
            "email_or_username": "<EMAIL>",
            "password": "password"
        }
        
        response = client.post("/api/v1/auth/login/json", json=login_data)
        
        assert response.status_code == 401
    
    def test_get_current_user(self, client: TestClient, auth_headers: dict, test_user: User):
        """Test getting current user information"""
        response = client.get("/api/v1/auth/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(test_user.id)
        assert data["email"] == test_user.email
        assert data["username"] == test_user.username
        assert "hashed_password" not in data
    
    def test_get_current_user_unauthorized(self, client: TestClient):
        """Test getting current user without authentication"""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401
    
    def test_get_current_user_invalid_token(self, client: TestClient):
        """Test getting current user with invalid token"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 401
    
    def test_refresh_token(self, client: TestClient, auth_headers: dict):
        """Test token refresh"""
        response = client.post("/api/v1/auth/refresh", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data
    
    def test_oauth2_login(self, client: TestClient, test_user: User):
        """Test OAuth2 compatible login"""
        form_data = {
            "username": test_user.email,
            "password": "secret"
        }
        
        response = client.post("/api/v1/auth/login", data=form_data)
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
