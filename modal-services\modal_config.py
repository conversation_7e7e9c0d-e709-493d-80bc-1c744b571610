"""
Modal configuration for Koursia AI services
"""

import modal
import os
from pathlib import Path

# Modal app configuration
app = modal.App("koursia-ai-services")

# GPU configuration for A100 80GB
GPU_CONFIG = modal.gpu.A100(count=1, memory=80)

# Base image with CUDA and Python dependencies
base_image = (
    modal.Image.from_registry("nvidia/cuda:11.8-devel-ubuntu20.04")
    .apt_install([
        "git",
        "wget",
        "curl",
        "ffmpeg",
        "libsndfile1",
        "build-essential",
        "python3",
        "python3-pip",
        "python3-dev",
    ])
    .pip_install([
        "torch==2.1.1",
        "torchvision==0.16.1",
        "torchaudio==2.1.1",
        "transformers==4.36.0",
        "accelerate==0.24.1",
        "openai-whisper==20231117",
        "google-generativeai==0.3.2",
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "httpx==0.25.2",
        "aiohttp==3.9.1",
        "aiofiles==23.2.1",
        "librosa==0.10.1",
        "soundfile==0.12.1",
        "pydub==0.25.1",
        "numpy==1.24.3",
        "pillow==10.1.0",
        "requests==2.31.0",
        "python-dotenv==1.0.0",
        "pydantic==2.5.0",
    ])
    .env({
        "CUDA_VISIBLE_DEVICES": "0",
        "NVIDIA_VISIBLE_DEVICES": "all",
        "NVIDIA_DRIVER_CAPABILITIES": "compute,utility",
    })
)

# Whisper image with pre-downloaded models
whisper_image = (
    base_image
    .run_commands([
        "python3 -c 'import whisper; whisper.load_model(\"base\")'",
        "python3 -c 'import whisper; whisper.load_model(\"small\")'",
        "python3 -c 'import whisper; whisper.load_model(\"medium\")'",
    ])
)

# Ecomimic image (placeholder - would need actual Ecomimic setup)
ecomimic_image = (
    base_image
    .apt_install([
        "nodejs",
        "npm",
        "imagemagick",
    ])
    .pip_install([
        "moviepy==1.0.3",
        "opencv-python==********",
        "scikit-image==0.22.0",
    ])
)

# TTS image
tts_image = (
    base_image
    .pip_install([
        "TTS==0.20.6",
        "espeak-ng",
        "phonemizer==3.2.1",
    ])
    .apt_install([
        "espeak",
        "espeak-data",
        "libespeak1",
        "libespeak-dev",
    ])
)

# Shared volumes for model storage and temporary files
models_volume = modal.Volume.from_name("koursia-models", create_if_missing=True)
temp_volume = modal.Volume.from_name("koursia-temp", create_if_missing=True)

# Environment variables
ENVIRONMENT_VARS = {
    "GEMINI_API_KEY": modal.Secret.from_name("gemini-api-key"),
    "OPENAI_API_KEY": modal.Secret.from_name("openai-api-key"),
    "CHATTERBOX_API_KEY": modal.Secret.from_name("chatterbox-api-key"),
    "KOKORO_API_KEY": modal.Secret.from_name("kokoro-api-key"),
    "ECOMIMIC_API_KEY": modal.Secret.from_name("ecomimic-api-key"),
}

# Common function decorator
def gpu_function(
    image=base_image,
    gpu=GPU_CONFIG,
    timeout=1800,  # 30 minutes
    retries=2,
    volumes=None,
    secrets=None,
):
    """Common decorator for GPU functions"""
    if volumes is None:
        volumes = {
            "/models": models_volume,
            "/tmp": temp_volume,
        }
    
    if secrets is None:
        secrets = list(ENVIRONMENT_VARS.values())
    
    return modal.Function.from_name(
        app,
        image=image,
        gpu=gpu,
        timeout=timeout,
        retries=retries,
        volumes=volumes,
        secrets=secrets,
        keep_warm=1,  # Keep one instance warm
    )

# Webhook configuration for external API access
webhook_config = {
    "method": "POST",
    "wait_for_response": True,
    "timeout": 1800,
}

# Auto-scaling configuration
SCALING_CONFIG = {
    "min_instances": 0,
    "max_instances": 10,
    "scale_down_delay": 300,  # 5 minutes
    "target_concurrency": 1,
}

# Monitoring and logging configuration
MONITORING_CONFIG = {
    "log_level": "INFO",
    "metrics_enabled": True,
    "health_check_interval": 60,
    "performance_tracking": True,
}

# Cost optimization settings
COST_OPTIMIZATION = {
    "preemptible": True,  # Use preemptible instances when possible
    "auto_shutdown": True,  # Auto-shutdown idle instances
    "resource_limits": {
        "cpu": "8",
        "memory": "32Gi",
        "gpu_memory": "80Gi",
    },
}

# Security configuration
SECURITY_CONFIG = {
    "network_isolation": True,
    "encrypted_volumes": True,
    "secure_secrets": True,
    "audit_logging": True,
}

# Model configurations
MODEL_CONFIGS = {
    "whisper": {
        "models": ["base", "small", "medium", "large"],
        "default_model": "base",
        "batch_size": 1,
        "chunk_length": 30,
    },
    "tts": {
        "providers": ["chatterbox", "kokoro", "local"],
        "default_provider": "chatterbox",
        "voice_cache": True,
        "quality": "high",
    },
    "ecomimic": {
        "avatar_styles": ["professional", "casual", "formal"],
        "video_quality": "1080p",
        "fps": 30,
        "background_removal": True,
    },
    "gemini": {
        "model": "gemini-pro",
        "temperature": 0.7,
        "max_tokens": 4096,
        "safety_settings": "default",
    },
}

# API rate limiting
RATE_LIMITS = {
    "whisper": "10/minute",
    "tts": "20/minute",
    "ecomimic": "5/minute",
    "gemini": "30/minute",
}

# Health check endpoints
HEALTH_CHECKS = {
    "whisper": "/health/whisper",
    "tts": "/health/tts",
    "ecomimic": "/health/ecomimic",
    "gemini": "/health/gemini",
}

# Fallback configurations
FALLBACK_CONFIG = {
    "whisper": {
        "local_model": True,
        "cpu_fallback": True,
    },
    "tts": {
        "local_tts": True,
        "cache_fallback": True,
    },
    "ecomimic": {
        "static_avatar": True,
        "image_fallback": True,
    },
}

# Performance optimization
PERFORMANCE_CONFIG = {
    "model_caching": True,
    "result_caching": True,
    "batch_processing": True,
    "async_processing": True,
    "memory_optimization": True,
}

# Export configurations
__all__ = [
    "app",
    "GPU_CONFIG",
    "base_image",
    "whisper_image",
    "ecomimic_image",
    "tts_image",
    "models_volume",
    "temp_volume",
    "ENVIRONMENT_VARS",
    "gpu_function",
    "webhook_config",
    "SCALING_CONFIG",
    "MONITORING_CONFIG",
    "COST_OPTIMIZATION",
    "SECURITY_CONFIG",
    "MODEL_CONFIGS",
    "RATE_LIMITS",
    "HEALTH_CHECKS",
    "FALLBACK_CONFIG",
    "PERFORMANCE_CONFIG",
]
