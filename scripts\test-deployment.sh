#!/bin/bash

# Comprehensive deployment test script for Koursia platform
# Tests all components including Modal GPU services integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL=${BASE_URL:-"http://localhost"}
FRONTEND_PORT=${FRONTEND_PORT:-3000}
BACKEND_PORT=${BACKEND_PORT:-8000}
AI_SERVICES_PORT=${AI_SERVICES_PORT:-8001}
MEDIA_PROCESSOR_PORT=${MEDIA_PROCESSOR_PORT:-8002}

echo -e "${BLUE}🧪 Starting comprehensive deployment tests for Koursia platform${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test service health
test_service_health() {
    local service_name=$1
    local url=$2
    local timeout=${3:-10}
    
    print_info "Testing $service_name health at $url"
    
    if curl -f -s --max-time $timeout "$url" > /dev/null; then
        print_status "$service_name is healthy"
        return 0
    else
        print_error "$service_name health check failed"
        return 1
    fi
}

# Test API endpoint
test_api_endpoint() {
    local endpoint=$1
    local expected_status=${2:-200}
    local method=${3:-GET}
    local data=${4:-""}
    
    print_info "Testing API endpoint: $method $endpoint"
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X POST \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$endpoint")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" "$endpoint")
    fi
    
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        print_status "API endpoint $endpoint returned expected status $expected_status"
        return 0
    else
        print_error "API endpoint $endpoint returned status $status_code, expected $expected_status"
        return 1
    fi
}

# Test frontend responsiveness
test_frontend_responsiveness() {
    print_info "Testing frontend responsiveness"
    
    # Test different viewport sizes
    local viewports=("1920x1080" "1024x768" "768x1024" "375x667")
    
    for viewport in "${viewports[@]}"; do
        print_info "Testing viewport: $viewport"
        # This would typically use a headless browser like Puppeteer
        # For now, we'll just test that the frontend is accessible
        if curl -f -s "$BASE_URL:$FRONTEND_PORT" > /dev/null; then
            print_status "Frontend accessible for viewport $viewport"
        else
            print_error "Frontend not accessible for viewport $viewport"
            return 1
        fi
    done
    
    return 0
}

# Test authentication flow
test_authentication() {
    print_info "Testing authentication flow"
    
    local backend_url="$BASE_URL:$BACKEND_PORT"
    
    # Test registration
    local register_data='{
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "Test User",
        "password": "testpassword123"
    }'
    
    if test_api_endpoint "$backend_url/api/v1/auth/register" 200 "POST" "$register_data"; then
        print_status "User registration test passed"
    else
        print_warning "User registration test failed (user might already exist)"
    fi
    
    # Test login
    local login_data='{
        "email_or_username": "<EMAIL>",
        "password": "testpassword123"
    }'
    
    if test_api_endpoint "$backend_url/api/v1/auth/login/json" 200 "POST" "$login_data"; then
        print_status "User login test passed"
    else
        print_error "User login test failed"
        return 1
    fi
    
    return 0
}

# Test pricing API
test_pricing_api() {
    print_info "Testing pricing API"
    
    local backend_url="$BASE_URL:$BACKEND_PORT"
    
    # Test get plans
    if test_api_endpoint "$backend_url/api/v1/pricing/plans" 200; then
        print_status "Get plans API test passed"
    else
        print_error "Get plans API test failed"
        return 1
    fi
    
    # Test pricing calculation
    local calc_url="$backend_url/api/v1/pricing/calculate?plan_id=test&billing_cycle=monthly"
    if test_api_endpoint "$calc_url" 200; then
        print_status "Pricing calculation API test passed"
    else
        print_warning "Pricing calculation API test failed (expected with test data)"
    fi
    
    return 0
}

# Test Modal GPU services
test_modal_services() {
    print_info "Testing Modal GPU services integration"
    
    # Test Whisper service
    if command -v modal &> /dev/null; then
        print_info "Testing Whisper service"
        if modal run modal-services/whisper_service.py::whisper_health 2>/dev/null; then
            print_status "Whisper service test passed"
        else
            print_warning "Whisper service test failed (Modal might not be deployed)"
        fi
        
        # Test TTS service
        print_info "Testing TTS service"
        if modal run modal-services/tts_service.py::tts_health 2>/dev/null; then
            print_status "TTS service test passed"
        else
            print_warning "TTS service test failed (Modal might not be deployed)"
        fi
        
        # Test Ecomimic service
        print_info "Testing Ecomimic service"
        if modal run modal-services/ecomimic_service.py::ecomimic_health 2>/dev/null; then
            print_status "Ecomimic service test passed"
        else
            print_warning "Ecomimic service test failed (Modal might not be deployed)"
        fi
    else
        print_warning "Modal CLI not found, skipping Modal services tests"
    fi
    
    return 0
}

# Test database connectivity
test_database() {
    print_info "Testing database connectivity"
    
    if docker-compose exec -T postgres pg_isready -U "${POSTGRES_USER:-koursia_user}" > /dev/null 2>&1; then
        print_status "Database connectivity test passed"
    else
        print_error "Database connectivity test failed"
        return 1
    fi
    
    return 0
}

# Test Redis connectivity
test_redis() {
    print_info "Testing Redis connectivity"
    
    if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_status "Redis connectivity test passed"
    else
        print_error "Redis connectivity test failed"
        return 1
    fi
    
    return 0
}

# Test file upload functionality
test_file_upload() {
    print_info "Testing file upload functionality"
    
    local backend_url="$BASE_URL:$BACKEND_PORT"
    
    # Create a test file
    echo "Test content" > /tmp/test_upload.txt
    
    # Test file upload (this would need authentication in real scenario)
    if curl -f -s -X POST \
        -F "file=@/tmp/test_upload.txt" \
        "$backend_url/api/v1/media/upload" > /dev/null 2>&1; then
        print_status "File upload test passed"
    else
        print_warning "File upload test failed (authentication required)"
    fi
    
    # Clean up
    rm -f /tmp/test_upload.txt
    
    return 0
}

# Test course creation workflow
test_course_creation() {
    print_info "Testing course creation workflow"
    
    local backend_url="$BASE_URL:$BACKEND_PORT"
    
    # This would require authentication and proper test data
    # For now, just test the endpoint accessibility
    if test_api_endpoint "$backend_url/api/v1/courses/" 401; then
        print_status "Course creation endpoint accessible (authentication required)"
    else
        print_error "Course creation endpoint test failed"
        return 1
    fi
    
    return 0
}

# Test performance
test_performance() {
    print_info "Testing performance"
    
    local frontend_url="$BASE_URL:$FRONTEND_PORT"
    local backend_url="$BASE_URL:$BACKEND_PORT"
    
    # Test frontend load time
    print_info "Testing frontend load time"
    local start_time=$(date +%s%N)
    curl -f -s "$frontend_url" > /dev/null
    local end_time=$(date +%s%N)
    local load_time=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
    
    if [ $load_time -lt 3000 ]; then
        print_status "Frontend load time: ${load_time}ms (Good)"
    elif [ $load_time -lt 5000 ]; then
        print_warning "Frontend load time: ${load_time}ms (Acceptable)"
    else
        print_error "Frontend load time: ${load_time}ms (Slow)"
    fi
    
    # Test API response time
    print_info "Testing API response time"
    start_time=$(date +%s%N)
    curl -f -s "$backend_url/health" > /dev/null
    end_time=$(date +%s%N)
    local api_time=$(( (end_time - start_time) / 1000000 ))
    
    if [ $api_time -lt 200 ]; then
        print_status "API response time: ${api_time}ms (Excellent)"
    elif [ $api_time -lt 500 ]; then
        print_warning "API response time: ${api_time}ms (Good)"
    else
        print_error "API response time: ${api_time}ms (Slow)"
    fi
    
    return 0
}

# Test security headers
test_security() {
    print_info "Testing security headers"
    
    local frontend_url="$BASE_URL:$FRONTEND_PORT"
    
    # Check for security headers
    local headers=$(curl -I -s "$frontend_url" 2>/dev/null)
    
    if echo "$headers" | grep -q "X-Frame-Options"; then
        print_status "X-Frame-Options header present"
    else
        print_warning "X-Frame-Options header missing"
    fi
    
    if echo "$headers" | grep -q "X-Content-Type-Options"; then
        print_status "X-Content-Type-Options header present"
    else
        print_warning "X-Content-Type-Options header missing"
    fi
    
    if echo "$headers" | grep -q "X-XSS-Protection"; then
        print_status "X-XSS-Protection header present"
    else
        print_warning "X-XSS-Protection header missing"
    fi
    
    return 0
}

# Main test execution
main() {
    local failed_tests=0
    
    echo -e "${BLUE}Starting deployment tests...${NC}\n"
    
    # Core service health tests
    echo -e "${BLUE}=== Core Service Health Tests ===${NC}"
    test_service_health "Frontend" "$BASE_URL:$FRONTEND_PORT" || ((failed_tests++))
    test_service_health "Backend" "$BASE_URL:$BACKEND_PORT/health" || ((failed_tests++))
    test_service_health "AI Services" "$BASE_URL:$AI_SERVICES_PORT/health" || ((failed_tests++))
    test_service_health "Media Processor" "$BASE_URL:$MEDIA_PROCESSOR_PORT/health" || ((failed_tests++))
    
    echo
    
    # Infrastructure tests
    echo -e "${BLUE}=== Infrastructure Tests ===${NC}"
    test_database || ((failed_tests++))
    test_redis || ((failed_tests++))
    
    echo
    
    # API tests
    echo -e "${BLUE}=== API Tests ===${NC}"
    test_authentication || ((failed_tests++))
    test_pricing_api || ((failed_tests++))
    test_course_creation || ((failed_tests++))
    test_file_upload || ((failed_tests++))
    
    echo
    
    # Frontend tests
    echo -e "${BLUE}=== Frontend Tests ===${NC}"
    test_frontend_responsiveness || ((failed_tests++))
    
    echo
    
    # Modal services tests
    echo -e "${BLUE}=== Modal GPU Services Tests ===${NC}"
    test_modal_services || ((failed_tests++))
    
    echo
    
    # Performance tests
    echo -e "${BLUE}=== Performance Tests ===${NC}"
    test_performance || ((failed_tests++))
    
    echo
    
    # Security tests
    echo -e "${BLUE}=== Security Tests ===${NC}"
    test_security || ((failed_tests++))
    
    echo
    
    # Summary
    echo -e "${BLUE}=== Test Summary ===${NC}"
    if [ $failed_tests -eq 0 ]; then
        print_status "All tests passed! 🎉"
        echo -e "${GREEN}Deployment is ready for production${NC}"
    else
        print_warning "$failed_tests test(s) failed"
        echo -e "${YELLOW}Please review failed tests before production deployment${NC}"
    fi
    
    return $failed_tests
}

# Run main function
main "$@"
