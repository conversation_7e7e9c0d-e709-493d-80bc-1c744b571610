"""
Generation schemas
"""

from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID

from app.models.course import CourseDifficulty


class VoiceSettings(BaseModel):
    """Voice settings schema"""
    provider: str = "chatterbox"  # 'chatterbox' or 'kokoro'
    voice_id: str = "en-US-AriaNeural"
    speed: float = 1.0
    pitch: float = 0.0
    volume: float = 1.0
    
    @validator('provider')
    def validate_provider(cls, v):
        if v not in ['chatterbox', 'kokoro']:
            raise ValueError('Provider must be either "chatterbox" or "kokoro"')
        return v
    
    @validator('speed')
    def validate_speed(cls, v):
        if v < 0.5 or v > 2.0:
            raise ValueError('Speed must be between 0.5 and 2.0')
        return v


class CourseGenerationRequest(BaseModel):
    """Course generation request schema"""
    title: str
    description: str
    target_audience: str
    difficulty: CourseDifficulty
    duration_minutes: int
    language: str = "en"
    learning_objectives: List[str]
    topics: List[str]
    voice_settings: VoiceSettings
    slide_template: str = "default"
    include_quiz: bool = False
    include_exercises: bool = False
    
    @validator('duration_minutes')
    def validate_duration(cls, v):
        if v < 5 or v > 480:  # 5 minutes to 8 hours
            raise ValueError('Duration must be between 5 and 480 minutes')
        return v
    
    @validator('topics')
    def validate_topics(cls, v):
        if len(v) < 1 or len(v) > 20:
            raise ValueError('Must have between 1 and 20 topics')
        return v


class AvatarGenerationRequest(CourseGenerationRequest):
    """Avatar course generation request schema"""
    avatar_style: str = "professional"
    avatar_gender: str = "neutral"
    avatar_age: str = "adult"
    background_style: str = "office"
    
    @validator('avatar_style')
    def validate_avatar_style(cls, v):
        allowed_styles = ['professional', 'casual', 'academic', 'creative']
        if v not in allowed_styles:
            raise ValueError(f'Avatar style must be one of: {", ".join(allowed_styles)}')
        return v


class GenerationJobCreate(BaseModel):
    """Generation job creation schema"""
    job_type: str
    input_data: Dict[str, Any]
    course_id: Optional[UUID] = None


class GenerationJobUpdate(BaseModel):
    """Generation job update schema"""
    status: Optional[str] = None
    progress_percentage: Optional[float] = None
    output_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class GenerationJobResponse(BaseModel):
    """Generation job response schema"""
    id: UUID
    job_type: str
    status: str
    progress_percentage: float
    input_data: Optional[Dict[str, Any]] = None
    output_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_duration: Optional[int] = None
    user_id: UUID
    course_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class GenerationProgress(BaseModel):
    """Generation progress update schema"""
    job_id: UUID
    status: str
    progress_percentage: float
    current_step: str
    message: Optional[str] = None
    estimated_time_remaining: Optional[int] = None


class ContentGenerationResult(BaseModel):
    """Content generation result schema"""
    course_outline: Dict[str, Any]
    modules: List[Dict[str, Any]]
    scripts: List[Dict[str, Any]]
    slide_content: List[Dict[str, Any]]
    metadata: Dict[str, Any]


class MediaGenerationResult(BaseModel):
    """Media generation result schema"""
    audio_files: List[str]
    video_files: List[str]
    slide_files: List[str]
    transcript_files: List[str]
    thumbnail_files: List[str]
    metadata: Dict[str, Any]
