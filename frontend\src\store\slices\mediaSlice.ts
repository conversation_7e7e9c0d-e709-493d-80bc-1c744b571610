import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import type { RootState } from '../index'

export interface MediaAsset {
  id: string
  filename: string
  original_filename: string
  title: string
  description?: string
  file_url: string
  file_size: number
  media_type: 'image' | 'video' | 'audio' | 'document' | 'slide'
  created_at: string
}

interface MediaState {
  assets: MediaAsset[]
  isLoading: boolean
  error: string | null
}

const initialState: MediaState = {
  assets: [],
  isLoading: false,
  error: null,
}

const mediaSlice = createSlice({
  name: 'media',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setAssets: (state, action: PayloadAction<MediaAsset[]>) => {
      state.assets = action.payload
      state.isLoading = false
      state.error = null
    },
    addAsset: (state, action: PayloadAction<MediaAsset>) => {
      state.assets.unshift(action.payload)
    },
    updateAsset: (state, action: PayloadAction<MediaAsset>) => {
      const index = state.assets.findIndex(asset => asset.id === action.payload.id)
      if (index !== -1) {
        state.assets[index] = action.payload
      }
    },
    removeAsset: (state, action: PayloadAction<string>) => {
      state.assets = state.assets.filter(asset => asset.id !== action.payload)
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload
      state.isLoading = false
    },
    clearError: (state) => {
      state.error = null
    },
  },
})

export const {
  setLoading,
  setAssets,
  addAsset,
  updateAsset,
  removeAsset,
  setError,
  clearError,
} = mediaSlice.actions

// Selectors
export const selectMediaAssets = (state: RootState) => state.media.assets
export const selectMediaLoading = (state: RootState) => state.media.isLoading
export const selectMediaError = (state: RootState) => state.media.error

export default mediaSlice.reducer
