#!/bin/bash

# Modal Secrets Management Template for Koursia AI Services
# SECURITY WARNING: Replace all placeholder values with actual credentials
# Never commit actual credentials to version control

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔐 Modal Secrets Setup for Koursia AI Services${NC}"
echo -e "${RED}⚠️  SECURITY WARNING: Replace all placeholder values with actual credentials${NC}"
echo

# Function to create secret securely
create_secret() {
    local secret_name=$1
    local description=$2
    
    echo -e "${BLUE}Creating secret: ${secret_name}${NC}"
    echo -e "${YELLOW}Description: ${description}${NC}"
    echo
}

# AWS Credentials
create_secret "aws-credentials" "AWS S3 storage credentials"
echo "modal secret create aws-credentials \\"
echo "  AWS_ACCESS_KEY_ID=REPLACE_WITH_YOUR_AWS_ACCESS_KEY \\"
echo "  AWS_SECRET_ACCESS_KEY=REPLACE_WITH_YOUR_AWS_SECRET_KEY \\"
echo "  AWS_S3_BUCKET=REPLACE_WITH_YOUR_S3_BUCKET_NAME \\"
echo "  AWS_REGION=us-east-1"
echo

# OpenAI API Key
create_secret "openai-api-key" "OpenAI API for Whisper and GPT services"
echo "modal secret create openai-api-key \\"
echo "  OPENAI_API_KEY=REPLACE_WITH_YOUR_OPENAI_API_KEY"
echo

# Gemini API Key
create_secret "gemini-api-key" "Google Gemini API for content generation"
echo "modal secret create gemini-api-key \\"
echo "  GEMINI_API_KEY=REPLACE_WITH_YOUR_GEMINI_API_KEY"
echo

# Pexels API Key
create_secret "pexels-api-key" "Pexels API for stock photos"
echo "modal secret create pexels-api-key \\"
echo "  PEXELS_API_KEY=REPLACE_WITH_YOUR_PEXELS_API_KEY"
echo

# Pixabay API Key
create_secret "pixabay-api-key" "Pixabay API for stock images"
echo "modal secret create pixabay-api-key \\"
echo "  PIXABAY_API_KEY=REPLACE_WITH_YOUR_PIXABAY_API_KEY"
echo

# Chatterbox TTS API Key
create_secret "chatterbox-api-key" "Chatterbox TTS service"
echo "modal secret create chatterbox-api-key \\"
echo "  CHATTERBOX_API_KEY=REPLACE_WITH_YOUR_CHATTERBOX_API_KEY"
echo

# Kokoro TTS API Key
create_secret "kokoro-api-key" "Kokoro TTS service"
echo "modal secret create kokoro-api-key \\"
echo "  KOKORO_API_KEY=REPLACE_WITH_YOUR_KOKORO_API_KEY"
echo

# Ecomimic API Key
create_secret "ecomimic-api-key" "Ecomimic V2 avatar generation"
echo "modal secret create ecomimic-api-key \\"
echo "  ECOMIMIC_API_KEY=REPLACE_WITH_YOUR_ECOMIMIC_API_KEY"
echo

echo -e "${GREEN}📋 Instructions:${NC}"
echo "1. Copy each command above"
echo "2. Replace placeholder values with your actual credentials"
echo "3. Run each command in your terminal"
echo "4. Verify secrets with: modal secret list"
echo
echo -e "${RED}🚨 SECURITY REMINDERS:${NC}"
echo "- Never commit actual credentials to version control"
echo "- Use environment variables for local development"
echo "- Rotate credentials regularly"
echo "- Monitor usage and access logs"
echo "- Use least privilege principle"
echo

# Verification commands
echo -e "${BLUE}🔍 Verification Commands:${NC}"
echo "# List all secrets"
echo "modal secret list"
echo
echo "# Test secret access (without revealing values)"
echo "modal run --secret aws-credentials -- python -c \"import os; print('AWS configured:', bool(os.getenv('AWS_ACCESS_KEY_ID')))\""
echo "modal run --secret openai-api-key -- python -c \"import os; print('OpenAI configured:', bool(os.getenv('OPENAI_API_KEY')))\""
echo "modal run --secret gemini-api-key -- python -c \"import os; print('Gemini configured:', bool(os.getenv('GEMINI_API_KEY')))\""
echo

# Security best practices
echo -e "${YELLOW}🛡️  Security Best Practices:${NC}"
echo "1. Credential Rotation:"
echo "   - Rotate AWS keys every 90 days"
echo "   - Rotate API keys every 6 months"
echo "   - Monitor for unauthorized usage"
echo
echo "2. Access Control:"
echo "   - Use IAM roles with minimal permissions"
echo "   - Enable MFA where possible"
echo "   - Monitor access logs"
echo
echo "3. Environment Separation:"
echo "   - Use different credentials for dev/staging/prod"
echo "   - Never use production credentials in development"
echo "   - Implement proper secret management in CI/CD"
echo
echo "4. Monitoring:"
echo "   - Set up billing alerts"
echo "   - Monitor API usage patterns"
echo "   - Enable security notifications"
echo

# Environment-specific configurations
echo -e "${BLUE}🌍 Environment-Specific Setup:${NC}"
echo
echo "# Development Environment"
echo "modal secret create aws-credentials-dev \\"
echo "  AWS_ACCESS_KEY_ID=DEV_AWS_ACCESS_KEY \\"
echo "  AWS_SECRET_ACCESS_KEY=DEV_AWS_SECRET_KEY \\"
echo "  AWS_S3_BUCKET=koursia-dev-bucket \\"
echo "  AWS_REGION=us-east-1"
echo
echo "# Staging Environment"
echo "modal secret create aws-credentials-staging \\"
echo "  AWS_ACCESS_KEY_ID=STAGING_AWS_ACCESS_KEY \\"
echo "  AWS_SECRET_ACCESS_KEY=STAGING_AWS_SECRET_KEY \\"
echo "  AWS_S3_BUCKET=koursia-staging-bucket \\"
echo "  AWS_REGION=us-east-1"
echo
echo "# Production Environment"
echo "modal secret create aws-credentials-prod \\"
echo "  AWS_ACCESS_KEY_ID=PROD_AWS_ACCESS_KEY \\"
echo "  AWS_SECRET_ACCESS_KEY=PROD_AWS_SECRET_KEY \\"
echo "  AWS_S3_BUCKET=koursia-prod-bucket \\"
echo "  AWS_REGION=us-east-1"
echo

# Cleanup commands
echo -e "${RED}🗑️  Cleanup Commands (Use with caution):${NC}"
echo "# Delete a specific secret"
echo "# modal secret delete SECRET_NAME"
echo
echo "# List and delete all Koursia secrets (DANGEROUS)"
echo "# modal secret list | grep koursia | awk '{print \$1}' | xargs -I {} modal secret delete {}"
echo

echo -e "${GREEN}✅ Secrets template generated successfully!${NC}"
echo -e "${YELLOW}Remember to replace all placeholder values with actual credentials${NC}"
