# Koursia Platform Environment Configuration Template
# Copy this file to .env and replace placeholder values with actual credentials
# SECURITY WARNING: Never commit .env files with actual credentials to version control

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment (development, staging, production)
NODE_ENV=development
ENVIRONMENT=development
DEBUG=true

# Application Details
APP_NAME=Koursia
APP_VERSION=1.0.0

# Application URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8000
API_BASE_URL=http://localhost:8000/api/v1

# Application Secrets
# Generate with: openssl rand -hex 32
SECRET_KEY=your_secret_key_here_generate_with_openssl_rand_hex_32
JWT_SECRET_KEY=your_jwt_secret_key_here_generate_with_openssl_rand_hex_32
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=koursia
POSTGRES_USER=koursia_user
POSTGRES_PASSWORD=koursia_password_change_in_production

# Database URL (constructed from above values)
DATABASE_URL=postgresql://koursia_user:koursia_password_change_in_production@localhost:5432/koursia

# Test Database
TEST_DATABASE_URL=postgresql://koursia_user:koursia_password_change_in_production@localhost:5432/koursia_test

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# AWS CONFIGURATION
# =============================================================================

# AWS Credentials for S3 storage
# Get from: https://console.aws.amazon.com/iam/home#/security_credentials
AWS_ACCESS_KEY_ID=your_aws_access_key_id_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET=your_s3_bucket_name_here

# S3 Configuration
S3_ENDPOINT_URL=https://s3.amazonaws.com
S3_USE_SSL=true
S3_ADDRESSING_STYLE=virtual

# =============================================================================
# AI SERVICES CONFIGURATION
# =============================================================================

# OpenAI API Configuration
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.7

# Google Gemini API Configuration
# Get from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_OUTPUT_TOKENS=2048

# =============================================================================
# STOCK MEDIA APIS
# =============================================================================

# Pexels API Configuration
# Get from: https://www.pexels.com/api/
PEXELS_API_KEY=your_pexels_api_key_here
PEXELS_API_URL=https://api.pexels.com/v1

# Pixabay API Configuration
# Get from: https://pixabay.com/api/docs/
PIXABAY_API_KEY=your_pixabay_api_key_here
PIXABAY_API_URL=https://pixabay.com/api/

# =============================================================================
# TEXT-TO-SPEECH SERVICES
# =============================================================================

# Chatterbox TTS Configuration
CHATTERBOX_API_KEY=your_chatterbox_api_key_here
CHATTERBOX_API_URL=https://api.chatterbox.com/v1

# Kokoro TTS Configuration
KOKORO_API_KEY=your_kokoro_api_key_here
KOKORO_API_URL=https://api.kokoro.ai/v1

# =============================================================================
# ECOMIMIC V2 CONFIGURATION
# =============================================================================

# Ecomimic Avatar Generation
ECOMIMIC_API_KEY=your_ecomimic_api_key_here
ECOMIMIC_API_URL=http://localhost:8003
ECOMIMIC_WEBHOOK_SECRET=your_ecomimic_webhook_secret_here

# =============================================================================
# MODAL AI SERVICES CONFIGURATION
# =============================================================================

# Modal Authentication
# Get from: https://modal.com/settings/tokens
MODAL_TOKEN_ID=your_modal_token_id_here
MODAL_TOKEN_SECRET=your_modal_token_secret_here
MODAL_PROFILE=koursia-production

# Modal Service URLs (populated after deployment)
MODAL_WHISPER_URL=your_modal_whisper_webhook_url_here
MODAL_TTS_URL=your_modal_tts_webhook_url_here
MODAL_ECOMIMIC_URL=your_modal_ecomimic_webhook_url_here

# Modal Configuration
USE_MODAL_SERVICES=false
MODAL_FALLBACK_TO_LOCAL=true
MODAL_TIMEOUT=300
MODAL_MAX_RETRIES=3

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# SMTP Configuration for email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password_here
SMTP_TLS=true
SMTP_SSL=false

# Email Settings
FROM_EMAIL=<EMAIL>
FROM_NAME=Koursia Platform

# =============================================================================
# PAYMENT PROCESSING
# =============================================================================

# Stripe Configuration
# Get from: https://dashboard.stripe.com/apikeys
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Session Configuration
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature toggles for development
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=false
ENABLE_SOCIAL_LOGIN=false
ENABLE_ANALYTICS=true
ENABLE_DEBUG_MODE=true

# AI Feature Flags
ENABLE_AVATAR_GENERATION=true
ENABLE_VOICE_SYNTHESIS=true
ENABLE_AUTO_TRANSCRIPTION=true
ENABLE_SMART_COURSE_GENERATION=true

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================

# File Upload Limits
MAX_FILE_SIZE=104857600
ALLOWED_FILE_TYPES=mp3,wav,mp4,avi,mov,pdf,docx,pptx,jpg,jpeg,png,gif
UPLOAD_PATH=uploads/

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/koursia.log

# Sentry Configuration (optional)
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=development

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================

# Celery Worker Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_WORKER_CONCURRENCY=4
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_TIMEZONE=UTC

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Cache Settings
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
CACHE_BACKEND=redis

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker Compose Configuration
COMPOSE_PROJECT_NAME=koursia
POSTGRES_CONTAINER_NAME=koursia_postgres
REDIS_CONTAINER_NAME=koursia_redis

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Development Configuration
ENABLE_HOT_RELOAD=true
ENABLE_DEBUG_TOOLBAR=true
ENABLE_PROFILING=false

# Testing Configuration
PYTEST_TIMEOUT=300
TEST_PARALLEL=true

# =============================================================================
# ANALYTICS AND TRACKING (OPTIONAL)
# =============================================================================

# Google Analytics (optional)
GA_TRACKING_ID=your_google_analytics_id_here

# Mixpanel (optional)
MIXPANEL_TOKEN=your_mixpanel_token_here
