{"name": "koursia-platform", "version": "1.0.0", "description": "AI-powered course creation platform with React frontend and FastAPI backend", "main": "index.js", "scripts": {"setup": "chmod +x scripts/setup-development.sh && ./scripts/setup-development.sh", "validate": "python scripts/validate-setup.py", "dev:frontend": "cd frontend && npm start", "dev:backend": "cd backend && source venv/bin/activate && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload", "dev:db": "docker-compose -f docker-compose.dev.yml up -d postgres redis", "test:frontend": "cd frontend && npm test -- --coverage --watchAll=false", "test:backend": "cd backend && source venv/bin/activate && pytest --cov=app --cov-report=html", "test:all": "npm run test:backend && npm run test:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && python -m build", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && source venv/bin/activate && flake8 app/", "format:frontend": "cd frontend && npm run format", "format:backend": "cd backend && source venv/bin/activate && black app/", "clean": "rm -rf frontend/node_modules backend/venv logs/*.log", "reset": "docker-compose -f docker-compose.dev.yml down -v && npm run clean", "logs:backend": "tail -f logs/backend.log", "logs:frontend": "tail -f logs/frontend.log", "stop": "kill $(cat logs/backend.pid logs/frontend.pid 2>/dev/null) 2>/dev/null || true", "restart": "npm run stop && sleep 2 && npm run dev:backend && npm run dev:frontend"}, "repository": {"type": "git", "url": "https://github.com/your-org/koursia-platform.git"}, "keywords": ["ai", "course-creation", "education", "react", "<PERSON><PERSON><PERSON>", "postgresql", "redis", "modal", "gpu", "machine-learning"], "author": "Koursia Team", "license": "MIT", "bugs": {"url": "https://github.com/your-org/koursia-platform/issues"}, "homepage": "https://github.com/your-org/koursia-platform#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0", "python": ">=3.9.0"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "nodemon": "^3.0.1"}, "workspaces": ["frontend"], "config": {"frontend_port": 3000, "backend_port": 8000, "postgres_port": 5432, "redis_port": 6379}}