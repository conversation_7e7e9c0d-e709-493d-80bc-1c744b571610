import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { Provider } from 'react-redux'
import { ThemeProvider } from '@mui/material/styles'
import { configureStore } from '@reduxjs/toolkit'

import Layout from '../Layout/Layout'
import authSlice from '../../store/slices/authSlice'
import uiSlice from '../../store/slices/uiSlice'
import { theme } from '../../theme'

// Mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authSlice,
      ui: uiSlice,
    },
    preloadedState: {
      auth: {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      },
      ui: {
        sidebarOpen: true,
        theme: 'light',
        notifications: [],
      },
      ...initialState,
    },
  })
}

const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
  const store = createMockStore(initialState)
  
  return render(
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  )
}

describe('Layout Component', () => {
  test('renders navigation items', () => {
    renderWithProviders(<Layout />)
    
    expect(screen.getByText('Home')).toBeInTheDocument()
    expect(screen.getByText('Courses')).toBeInTheDocument()
    expect(screen.getByText('Koursia')).toBeInTheDocument()
  })

  test('shows authenticated navigation when user is logged in', () => {
    const authenticatedState = {
      auth: {
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          full_name: 'Test User',
          role: 'user',
          is_active: true,
          is_verified: true,
          created_at: '2023-01-01T00:00:00Z',
        },
        token: 'test-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    }

    renderWithProviders(<Layout />, authenticatedState)
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('My Courses')).toBeInTheDocument()
    expect(screen.getByText('Create Course')).toBeInTheDocument()
  })

  test('shows login button when user is not authenticated', () => {
    renderWithProviders(<Layout />)
    
    expect(screen.getByText('Login')).toBeInTheDocument()
  })

  test('opens profile menu when avatar is clicked', () => {
    const authenticatedState = {
      auth: {
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          full_name: 'Test User',
          role: 'user',
          is_active: true,
          is_verified: true,
          created_at: '2023-01-01T00:00:00Z',
        },
        token: 'test-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    }

    renderWithProviders(<Layout />, authenticatedState)
    
    // Find and click the avatar button
    const avatarButton = screen.getByRole('button', { name: /account of current user/i })
    fireEvent.click(avatarButton)
    
    // Check if profile menu items appear
    expect(screen.getByText('Profile')).toBeInTheDocument()
    expect(screen.getByText('Settings')).toBeInTheDocument()
    expect(screen.getByText('Logout')).toBeInTheDocument()
  })
})
