{"name": "whisper_frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-feather": "^2.0.10", "react-router-dom": "^6.4.2", "react-spinners": "^0.13.6", "swr": "^1.3.0"}, "devDependencies": {"@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@vitejs/plugin-react": "^2.1.0", "autoprefixer": "^10.4.12", "postcss": "^8.4.18", "tailwindcss": "^3.1.8", "typescript": "^4.6.4", "vite": "^3.1.0"}}