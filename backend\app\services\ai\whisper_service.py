"""
Whisper service for speech recognition and transcription
"""

import whisper
import asyncio
from typing import Dict, Any, List, Optional
from pathlib import Path
import json
import aiofiles

from app.core.config import settings


class WhisperService:
    """Service for speech recognition using OpenAI Whisper"""
    
    def __init__(self):
        self.model = None
        self.model_name = "base"  # Can be: tiny, base, small, medium, large
        self.output_dir = Path("media/generated/transcripts")
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def load_model(self, model_name: str = "base") -> None:
        """Load Whisper model"""
        if self.model is None or self.model_name != model_name:
            self.model_name = model_name
            self.model = await asyncio.to_thread(whisper.load_model, model_name)
    
    async def transcribe_audio(
        self,
        audio_file_path: str,
        language: Optional[str] = None,
        task: str = "transcribe"  # "transcribe" or "translate"
    ) -> Dict[str, Any]:
        """Transcribe audio file to text"""
        
        await self.load_model()
        
        try:
            # Run transcription in thread to avoid blocking
            result = await asyncio.to_thread(
                self.model.transcribe,
                audio_file_path,
                language=language,
                task=task,
                verbose=False
            )
            
            return {
                "text": result["text"],
                "language": result["language"],
                "segments": result["segments"],
                "duration": max([seg["end"] for seg in result["segments"]]) if result["segments"] else 0
            }
            
        except Exception as e:
            print(f"Whisper transcription error: {e}")
            return {
                "text": "",
                "language": language or "en",
                "segments": [],
                "duration": 0,
                "error": str(e)
            }
    
    async def generate_subtitles(
        self,
        audio_file_path: str,
        output_format: str = "srt",
        language: Optional[str] = None
    ) -> Optional[str]:
        """Generate subtitle file from audio"""
        
        transcription = await self.transcribe_audio(audio_file_path, language)
        
        if not transcription["segments"]:
            return None
        
        # Generate filename
        audio_path = Path(audio_file_path)
        subtitle_filename = f"{audio_path.stem}.{output_format}"
        subtitle_path = self.output_dir / subtitle_filename
        
        try:
            if output_format.lower() == "srt":
                content = self._generate_srt(transcription["segments"])
            elif output_format.lower() == "vtt":
                content = self._generate_vtt(transcription["segments"])
            elif output_format.lower() == "json":
                content = json.dumps(transcription, indent=2)
            else:
                raise ValueError(f"Unsupported subtitle format: {output_format}")
            
            # Save subtitle file
            async with aiofiles.open(subtitle_path, 'w', encoding='utf-8') as f:
                await f.write(content)
            
            return str(subtitle_path)
            
        except Exception as e:
            print(f"Error generating subtitles: {e}")
            return None
    
    async def transcribe_course_audio(
        self,
        audio_files: List[str],
        language: Optional[str] = None
    ) -> Dict[str, str]:
        """Transcribe multiple audio files for a course"""
        
        transcripts = {}
        
        for audio_file in audio_files:
            try:
                # Generate transcript
                transcription = await self.transcribe_audio(audio_file, language)
                
                # Save transcript file
                audio_path = Path(audio_file)
                transcript_filename = f"{audio_path.stem}_transcript.txt"
                transcript_path = self.output_dir / transcript_filename
                
                async with aiofiles.open(transcript_path, 'w', encoding='utf-8') as f:
                    await f.write(transcription["text"])
                
                transcripts[audio_file] = str(transcript_path)
                
                # Also generate SRT subtitles
                await self.generate_subtitles(audio_file, "srt", language)
                
            except Exception as e:
                print(f"Error transcribing {audio_file}: {e}")
                transcripts[audio_file] = None
        
        return transcripts
    
    def _generate_srt(self, segments: List[Dict[str, Any]]) -> str:
        """Generate SRT subtitle format"""
        
        srt_content = []
        
        for i, segment in enumerate(segments, 1):
            start_time = self._format_timestamp(segment["start"])
            end_time = self._format_timestamp(segment["end"])
            text = segment["text"].strip()
            
            srt_content.append(f"{i}")
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(text)
            srt_content.append("")  # Empty line between subtitles
        
        return "\n".join(srt_content)
    
    def _generate_vtt(self, segments: List[Dict[str, Any]]) -> str:
        """Generate WebVTT subtitle format"""
        
        vtt_content = ["WEBVTT", ""]
        
        for segment in segments:
            start_time = self._format_timestamp_vtt(segment["start"])
            end_time = self._format_timestamp_vtt(segment["end"])
            text = segment["text"].strip()
            
            vtt_content.append(f"{start_time} --> {end_time}")
            vtt_content.append(text)
            vtt_content.append("")
        
        return "\n".join(vtt_content)
    
    def _format_timestamp(self, seconds: float) -> str:
        """Format timestamp for SRT format (HH:MM:SS,mmm)"""
        
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
    
    def _format_timestamp_vtt(self, seconds: float) -> str:
        """Format timestamp for VTT format (HH:MM:SS.mmm)"""
        
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d}.{milliseconds:03d}"
    
    async def detect_language(self, audio_file_path: str) -> str:
        """Detect language of audio file"""
        
        await self.load_model()
        
        try:
            # Load audio and detect language
            audio = whisper.load_audio(audio_file_path)
            audio = whisper.pad_or_trim(audio)
            
            # Make log-Mel spectrogram and move to the same device as the model
            mel = whisper.log_mel_spectrogram(audio).to(self.model.device)
            
            # Detect the spoken language
            _, probs = self.model.detect_language(mel)
            detected_language = max(probs, key=probs.get)
            
            return detected_language
            
        except Exception as e:
            print(f"Language detection error: {e}")
            return "en"  # Default to English
