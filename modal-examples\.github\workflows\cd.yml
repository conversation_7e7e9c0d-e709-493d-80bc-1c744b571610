name: Deploy
on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy example apps
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-24.04
    env:
      MODAL_TOKEN_ID: ${{ secrets.MODAL_MODAL_LABS_TOKEN_ID }}
      MODAL_TOKEN_SECRET: ${{ secrets.MODAL_MODAL_LABS_TOKEN_SECRET }}
      MODAL_ENVIRONMENT: examples

    steps:
      - uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3
        with:
          fetch-depth: 1
      - uses: ./.github/actions/setup

      - name: Run deployment script
        run: |
          python3 internal/deploy.py --no-dry-run
