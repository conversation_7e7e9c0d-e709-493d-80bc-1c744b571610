import React from 'react'
import { Typography, Box, Button } from '@mui/material'
import { Add, SmartToy } from '@mui/icons-material'

const CreateCoursePage: React.FC = () => {
  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Create New Course
      </Typography>
      <Typography variant="body1" paragraph>
        Choose how you want to create your course:
      </Typography>
      
      <Box sx={{ display: 'flex', gap: 2, mt: 4 }}>
        <Button
          variant="contained"
          startIcon={<Add />}
          size="large"
          sx={{ minWidth: 200, minHeight: 100, flexDirection: 'column' }}
        >
          <Typography variant="h6">Smart Course</Typography>
          <Typography variant="body2">AI-generated with slides</Typography>
        </Button>
        
        <Button
          variant="contained"
          startIcon={<SmartToy />}
          size="large"
          sx={{ minWidth: 200, minHeight: 100, flexDirection: 'column' }}
        >
          <Typography variant="h6">Avatar Course</Typography>
          <Typography variant="body2">AI avatar instructor</Typography>
        </Button>
      </Box>
    </Box>
  )
}

export default CreateCoursePage
