"""
Service layer tests
"""

import pytest
from unittest.mock import patch, AsyncMock, MagicMock
import asyncio

from app.services.ai.gemini_service import GeminiService
from app.services.ai.tts_service import TTSService
from app.services.ai.whisper_service import WhisperService
from app.services.media.video_service import VideoService
from app.services.media.slide_service import SlideService
from app.schemas.generation import CourseGenerationRequest, VoiceSettings


class TestGeminiService:
    """Test Gemini AI service"""
    
    @patch('google.generativeai.GenerativeModel')
    @pytest.mark.asyncio
    async def test_generate_course_structure(self, mock_model):
        """Test course structure generation"""
        # Mock response
        mock_response = MagicMock()
        mock_response.text = '''
        {
            "course_overview": {
                "title": "Test Course",
                "description": "A test course",
                "estimated_duration": 60,
                "difficulty_level": "beginner"
            },
            "modules": [
                {
                    "title": "Module 1",
                    "description": "First module",
                    "order_index": 1,
                    "estimated_duration": 30,
                    "lesson_count": 3
                }
            ]
        }
        '''
        
        mock_model_instance = MagicMock()
        mock_model_instance.generate_content.return_value = mock_response
        mock_model.return_value = mock_model_instance
        
        # Create service and request
        service = GeminiService()
        service.model = mock_model_instance
        
        request = CourseGenerationRequest(
            title="Test Course",
            description="A test course",
            target_audience="Beginners",
            difficulty="beginner",
            duration_minutes=60,
            learning_objectives=["Learn basics"],
            topics=["Topic 1"],
            voice_settings=VoiceSettings()
        )
        
        # Test
        result = await service.generate_course_structure(request)
        
        assert "course_overview" in result
        assert "modules" in result
        assert result["course_overview"]["title"] == "Test Course"
        assert len(result["modules"]) == 1
    
    @patch('google.generativeai.GenerativeModel')
    @pytest.mark.asyncio
    async def test_generate_module_content(self, mock_model):
        """Test module content generation"""
        mock_response = MagicMock()
        mock_response.text = '''
        {
            "title": "Test Module",
            "description": "A test module",
            "order_index": 1,
            "lessons": [
                {
                    "title": "Lesson 1",
                    "description": "First lesson",
                    "order_index": 1,
                    "script": "Welcome to lesson 1...",
                    "key_points": ["Point 1", "Point 2"]
                }
            ]
        }
        '''
        
        mock_model_instance = MagicMock()
        mock_model_instance.generate_content.return_value = mock_response
        mock_model.return_value = mock_model_instance
        
        service = GeminiService()
        service.model = mock_model_instance
        
        module_data = {
            "title": "Test Module",
            "description": "A test module",
            "order_index": 1,
            "lesson_count": 1
        }
        
        request = CourseGenerationRequest(
            title="Test Course",
            description="A test course",
            target_audience="Beginners",
            difficulty="beginner",
            duration_minutes=60,
            learning_objectives=["Learn basics"],
            topics=["Topic 1"],
            voice_settings=VoiceSettings()
        )
        
        result = await service.generate_module_content(module_data, request)
        
        assert result["title"] == "Test Module"
        assert "lessons" in result
        assert len(result["lessons"]) == 1


class TestTTSService:
    """Test Text-to-Speech service"""
    
    @patch('aiohttp.ClientSession')
    @pytest.mark.asyncio
    async def test_generate_lesson_audio_chatterbox(self, mock_session):
        """Test audio generation with Chatterbox TTS"""
        # Mock HTTP response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.content.iter_chunked.return_value = [b'audio_data']
        
        mock_session_instance = AsyncMock()
        mock_session_instance.post.return_value.__aenter__.return_value = mock_response
        mock_session.return_value = mock_session_instance
        
        service = TTSService()
        service.chatterbox_api_key = "test_key"
        
        voice_settings = VoiceSettings(
            provider="chatterbox",
            voice_id="en-US-AriaNeural"
        )
        
        with patch('aiofiles.open', create=True) as mock_open:
            mock_file = AsyncMock()
            mock_open.return_value.__aenter__.return_value = mock_file
            
            result = await service.generate_lesson_audio("lesson_1", "Test text", voice_settings)
            
            assert result is not None
            assert "lesson_1" in result
    
    @pytest.mark.asyncio
    async def test_generate_lesson_audio_fallback(self):
        """Test audio generation fallback"""
        service = TTSService()
        service.chatterbox_api_key = None  # No API key
        
        voice_settings = VoiceSettings(
            provider="chatterbox",
            voice_id="en-US-AriaNeural"
        )
        
        with patch('aiofiles.open', create=True) as mock_open:
            mock_file = AsyncMock()
            mock_open.return_value.__aenter__.return_value = mock_file
            
            result = await service.generate_lesson_audio("lesson_1", "Test text", voice_settings)
            
            # Should fall back to local generation
            assert result is not None


class TestWhisperService:
    """Test Whisper speech recognition service"""
    
    @patch('whisper.load_model')
    @patch('whisper.load_audio')
    @pytest.mark.asyncio
    async def test_transcribe_audio(self, mock_load_audio, mock_load_model):
        """Test audio transcription"""
        # Mock Whisper model
        mock_model = MagicMock()
        mock_model.transcribe.return_value = {
            "text": "This is a test transcription",
            "language": "en",
            "segments": [
                {
                    "start": 0.0,
                    "end": 5.0,
                    "text": "This is a test transcription"
                }
            ]
        }
        mock_load_model.return_value = mock_model
        
        service = WhisperService()
        service.model = mock_model
        
        result = await service.transcribe_audio("test_audio.mp3")
        
        assert result["text"] == "This is a test transcription"
        assert result["language"] == "en"
        assert len(result["segments"]) == 1
    
    @patch('aiofiles.open', create=True)
    @pytest.mark.asyncio
    async def test_generate_subtitles_srt(self, mock_open):
        """Test SRT subtitle generation"""
        mock_file = AsyncMock()
        mock_open.return_value.__aenter__.return_value = mock_file
        
        service = WhisperService()
        
        # Mock transcription result
        with patch.object(service, 'transcribe_audio') as mock_transcribe:
            mock_transcribe.return_value = {
                "segments": [
                    {"start": 0.0, "end": 5.0, "text": "First subtitle"},
                    {"start": 5.0, "end": 10.0, "text": "Second subtitle"}
                ]
            }
            
            result = await service.generate_subtitles("test_audio.mp3", "srt")
            
            assert result is not None
            assert "test_audio.srt" in result


class TestVideoService:
    """Test video processing service"""
    
    @patch('asyncio.create_subprocess_exec')
    @pytest.mark.asyncio
    async def test_get_audio_duration(self, mock_subprocess):
        """Test getting audio duration"""
        # Mock ffprobe output
        mock_process = AsyncMock()
        mock_process.communicate.return_value = (
            b'{"format": {"duration": "120.5"}}',
            b''
        )
        mock_process.returncode = 0
        mock_subprocess.return_value = mock_process
        
        service = VideoService()
        duration = await service._get_audio_duration("test_audio.mp3")
        
        assert duration == 120.5
    
    @patch('asyncio.create_subprocess_exec')
    @pytest.mark.asyncio
    async def test_create_video_with_static_background(self, mock_subprocess):
        """Test creating video with static background"""
        # Mock successful ffmpeg execution
        mock_process = AsyncMock()
        mock_process.communicate.return_value = (b'', b'')
        mock_process.returncode = 0
        mock_subprocess.return_value = mock_process
        
        service = VideoService()
        
        with patch.object(service, '_get_audio_duration', return_value=60.0):
            result = await service._create_video_with_static_background(
                "test_audio.mp3",
                service.output_dir / "test_video.mp4",
                "Test Title"
            )
            
            assert result is True


class TestSlideService:
    """Test slide generation service"""
    
    @pytest.mark.asyncio
    async def test_parse_lesson_content(self):
        """Test parsing lesson content for slides"""
        service = SlideService()
        
        content = """
        Introduction to the topic.
        
        This is the main content section with important information.
        
        Here are the key points to remember.
        """
        
        slides = await service._parse_lesson_content(content, "Test Lesson")
        
        assert len(slides) >= 3  # Title, content slides, summary
        assert slides[0]["type"] == "title"
        assert slides[0]["title"] == "Test Lesson"
        assert slides[-1]["type"] == "summary"
    
    @pytest.mark.asyncio
    async def test_generate_marp_content(self):
        """Test generating Marp markdown content"""
        service = SlideService()
        
        slides = [
            {"title": "Test Slide", "content": "# Test Content", "type": "content"}
        ]
        
        with patch.object(service, '_load_template', return_value="---\nmarp: true\n---"):
            content = await service._generate_marp_content(slides, "default")
            
            assert "marp: true" in content
            assert "# Test Content" in content
    
    @patch('asyncio.create_subprocess_exec')
    @pytest.mark.asyncio
    async def test_convert_to_pdf_success(self, mock_subprocess):
        """Test successful PDF conversion"""
        # Mock successful marp execution
        mock_process = AsyncMock()
        mock_process.communicate.return_value = (b'', b'')
        mock_process.returncode = 0
        mock_subprocess.return_value = mock_process
        
        service = SlideService()
        
        result = await service._convert_to_pdf(
            service.output_dir / "test.md",
            service.output_dir / "test.pdf"
        )
        
        assert result is True
