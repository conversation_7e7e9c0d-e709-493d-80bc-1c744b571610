<html>
  <head>
    <script
      defer
      src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"
    ></script>
    <script src="https://cdn.tailwindcss.com"></script>

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>{{ model_name }} — Modal</title>
  </head>
  <body x-data="state()">
    <div class="max-w-3xl mx-auto pt-4 pb-8 px-10 sm:py-12 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-medium text-center mb-10">
        {{ model_name }} on Modal
      </h2>

      <form
        @submit.prevent="submitPrompt"
        class="flex items-center justify-center gap-x-4 gap-y-2 w-full mx-auto mb-4"
      >
        <input
          x-data
          x-model="prompt"
          x-init="$nextTick(() => { $el.focus(); });"
          type="text"
          class="flex w-full px-3 py-3 text-md bg-white border rounded-md border-neutral-300 ring-offset-background placeholder:text-neutral-500 focus:border-neutral-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-neutral-400 disabled:cursor-not-allowed disabled:opacity-50 text-center"
        />
        <button
          type="submit"
          class="inline-flex items-center justify-center px-4 py-3 text-sm font-medium tracking-wide text-white transition-colors duration-200 rounded-md bg-neutral-950 hover:bg-neutral-900 focus:ring-2 focus:ring-offset-2 focus:ring-neutral-900 focus:shadow-outline focus:outline-none"
          :disabled="loading"
        >
          <span x-show="!loading">Submit</span>
          <div class="animate-spin w-6 h-6 mx-3" x-show="loading">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="lucide lucide-loader-2"
            >
              <path d="M21 12a9 9 0 1 1-6.219-8.56" />
            </svg>
          </div>
        </button>
      </form>

      <div class="mx-auto w-full max-w-[768px] relative grid">
        <div
          style="padding-top: 100%"
          x-show="loading"
          class="absolute w-full h-full animate-pulse bg-neutral-100 rounded-md"
        ></div>
        <img
          x-show="imageURL"
          class="rounded-md self-center justify-self-center"
          :src="imageURL"
        />
      </div>
    </div>

    <script>
      function state() {
        return {
          prompt: "{{ default_prompt }}",
          submitted: "",
          loading: false,
          imageURL: "",
          async submitPrompt() {
            if (!this.prompt) return;
            this.submitted = this.prompt;
            this.loading = true;

            try {
              const res = await fetch(
                `{{ inference_url }}?prompt=${this.submitted}`,
              );

              if (!res.ok) {
                throw new Error("Inference failed");
              }

              const blob = await res.blob();
              this.imageURL = URL.createObjectURL(blob);
            } catch (error) {
              console.error("Fetch failed:", error);
              alert("There was an error generating the image.");
            } finally {
              this.loading = false;
              console.log(this.imageURL);
            }
          },
        };
      }
    </script>
  </body>
</html>
