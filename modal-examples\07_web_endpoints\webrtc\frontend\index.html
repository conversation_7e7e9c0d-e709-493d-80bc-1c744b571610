<!DOCTYPE html>
<html>
<head>
    <title>WebRTC YOLO Demo</title>
    <style>
        video {
            width: 320px;
            height: 240px;
            margin: 10px;
            border: 1px solid black;
        }
        button {
            margin: 10px;
            padding: 10px;
        }
        #videos {
            display: flex;
            flex-wrap: wrap;
        }
        .radio-group {
            margin: 10px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .radio-group label {
            margin-right: 15px;
        }
        #statusDisplay {
            margin: 10px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #f5f5f5;
            min-height: 20px;
            max-height: 150px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .status-line {
            margin: 2px 0;
            padding: 2px;
            border-bottom: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="radio-group">
        <label>
            <input type="radio" name="iceServer" value="stun" checked> STUN Server
        </label>
        <label>
            <input type="radio" name="iceServer" value="turn"> TURN Server
        </label>
    </div>
    <div id="videos">
        <video id="localVideo" autoplay playsinline muted></video>
        <video id="remoteVideo" autoplay playsinline></video>
    </div>
    <div>
        <button id="startWebcamButton">Start Webcam</button>
        <button id="startStreamingButton" disabled>Stream YOLO</button>
        <button id="stopStreamingButton" disabled>Stop Streaming</button>
    </div>
    <div id="statusDisplay"></div>
    <script type="module" src="/static/webcam_webrtc.js"></script>
</body>
</html> 