"""
Modal deployment script for Koursia AI services
"""

import modal
import subprocess
import sys
import os
from pathlib import Path

def check_modal_auth():
    """Check if Modal is authenticated"""
    try:
        result = subprocess.run(["modal", "token", "current"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Modal authentication verified")
            return True
        else:
            print("❌ Modal authentication failed")
            return False
    except FileNotFoundError:
        print("❌ Modal CLI not found. Please install with: pip install modal")
        return False

def setup_secrets():
    """Setup Modal secrets for API keys"""
    secrets = [
        ("gemini-api-key", "GEMINI_API_KEY"),
        ("openai-api-key", "OPENAI_API_KEY"),
        ("chatterbox-api-key", "CHATTERBOX_API_KEY"),
        ("kokoro-api-key", "KOKORO_API_KEY"),
        ("ecomimic-api-key", "ECOMIMIC_API_KEY"),
    ]
    
    print("Setting up Modal secrets...")
    
    for secret_name, env_var in secrets:
        value = os.getenv(env_var)
        if value:
            try:
                subprocess.run([
                    "modal", "secret", "create", secret_name,
                    f"{env_var}={value}"
                ], check=True, capture_output=True)
                print(f"✅ Secret {secret_name} created")
            except subprocess.CalledProcessError as e:
                if "already exists" in e.stderr.decode():
                    print(f"⚠️ Secret {secret_name} already exists")
                else:
                    print(f"❌ Failed to create secret {secret_name}: {e}")
        else:
            print(f"⚠️ Environment variable {env_var} not set")

def deploy_services():
    """Deploy all Modal services"""
    services = [
        "whisper_service.py",
        "tts_service.py",
        "ecomimic_service.py",
    ]
    
    print("Deploying Modal services...")
    
    for service in services:
        if Path(service).exists():
            try:
                print(f"Deploying {service}...")
                subprocess.run([
                    "modal", "deploy", service
                ], check=True)
                print(f"✅ {service} deployed successfully")
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to deploy {service}: {e}")
        else:
            print(f"⚠️ Service file {service} not found")

def get_service_urls():
    """Get deployed service URLs"""
    try:
        result = subprocess.run([
            "modal", "app", "list", "--json"
        ], capture_output=True, text=True, check=True)
        
        import json
        apps = json.loads(result.stdout)
        
        koursia_app = None
        for app in apps:
            if app.get("name") == "koursia-ai-services":
                koursia_app = app
                break
        
        if koursia_app:
            print("\n🚀 Deployed service URLs:")
            print(f"Whisper Service: {koursia_app.get('whisper_webhook', 'Not deployed')}")
            print(f"TTS Service: {koursia_app.get('tts_webhook', 'Not deployed')}")
            print(f"Ecomimic Service: {koursia_app.get('ecomimic_webhook', 'Not deployed')}")
        else:
            print("⚠️ Koursia app not found in deployed apps")
            
    except Exception as e:
        print(f"❌ Failed to get service URLs: {e}")

def test_services():
    """Test deployed services"""
    print("\n🧪 Testing deployed services...")
    
    # Test health endpoints
    health_tests = [
        ("Whisper", "whisper_health"),
        ("TTS", "tts_health"),
        ("Ecomimic", "ecomimic_health"),
    ]
    
    for service_name, health_endpoint in health_tests:
        try:
            result = subprocess.run([
                "modal", "run", f"{service_name.lower()}_service.py::{health_endpoint}"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ {service_name} service health check passed")
            else:
                print(f"❌ {service_name} service health check failed")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {service_name} service health check timed out")
        except Exception as e:
            print(f"❌ {service_name} service test error: {e}")

def monitor_costs():
    """Monitor Modal usage and costs"""
    try:
        result = subprocess.run([
            "modal", "usage", "--json"
        ], capture_output=True, text=True, check=True)
        
        import json
        usage = json.loads(result.stdout)
        
        print("\n💰 Modal Usage Summary:")
        print(f"Current month credits used: {usage.get('credits_used', 'N/A')}")
        print(f"GPU hours used: {usage.get('gpu_hours', 'N/A')}")
        print(f"Function calls: {usage.get('function_calls', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Failed to get usage information: {e}")

def setup_monitoring():
    """Setup monitoring and alerting"""
    print("\n📊 Setting up monitoring...")
    
    # Create monitoring script
    monitoring_script = """
import modal
import time
import requests
from datetime import datetime

@modal.Function.from_name("koursia-ai-services", "monitor")
def check_service_health():
    services = ["whisper", "tts", "ecomimic"]
    results = {}
    
    for service in services:
        try:
            # Check health endpoint
            response = requests.get(f"https://api.modal.com/v1/apps/koursia-ai-services/{service}_health")
            results[service] = {
                "status": "healthy" if response.status_code == 200 else "unhealthy",
                "response_time": response.elapsed.total_seconds(),
                "timestamp": datetime.utcnow().isoformat(),
            }
        except Exception as e:
            results[service] = {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }
    
    return results

if __name__ == "__main__":
    with modal.run():
        health_status = check_service_health.remote()
        print(health_status)
"""
    
    with open("monitoring.py", "w") as f:
        f.write(monitoring_script)
    
    print("✅ Monitoring script created")

def main():
    """Main deployment function"""
    print("🚀 Starting Modal deployment for Koursia AI services")
    
    # Check prerequisites
    if not check_modal_auth():
        print("Please authenticate with Modal first: modal token new")
        sys.exit(1)
    
    # Setup secrets
    setup_secrets()
    
    # Deploy services
    deploy_services()
    
    # Get service URLs
    get_service_urls()
    
    # Test services
    test_services()
    
    # Monitor costs
    monitor_costs()
    
    # Setup monitoring
    setup_monitoring()
    
    print("\n🎉 Modal deployment completed!")
    print("\n📋 Next steps:")
    print("1. Update your backend configuration with the new service URLs")
    print("2. Test the integration with your main application")
    print("3. Set up monitoring and alerting")
    print("4. Configure auto-scaling based on your usage patterns")

if __name__ == "__main__":
    main()
