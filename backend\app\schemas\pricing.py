"""
Pricing and subscription schemas
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from uuid import UUID

from app.models.subscription import PlanType, BillingCycle, SubscriptionStatus


class PlanBase(BaseModel):
    name: str
    plan_type: PlanType
    description: Optional[str] = None
    monthly_price: float = Field(..., ge=0)
    yearly_price: float = Field(..., ge=0)
    
    # Limits
    max_smart_courses_per_month: int = Field(..., ge=-1)  # -1 = unlimited
    max_avatar_courses_per_month: int = Field(..., ge=0)
    max_course_duration_minutes: int = Field(..., gt=0)
    
    # Features
    has_premium_templates: bool = False
    has_custom_branding: bool = False
    has_advanced_analytics: bool = False
    has_api_access: bool = False
    has_priority_support: bool = False
    has_team_collaboration: bool = False
    has_white_label: bool = False
    has_custom_integrations: bool = False
    has_sla_guarantee: bool = False
    
    # Video settings
    max_video_resolution: str = "720p"
    has_watermark: bool = True
    
    # Trial
    trial_days: int = Field(..., ge=0)
    
    # Status
    is_active: bool = True
    is_featured: bool = False


class PlanCreate(PlanBase):
    pass


class PlanUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    monthly_price: Optional[float] = Field(None, ge=0)
    yearly_price: Optional[float] = Field(None, ge=0)
    is_active: Optional[bool] = None
    is_featured: Optional[bool] = None


class PlanResponse(PlanBase):
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class SubscriptionBase(BaseModel):
    plan_id: UUID
    billing_cycle: BillingCycle
    amount: float = Field(..., ge=0)
    currency: str = "USD"


class SubscriptionCreate(BaseModel):
    plan_id: UUID
    billing_cycle: BillingCycle
    payment_method_id: Optional[str] = None
    promo_code: Optional[str] = None


class SubscriptionUpdate(BaseModel):
    plan_id: Optional[UUID] = None
    billing_cycle: Optional[BillingCycle] = None
    payment_method_id: Optional[str] = None


class SubscriptionResponse(SubscriptionBase):
    id: UUID
    user_id: UUID
    status: SubscriptionStatus
    
    # Dates
    start_date: datetime
    end_date: Optional[datetime] = None
    trial_start: Optional[datetime] = None
    trial_end: Optional[datetime] = None
    canceled_at: Optional[datetime] = None
    
    # Usage
    smart_courses_used: int = 0
    avatar_courses_used: int = 0
    last_usage_reset: datetime
    
    # External IDs
    stripe_subscription_id: Optional[str] = None
    stripe_customer_id: Optional[str] = None
    
    # Metadata
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # Related data
    plan: PlanResponse
    
    class Config:
        from_attributes = True


class UsageResponse(BaseModel):
    # Current period usage
    smart_courses_used: int
    avatar_courses_used: int
    api_calls_used: int = 0
    
    # Limits from current plan
    smart_courses_limit: int  # -1 = unlimited
    avatar_courses_limit: int
    api_calls_limit: int = -1
    
    # Usage percentages
    smart_courses_usage_percent: float
    avatar_courses_usage_percent: float
    api_calls_usage_percent: float = 0.0
    
    # Billing period
    billing_period_start: datetime
    billing_period_end: datetime
    
    # Plan info
    current_plan: PlanResponse
    
    # Warnings
    approaching_limits: List[str] = []
    exceeded_limits: List[str] = []


class PricingCalculation(BaseModel):
    plan_id: UUID
    billing_cycle: BillingCycle
    
    # Base pricing
    base_price: float
    
    # Discounts
    discount_amount: float = 0.0
    discount_percentage: float = 0.0
    promo_code: Optional[str] = None
    
    # Tax
    tax_amount: float = 0.0
    tax_percentage: float = 0.0
    
    # Final pricing
    subtotal: float
    total_amount: float
    
    # Savings (for yearly billing)
    yearly_savings: Optional[float] = None
    yearly_savings_percentage: Optional[float] = None
    
    # Currency
    currency: str = "USD"
    
    # Trial info
    trial_days: int = 0
    trial_amount: float = 0.0


class InvoiceResponse(BaseModel):
    id: UUID
    invoice_number: str
    amount: float
    currency: str
    tax_amount: float
    total_amount: float
    status: str
    
    # Dates
    invoice_date: datetime
    due_date: datetime
    paid_at: Optional[datetime] = None
    
    # External IDs
    stripe_invoice_id: Optional[str] = None
    stripe_payment_intent_id: Optional[str] = None
    
    # Metadata
    description: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class PaymentMethodBase(BaseModel):
    type: str  # card, bank_account, paypal
    last_four: Optional[str] = None
    brand: Optional[str] = None
    exp_month: Optional[int] = None
    exp_year: Optional[int] = None
    is_default: bool = False


class PaymentMethodCreate(PaymentMethodBase):
    stripe_payment_method_id: str


class PaymentMethodResponse(PaymentMethodBase):
    id: UUID
    user_id: UUID
    is_active: bool
    stripe_payment_method_id: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class PromoCodeBase(BaseModel):
    code: str = Field(..., min_length=3, max_length=50)
    discount_type: str  # percentage, fixed_amount
    discount_value: float = Field(..., gt=0)
    currency: str = "USD"
    
    # Validity
    valid_from: datetime
    valid_until: datetime
    max_uses: Optional[int] = None
    
    # Restrictions
    applicable_plans: Optional[List[UUID]] = None
    first_time_only: bool = False
    
    # Metadata
    description: Optional[str] = None


class PromoCodeCreate(PromoCodeBase):
    pass


class PromoCodeResponse(PromoCodeBase):
    id: UUID
    uses_count: int = 0
    is_active: bool = True
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class PromoCodeValidation(BaseModel):
    code: str
    plan_id: UUID
    user_id: Optional[UUID] = None


class PromoCodeValidationResponse(BaseModel):
    valid: bool
    discount_amount: float = 0.0
    discount_percentage: float = 0.0
    error_message: Optional[str] = None
    promo_code: Optional[PromoCodeResponse] = None


class BillingAddress(BaseModel):
    line1: str
    line2: Optional[str] = None
    city: str
    state: str
    postal_code: str
    country: str = "US"


class CheckoutSession(BaseModel):
    plan_id: UUID
    billing_cycle: BillingCycle
    promo_code: Optional[str] = None
    billing_address: Optional[BillingAddress] = None
    success_url: str
    cancel_url: str


class CheckoutSessionResponse(BaseModel):
    session_id: str
    checkout_url: str
    expires_at: datetime


class FeatureFlags(BaseModel):
    # Course creation
    can_create_smart_courses: bool
    can_create_avatar_courses: bool
    max_course_duration: int
    
    # Templates and customization
    has_premium_templates: bool
    has_custom_branding: bool
    can_remove_watermark: bool
    
    # Analytics and insights
    has_basic_analytics: bool
    has_advanced_analytics: bool
    
    # Collaboration and sharing
    has_team_collaboration: bool
    can_share_courses: bool
    
    # Technical features
    has_api_access: bool
    has_webhook_support: bool
    has_custom_integrations: bool
    
    # Support and SLA
    has_priority_support: bool
    has_sla_guarantee: bool
    has_dedicated_support: bool
    
    # White label
    has_white_label: bool
    can_customize_domain: bool
    
    # Video quality
    max_video_resolution: str
    supported_formats: List[str]
    
    # Usage limits
    monthly_smart_courses: int  # -1 = unlimited
    monthly_avatar_courses: int
    monthly_api_calls: int  # -1 = unlimited
    
    # Current usage
    current_smart_courses: int
    current_avatar_courses: int
    current_api_calls: int


class WebhookEvent(BaseModel):
    id: str
    type: str
    data: Dict[str, Any]
    created: datetime
    
    class Config:
        from_attributes = True
