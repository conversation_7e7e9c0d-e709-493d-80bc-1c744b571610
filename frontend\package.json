{"name": "koursia-frontend", "version": "1.0.0", "description": "Koursia - AI-Powered Course Creation Platform Frontend", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "axios": "^1.6.2", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.3.3", "react-dropzone": "^14.2.3", "react-player": "^2.13.0", "react-beautiful-dnd": "^13.1.1", "framer-motion": "^10.16.5", "date-fns": "^2.30.0", "lodash": "^4.17.21", "uuid": "^9.0.1", "react-toastify": "^9.1.3", "recharts": "^2.8.0", "react-markdown": "^9.0.1", "prismjs": "^1.29.0", "react-syntax-highlighter": "^15.5.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/lodash": "^4.14.202", "@types/uuid": "^9.0.7", "@types/prismjs": "^1.26.3", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "jsdom": "^22.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}