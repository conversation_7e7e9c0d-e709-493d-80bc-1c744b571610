[tool.pytest.ini_options]
filterwarnings = [
    "error::DeprecationWarning",
    "error::modal.exception.DeprecationError",
    "ignore::DeprecationWarning:pytest.*:",
]
addopts = "--ignore 07_web_endpoints/webrtc/webrtc_yolo_test.py --ignore 06_gpu_and_ml/llm-serving/openai_compatible/load_test.py --ignore 07_web_endpoints/fasthtml-checkboxes/cbx_load_test.py"

[tool.mypy]
ignore_missing_imports = true
check_untyped_defs = true
no_strict_optional = true

# https://github.com/python/mypy/issues/10632
[[tool.mypy.overrides]]
module = "requests"
ignore_missing_imports = true

[tool.ruff]
exclude = [".venv", "venv", "__pycache__"]
line-length = 88
# TODO: Add when available: "E266", "E203"
lint.ignore = ["E501", "E741", "E402"]
lint.select = ['E', 'F', 'W', 'I']

[tool.ruff.lint.isort]
combine-as-imports = true
known-third-party = ["modal"]
