import React from 'react'
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  LinearProgress,
  Chip,
} from '@mui/material'
import {
  Add,
  School,
  VideoLibrary,
  TrendingUp,
  People,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'

const DashboardPage: React.FC = () => {
  const navigate = useNavigate()

  const stats = [
    { title: 'Total Courses', value: '12', icon: <School />, color: 'primary' },
    { title: 'Published Courses', value: '8', icon: <VideoLibrary />, color: 'success' },
    { title: 'Total Students', value: '1,234', icon: <People />, color: 'info' },
    { title: 'Course Views', value: '5,678', icon: <TrendingUp />, color: 'warning' },
  ]

  const recentCourses = [
    {
      id: '1',
      title: 'Introduction to React',
      status: 'Published',
      progress: 100,
      students: 45,
      lastUpdated: '2 days ago',
    },
    {
      id: '2',
      title: 'Advanced JavaScript',
      status: 'Generating',
      progress: 75,
      students: 0,
      lastUpdated: '1 hour ago',
    },
    {
      id: '3',
      title: 'Python for Beginners',
      status: 'Draft',
      progress: 30,
      students: 0,
      lastUpdated: '1 week ago',
    },
  ]

  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1">
          Dashboard
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => navigate('/create-course')}
        >
          Create New Course
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box
                    sx={{
                      p: 1,
                      borderRadius: 1,
                      bgcolor: `${stat.color}.light`,
                      color: `${stat.color}.main`,
                      mr: 2,
                    }}
                  >
                    {stat.icon}
                  </Box>
                  <Box>
                    <Typography variant="h4" component="div">
                      {stat.value}
                    </Typography>
                    <Typography color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Recent Courses */}
      <Card>
        <CardContent>
          <Typography variant="h6" component="h2" gutterBottom>
            Recent Courses
          </Typography>
          <Grid container spacing={2}>
            {recentCourses.map((course) => (
              <Grid item xs={12} key={course.id}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box>
                        <Typography variant="h6" component="h3">
                          {course.title}
                        </Typography>
                        <Typography color="text.secondary" variant="body2">
                          Last updated: {course.lastUpdated}
                        </Typography>
                      </Box>
                      <Chip
                        label={course.status}
                        color={
                          course.status === 'Published' ? 'success' :
                          course.status === 'Generating' ? 'warning' : 'default'
                        }
                        size="small"
                      />
                    </Box>
                    
                    <Box sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Progress</Typography>
                        <Typography variant="body2">{course.progress}%</Typography>
                      </Box>
                      <LinearProgress variant="determinate" value={course.progress} />
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        {course.students} students enrolled
                      </Typography>
                      <Button size="small" onClick={() => navigate(`/courses/${course.id}`)}>
                        View Course
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>
    </Box>
  )
}

export default DashboardPage
