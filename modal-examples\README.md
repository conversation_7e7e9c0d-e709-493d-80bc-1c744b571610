<p align="center">
  <a href="https://modal.com">
    <img src="https://modal-cdn.com/Modal-Primary-Logo.png" height="96">
    <h3 align="center">Modal Examples</h3>
  </a>
</p>

This is a collection of examples for [Modal](https://modal.com/). Use these examples to learn Modal and build your own robust and scalable applications.

## Usage

First, sign up for a free account at [modal.com](https://modal.com/) and follow
the setup instructions to install the `modal` package and set your API key.

The examples are organized into several folders based on their category. You can
generally run the files in any folder much like you run ordinary Python programs, with a
command like:

```bash
modal run 01_getting_started/hello_world.py
```

Although these scripts are run on your local machine, they'll communicate with
<PERSON><PERSON> and run in our cloud, spawning serverless containers on demand.

## Examples

- [**`01_getting_started/`**](01_getting_started) through [**`14_clusters/`**](14_clusters) provide a guided tour through <PERSON><PERSON>'s concepts and capabilities.
- [**`misc/`**](/misc) contains uncategorized, miscellaneous examples.

_These examples are continuously tested for correctness against Python **3.11**._

## License

The [MIT license](LICENSE).
