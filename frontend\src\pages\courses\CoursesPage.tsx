import React from 'react'
import { Typography, Box } from '@mui/material'

interface CoursesPageProps {
  userCourses?: boolean
}

const CoursesPage: React.FC<CoursesPageProps> = ({ userCourses = false }) => {
  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        {userCourses ? 'My Courses' : 'All Courses'}
      </Typography>
      <Typography variant="body1">
        {userCourses 
          ? 'Manage your created courses here.'
          : 'Browse all available courses on the platform.'
        }
      </Typography>
    </Box>
  )
}

export default CoursesPage
