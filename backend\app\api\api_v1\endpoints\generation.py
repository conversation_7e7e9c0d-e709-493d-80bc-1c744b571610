"""
Course generation endpoints
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.security import get_current_active_user
from app.models.user import User
from app.models.course import Course
from app.models.media import GenerationJob
from app.schemas.generation import (
    GenerationJobResponse, CourseGenerationRequest, AvatarGenerationRequest,
    GenerationJobCreate, GenerationJobUpdate
)
from app.services.generation import CourseGenerationService

router = APIRouter()


@router.post("/course", response_model=GenerationJobResponse, status_code=status.HTTP_201_CREATED)
async def start_course_generation(
    generation_request: CourseGenerationRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Start course generation process
    """
    # Create generation job
    job = GenerationJob(
        job_type="course_generation",
        status="pending",
        input_data=generation_request.dict(),
        user_id=current_user.id
    )
    
    db.add(job)
    db.commit()
    db.refresh(job)
    
    # Start background task
    background_tasks.add_task(
        CourseGenerationService.generate_course,
        job.id,
        generation_request,
        current_user.id
    )
    
    return job


@router.post("/avatar", response_model=GenerationJobResponse, status_code=status.HTTP_201_CREATED)
async def start_avatar_generation(
    generation_request: AvatarGenerationRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Start avatar course generation process
    """
    # Create generation job
    job = GenerationJob(
        job_type="avatar_generation",
        status="pending",
        input_data=generation_request.dict(),
        user_id=current_user.id
    )
    
    db.add(job)
    db.commit()
    db.refresh(job)
    
    # Start background task
    background_tasks.add_task(
        CourseGenerationService.generate_avatar_course,
        job.id,
        generation_request,
        current_user.id
    )
    
    return job


@router.get("/jobs", response_model=List[GenerationJobResponse])
async def get_generation_jobs(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get user's generation jobs
    """
    jobs = db.query(GenerationJob).filter(
        GenerationJob.user_id == current_user.id
    ).order_by(GenerationJob.created_at.desc()).all()
    
    return jobs


@router.get("/jobs/{job_id}", response_model=GenerationJobResponse)
async def get_generation_job(
    job_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get generation job by ID
    """
    job = db.query(GenerationJob).filter(
        GenerationJob.id == job_id,
        GenerationJob.user_id == current_user.id
    ).first()
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Generation job not found"
        )
    
    return job


@router.delete("/jobs/{job_id}")
async def cancel_generation_job(
    job_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Cancel generation job
    """
    job = db.query(GenerationJob).filter(
        GenerationJob.id == job_id,
        GenerationJob.user_id == current_user.id
    ).first()
    
    if not job:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Generation job not found"
        )
    
    if job.status in ["completed", "failed"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot cancel completed or failed job"
        )
    
    # Update job status
    job.status = "cancelled"
    db.commit()
    
    return {"message": "Generation job cancelled successfully"}


@router.post("/regenerate/{course_id}", response_model=GenerationJobResponse)
async def regenerate_course_content(
    course_id: str,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Regenerate content for an existing course
    """
    course = db.query(Course).filter(
        Course.id == course_id,
        Course.creator_id == current_user.id
    ).first()
    
    if not course:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Course not found"
        )
    
    # Create regeneration job
    job = GenerationJob(
        job_type="course_regeneration",
        status="pending",
        input_data={"course_id": course_id},
        user_id=current_user.id,
        course_id=course_id
    )
    
    db.add(job)
    db.commit()
    db.refresh(job)
    
    # Start background task
    background_tasks.add_task(
        CourseGenerationService.regenerate_course,
        job.id,
        course_id,
        current_user.id
    )
    
    return job
