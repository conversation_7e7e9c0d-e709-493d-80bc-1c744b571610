"""
Stock media service for Pexels and Pixabay integration
"""

import aiohttp
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json

from app.core.config import settings
from app.core.database import SessionLocal
from app.models.media import StockMediaCache
from app.schemas.media import StockMediaSearchRequest, StockMediaSearchResponse, StockMediaItem


class StockMediaService:
    """Service for searching and downloading stock media"""
    
    def __init__(self):
        self.pexels_api_key = settings.PEXELS_API_KEY
        self.pixabay_api_key = settings.PIXABAY_API_KEY
        self.cache_duration = timedelta(hours=24)  # Cache results for 24 hours
    
    async def search_media(
        self,
        request: StockMediaSearchRequest
    ) -> StockMediaSearchResponse:
        """Search for stock media across providers"""
        
        # Check cache first
        cached_result = await self._get_cached_result(request)
        if cached_result:
            return cached_result
        
        # Search based on provider
        if request.provider == "pexels":
            result = await self._search_pexels(request)
        elif request.provider == "pixabay":
            result = await self._search_pixabay(request)
        else:
            raise ValueError(f"Unsupported provider: {request.provider}")
        
        # Cache the result
        await self._cache_result(request, result)
        
        return result
    
    async def _search_pexels(
        self,
        request: StockMediaSearchRequest
    ) -> StockMediaSearchResponse:
        """Search Pexels for stock photos"""
        
        if not self.pexels_api_key:
            raise ValueError("Pexels API key not configured")
        
        headers = {
            "Authorization": self.pexels_api_key
        }
        
        params = {
            "query": request.query,
            "per_page": request.per_page,
            "page": request.page
        }
        
        if request.category:
            params["category"] = request.category
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://api.pexels.com/v1/search",
                    headers=headers,
                    params=params
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_pexels_response(data, request)
                    else:
                        error_text = await response.text()
                        raise Exception(f"Pexels API error: {response.status} - {error_text}")
                        
        except Exception as e:
            print(f"Pexels search error: {e}")
            return self._create_empty_response(request)
    
    async def _search_pixabay(
        self,
        request: StockMediaSearchRequest
    ) -> StockMediaSearchResponse:
        """Search Pixabay for stock media"""
        
        if not self.pixabay_api_key:
            raise ValueError("Pixabay API key not configured")
        
        params = {
            "key": self.pixabay_api_key,
            "q": request.query,
            "per_page": request.per_page,
            "page": request.page,
            "safesearch": "true",
            "image_type": "photo"
        }
        
        if request.category:
            params["category"] = request.category
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://pixabay.com/api/",
                    params=params
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_pixabay_response(data, request)
                    else:
                        error_text = await response.text()
                        raise Exception(f"Pixabay API error: {response.status} - {error_text}")
                        
        except Exception as e:
            print(f"Pixabay search error: {e}")
            return self._create_empty_response(request)
    
    def _parse_pexels_response(
        self,
        data: Dict[str, Any],
        request: StockMediaSearchRequest
    ) -> StockMediaSearchResponse:
        """Parse Pexels API response"""
        
        items = []
        
        for photo in data.get("photos", []):
            item = StockMediaItem(
                id=str(photo["id"]),
                url=photo["url"],
                preview_url=photo["src"]["medium"],
                download_url=photo["src"]["original"],
                title=photo.get("alt", "Untitled"),
                description=photo.get("alt", ""),
                tags=[],  # Pexels doesn't provide tags in search results
                width=photo["width"],
                height=photo["height"],
                photographer=photo["photographer"],
                photographer_url=photo["photographer_url"],
                license="Pexels License",
                source="pexels"
            )
            items.append(item)
        
        total_pages = (data.get("total_results", 0) + request.per_page - 1) // request.per_page
        
        return StockMediaSearchResponse(
            items=items,
            total=data.get("total_results", 0),
            page=request.page,
            per_page=request.per_page,
            pages=total_pages,
            query=request.query,
            provider=request.provider
        )
    
    def _parse_pixabay_response(
        self,
        data: Dict[str, Any],
        request: StockMediaSearchRequest
    ) -> StockMediaSearchResponse:
        """Parse Pixabay API response"""
        
        items = []
        
        for hit in data.get("hits", []):
            tags = hit.get("tags", "").split(", ") if hit.get("tags") else []
            
            item = StockMediaItem(
                id=str(hit["id"]),
                url=hit["pageURL"],
                preview_url=hit["webformatURL"],
                download_url=hit["largeImageURL"],
                title=tags[0] if tags else "Untitled",
                description=", ".join(tags),
                tags=tags,
                width=hit["imageWidth"],
                height=hit["imageHeight"],
                photographer=hit["user"],
                photographer_url=f"https://pixabay.com/users/{hit['user']}-{hit['user_id']}/",
                license="Pixabay License",
                source="pixabay"
            )
            items.append(item)
        
        total_pages = (data.get("totalHits", 0) + request.per_page - 1) // request.per_page
        
        return StockMediaSearchResponse(
            items=items,
            total=data.get("totalHits", 0),
            page=request.page,
            per_page=request.per_page,
            pages=total_pages,
            query=request.query,
            provider=request.provider
        )
    
    def _create_empty_response(
        self,
        request: StockMediaSearchRequest
    ) -> StockMediaSearchResponse:
        """Create empty response for error cases"""
        
        return StockMediaSearchResponse(
            items=[],
            total=0,
            page=request.page,
            per_page=request.per_page,
            pages=0,
            query=request.query,
            provider=request.provider
        )
    
    async def _get_cached_result(
        self,
        request: StockMediaSearchRequest
    ) -> Optional[StockMediaSearchResponse]:
        """Get cached search result"""
        
        db = SessionLocal()
        try:
            cache_key = f"{request.provider}:{request.query}:{request.category}:{request.page}:{request.per_page}"
            
            cached = db.query(StockMediaCache).filter(
                StockMediaCache.query == request.query,
                StockMediaCache.provider == request.provider,
                StockMediaCache.category == request.category,
                StockMediaCache.expires_at > datetime.utcnow()
            ).first()
            
            if cached:
                # Update hit count
                cached.hit_count += 1
                db.commit()
                
                # Parse cached results
                results_data = cached.results
                items = [StockMediaItem(**item) for item in results_data.get("items", [])]
                
                return StockMediaSearchResponse(
                    items=items,
                    total=results_data.get("total", 0),
                    page=request.page,
                    per_page=request.per_page,
                    pages=results_data.get("pages", 0),
                    query=request.query,
                    provider=request.provider
                )
            
            return None
            
        except Exception as e:
            print(f"Cache retrieval error: {e}")
            return None
        finally:
            db.close()
    
    async def _cache_result(
        self,
        request: StockMediaSearchRequest,
        result: StockMediaSearchResponse
    ) -> None:
        """Cache search result"""
        
        db = SessionLocal()
        try:
            # Prepare data for caching
            cache_data = {
                "items": [item.dict() for item in result.items],
                "total": result.total,
                "pages": result.pages
            }
            
            # Create cache entry
            cache_entry = StockMediaCache(
                query=request.query,
                provider=request.provider,
                category=request.category,
                results=cache_data,
                total_results=result.total,
                expires_at=datetime.utcnow() + self.cache_duration
            )
            
            db.add(cache_entry)
            db.commit()
            
        except Exception as e:
            print(f"Cache storage error: {e}")
        finally:
            db.close()
    
    async def download_media(
        self,
        media_item: StockMediaItem,
        save_path: str
    ) -> bool:
        """Download media file"""
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(media_item.download_url) as response:
                    if response.status == 200:
                        with open(save_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                f.write(chunk)
                        return True
                    else:
                        return False
                        
        except Exception as e:
            print(f"Download error: {e}")
            return False
    
    async def get_trending_searches(self, provider: str) -> List[str]:
        """Get trending search terms"""
        
        # This would typically come from the API providers
        # For now, return some common search terms
        trending_terms = [
            "business", "technology", "education", "nature", "people",
            "office", "computer", "meeting", "presentation", "teamwork",
            "success", "growth", "innovation", "creativity", "leadership"
        ]
        
        return trending_terms
    
    async def get_categories(self, provider: str) -> List[str]:
        """Get available categories for a provider"""
        
        if provider == "pexels":
            return [
                "nature", "people", "technology", "business", "food",
                "travel", "architecture", "animals", "sports", "fashion"
            ]
        elif provider == "pixabay":
            return [
                "backgrounds", "fashion", "nature", "science", "education",
                "feelings", "health", "people", "religion", "places",
                "animals", "industry", "computer", "food", "sports",
                "transportation", "travel", "buildings", "business", "music"
            ]
        else:
            return []
