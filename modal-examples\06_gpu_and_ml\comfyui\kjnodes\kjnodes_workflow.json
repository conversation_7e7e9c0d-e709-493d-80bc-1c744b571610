{"last_node_id": 4, "last_link_id": 3, "nodes": [{"id": 1, "type": "LoadImage", "pos": {"0": 425, "1": 151}, "size": [315, 314], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["mood.jpg", "image"]}, {"id": 2, "type": "LoadImage", "pos": {"0": 486, "1": 558}, "size": [315, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [2], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["IMG_4651.jpeg", "image"]}, {"id": 3, "type": "ColorMatch", "pos": {"0": 905, "1": 377}, "size": {"0": 315, "1": 102}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "image_ref", "type": "IMAGE", "link": 1}, {"name": "image_target", "type": "IMAGE", "link": 2}], "outputs": [{"name": "image", "type": "IMAGE", "links": [3], "slot_index": 0}], "properties": {"Node name for S&R": "ColorMatch"}, "widgets_values": ["mkl", 1]}, {"id": 4, "type": "SaveImage", "pos": {"0": 1301, "1": 409}, "size": [315, 270], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 3}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}], "links": [[1, 1, 0, 3, 0, "IMAGE"], [2, 2, 0, 3, 1, "IMAGE"], [3, 3, 0, 4, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1, "offset": [0, 0]}}, "version": 0.4}