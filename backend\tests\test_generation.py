"""
Course generation endpoint tests
"""

import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session

from app.models.media import GenerationJob


class TestGenerationEndpoints:
    """Test course generation endpoints"""
    
    @patch('app.services.generation.CourseGenerationService.generate_course')
    def test_start_course_generation_success(self, mock_generate, client: TestClient, auth_headers: dict, sample_generation_request: dict):
        """Test successful course generation start"""
        mock_generate.return_value = AsyncMock()
        
        response = client.post("/api/v1/generation/course", json=sample_generation_request, headers=auth_headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["job_type"] == "course_generation"
        assert data["status"] == "pending"
        assert "id" in data
        assert "created_at" in data
    
    def test_start_course_generation_unauthorized(self, client: TestClient, sample_generation_request: dict):
        """Test course generation without authentication"""
        response = client.post("/api/v1/generation/course", json=sample_generation_request)
        
        assert response.status_code == 401
    
    def test_start_course_generation_invalid_data(self, client: TestClient, auth_headers: dict):
        """Test course generation with invalid data"""
        invalid_data = {
            "title": "",  # Empty title
            "duration_minutes": 0,  # Invalid duration
            "topics": []  # Empty topics
        }
        
        response = client.post("/api/v1/generation/course", json=invalid_data, headers=auth_headers)
        
        assert response.status_code == 422
    
    @patch('app.services.generation.CourseGenerationService.generate_avatar_course')
    def test_start_avatar_generation_success(self, mock_generate, client: TestClient, auth_headers: dict, sample_generation_request: dict):
        """Test successful avatar course generation start"""
        mock_generate.return_value = AsyncMock()
        
        # Add avatar-specific fields
        avatar_request = {
            **sample_generation_request,
            "avatar_style": "professional",
            "avatar_gender": "neutral",
            "avatar_age": "adult",
            "background_style": "office"
        }
        
        response = client.post("/api/v1/generation/avatar", json=avatar_request, headers=auth_headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["job_type"] == "avatar_generation"
        assert data["status"] == "pending"
    
    def test_get_generation_jobs(self, client: TestClient, auth_headers: dict, db_session: Session, test_user):
        """Test getting user's generation jobs"""
        # Create a test job
        job = GenerationJob(
            job_type="course_generation",
            status="completed",
            progress_percentage=100.0,
            user_id=test_user.id
        )
        db_session.add(job)
        db_session.commit()
        
        response = client.get("/api/v1/generation/jobs", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        job_data = data[0]
        assert job_data["job_type"] == "course_generation"
        assert job_data["status"] == "completed"
    
    def test_get_generation_jobs_unauthorized(self, client: TestClient):
        """Test getting generation jobs without authentication"""
        response = client.get("/api/v1/generation/jobs")
        
        assert response.status_code == 401
    
    def test_get_generation_job_by_id(self, client: TestClient, auth_headers: dict, db_session: Session, test_user):
        """Test getting specific generation job"""
        job = GenerationJob(
            job_type="course_generation",
            status="processing",
            progress_percentage=50.0,
            user_id=test_user.id
        )
        db_session.add(job)
        db_session.commit()
        db_session.refresh(job)
        
        response = client.get(f"/api/v1/generation/jobs/{job.id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(job.id)
        assert data["status"] == "processing"
        assert data["progress_percentage"] == 50.0
    
    def test_get_generation_job_not_found(self, client: TestClient, auth_headers: dict):
        """Test getting nonexistent generation job"""
        fake_id = "00000000-0000-0000-0000-000000000000"
        response = client.get(f"/api/v1/generation/jobs/{fake_id}", headers=auth_headers)
        
        assert response.status_code == 404
    
    def test_get_generation_job_not_owner(self, client: TestClient, db_session: Session, test_user):
        """Test getting generation job by non-owner"""
        # Create another user
        from app.models.user import User
        other_user = User(
            email="<EMAIL>",
            username="otheruser",
            full_name="Other User",
            hashed_password="hashed_password"
        )
        db_session.add(other_user)
        db_session.commit()
        
        # Create job for other user
        job = GenerationJob(
            job_type="course_generation",
            status="completed",
            user_id=other_user.id
        )
        db_session.add(job)
        db_session.commit()
        db_session.refresh(job)
        
        # Try to access with test_user credentials
        from app.core.security import create_access_token
        token = create_access_token(data={"sub": str(test_user.id)})
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.get(f"/api/v1/generation/jobs/{job.id}", headers=headers)
        
        assert response.status_code == 404
    
    def test_cancel_generation_job_success(self, client: TestClient, auth_headers: dict, db_session: Session, test_user):
        """Test successful job cancellation"""
        job = GenerationJob(
            job_type="course_generation",
            status="pending",
            progress_percentage=0.0,
            user_id=test_user.id
        )
        db_session.add(job)
        db_session.commit()
        db_session.refresh(job)
        
        response = client.delete(f"/api/v1/generation/jobs/{job.id}", headers=auth_headers)
        
        assert response.status_code == 200
        
        # Verify job was cancelled
        db_session.refresh(job)
        assert job.status == "cancelled"
    
    def test_cancel_completed_job(self, client: TestClient, auth_headers: dict, db_session: Session, test_user):
        """Test cancelling already completed job"""
        job = GenerationJob(
            job_type="course_generation",
            status="completed",
            progress_percentage=100.0,
            user_id=test_user.id
        )
        db_session.add(job)
        db_session.commit()
        db_session.refresh(job)
        
        response = client.delete(f"/api/v1/generation/jobs/{job.id}", headers=auth_headers)
        
        assert response.status_code == 400
        assert "Cannot cancel completed or failed job" in response.json()["detail"]
    
    @patch('app.services.generation.CourseGenerationService.regenerate_course')
    def test_regenerate_course_content(self, mock_regenerate, client: TestClient, auth_headers: dict, test_course):
        """Test course content regeneration"""
        mock_regenerate.return_value = AsyncMock()
        
        response = client.post(f"/api/v1/generation/regenerate/{test_course.id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["job_type"] == "course_regeneration"
        assert data["status"] == "pending"
    
    def test_regenerate_course_not_found(self, client: TestClient, auth_headers: dict):
        """Test regenerating nonexistent course"""
        fake_id = "00000000-0000-0000-0000-000000000000"
        response = client.post(f"/api/v1/generation/regenerate/{fake_id}", headers=auth_headers)
        
        assert response.status_code == 404
