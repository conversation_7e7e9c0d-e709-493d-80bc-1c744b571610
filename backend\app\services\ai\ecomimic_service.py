"""
Ecomimic V2 service for avatar generation
"""

import aiohttp
import asyncio
import aiofiles
from typing import Dict, Any, List, Optional
from pathlib import Path
import uuid
import json

from app.core.config import settings
from app.schemas.generation import AvatarGenerationRequest


class EcomimicService:
    """Service for avatar generation using Ecomimic V2"""
    
    def __init__(self):
        self.api_url = settings.ECOMIMIC_API_URL
        self.api_key = settings.ECOMIMIC_API_KEY
        self.output_dir = Path("media/generated/avatars")
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def generate_avatar_video(
        self,
        lesson_id: str,
        script: str,
        avatar_settings: Dict[str, Any]
    ) -> Optional[str]:
        """Generate avatar video for a lesson"""
        
        try:
            # Prepare request data
            request_data = {
                "script": script,
                "avatar_style": avatar_settings.get("style", "professional"),
                "avatar_gender": avatar_settings.get("gender", "neutral"),
                "avatar_age": avatar_settings.get("age", "adult"),
                "background": avatar_settings.get("background", "office"),
                "voice_settings": avatar_settings.get("voice_settings", {}),
                "output_format": "mp4",
                "resolution": "1920x1080",
                "fps": 30
            }
            
            # Generate avatar video
            job_id = await self._start_avatar_generation(request_data)
            
            if job_id:
                # Wait for completion and download result
                video_file = await self._wait_for_completion(job_id, lesson_id)
                return video_file
            
            return None
            
        except Exception as e:
            print(f"Error generating avatar video for lesson {lesson_id}: {e}")
            return None
    
    async def _start_avatar_generation(
        self,
        request_data: Dict[str, Any]
    ) -> Optional[str]:
        """Start avatar generation job"""
        
        if not self.api_key:
            raise ValueError("Ecomimic API key not configured")
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_url}/api/v2/generate",
                    json=request_data,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        return data.get("job_id")
                    else:
                        error_text = await response.text()
                        raise Exception(f"Ecomimic API error: {response.status} - {error_text}")
                        
        except Exception as e:
            print(f"Error starting avatar generation: {e}")
            return None
    
    async def _wait_for_completion(
        self,
        job_id: str,
        lesson_id: str,
        max_wait_time: int = 1800  # 30 minutes
    ) -> Optional[str]:
        """Wait for avatar generation to complete"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}"
        }
        
        start_time = asyncio.get_event_loop().time()
        
        while True:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.api_url}/api/v2/status/{job_id}",
                        headers=headers
                    ) as response:
                        
                        if response.status == 200:
                            data = await response.json()
                            status = data.get("status")
                            
                            if status == "completed":
                                # Download the generated video
                                download_url = data.get("download_url")
                                if download_url:
                                    return await self._download_avatar_video(
                                        download_url, lesson_id, headers
                                    )
                                else:
                                    return None
                            
                            elif status == "failed":
                                error_message = data.get("error", "Unknown error")
                                print(f"Avatar generation failed: {error_message}")
                                return None
                            
                            elif status in ["pending", "processing"]:
                                # Check if we've exceeded max wait time
                                elapsed_time = asyncio.get_event_loop().time() - start_time
                                if elapsed_time > max_wait_time:
                                    print(f"Avatar generation timeout for job {job_id}")
                                    return None
                                
                                # Wait before checking again
                                await asyncio.sleep(30)
                                continue
                            
                        else:
                            print(f"Error checking status: {response.status}")
                            return None
                            
            except Exception as e:
                print(f"Error waiting for completion: {e}")
                return None
    
    async def _download_avatar_video(
        self,
        download_url: str,
        lesson_id: str,
        headers: Dict[str, str]
    ) -> Optional[str]:
        """Download generated avatar video"""
        
        try:
            filename = f"avatar_lesson_{lesson_id}_{uuid.uuid4().hex[:8]}.mp4"
            file_path = self.output_dir / filename
            
            async with aiohttp.ClientSession() as session:
                async with session.get(download_url, headers=headers) as response:
                    if response.status == 200:
                        async with aiofiles.open(file_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        return str(file_path)
                    else:
                        print(f"Error downloading avatar video: {response.status}")
                        return None
                        
        except Exception as e:
            print(f"Error downloading avatar video: {e}")
            return None
    
    async def get_available_avatars(self) -> List[Dict[str, Any]]:
        """Get list of available avatar styles"""
        
        if not self.api_key:
            return []
        
        headers = {
            "Authorization": f"Bearer {self.api_key}"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/api/v2/avatars",
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        return data.get("avatars", [])
                    else:
                        return []
                        
        except Exception as e:
            print(f"Error fetching available avatars: {e}")
            return []
    
    async def get_available_backgrounds(self) -> List[Dict[str, Any]]:
        """Get list of available background styles"""
        
        if not self.api_key:
            return []
        
        headers = {
            "Authorization": f"Bearer {self.api_key}"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.api_url}/api/v2/backgrounds",
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        return data.get("backgrounds", [])
                    else:
                        return []
                        
        except Exception as e:
            print(f"Error fetching available backgrounds: {e}")
            return []
    
    async def create_custom_avatar(
        self,
        avatar_config: Dict[str, Any]
    ) -> Optional[str]:
        """Create a custom avatar configuration"""
        
        if not self.api_key:
            raise ValueError("Ecomimic API key not configured")
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_url}/api/v2/avatars/custom",
                    json=avatar_config,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        return data.get("avatar_id")
                    else:
                        error_text = await response.text()
                        raise Exception(f"Error creating custom avatar: {response.status} - {error_text}")
                        
        except Exception as e:
            print(f"Error creating custom avatar: {e}")
            return None
    
    async def generate_avatar_preview(
        self,
        avatar_id: str,
        sample_text: str = "Hello, welcome to this course!"
    ) -> Optional[str]:
        """Generate a preview of an avatar"""
        
        if not self.api_key:
            raise ValueError("Ecomimic API key not configured")
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        request_data = {
            "avatar_id": avatar_id,
            "text": sample_text,
            "duration": 10,  # 10 second preview
            "output_format": "mp4"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_url}/api/v2/preview",
                    json=request_data,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        job_id = data.get("job_id")
                        
                        if job_id:
                            # Wait for preview completion (shorter timeout)
                            return await self._wait_for_completion(
                                job_id, f"preview_{uuid.uuid4().hex[:8]}", max_wait_time=300
                            )
                    
                    return None
                    
        except Exception as e:
            print(f"Error generating avatar preview: {e}")
            return None
