# Secure Modal Deployment Guide for Koursia AI Services

## 🚨 CRITICAL SECURITY WARNING

**NEVER share API keys or credentials in plain text, chat messages, or version control.**

This guide provides secure deployment procedures using placeholder values and proper secrets management.

## Prerequisites

### 1. Modal Account Setup
- Modal account with GPU access
- Modal CLI installed: `pip install modal`
- A100 80GB GPU quota available

### 2. Required Credentials (Keep Secure)
- Modal authentication token
- AWS credentials for S3 storage
- OpenAI API key
- Google Gemini API key
- Pexels API key
- Pixabay API key
- TTS service API keys (Chatterbox, Kokoro)
- Ecomimic V2 API key

## Step 1: Secure Modal Authentication

### 1.1 Set Modal Token (Replace with your actual credentials)
```bash
# SECURITY: Replace with your actual token values
modal token set \
  --token-id YOUR_ACTUAL_TOKEN_ID \
  --token-secret YOUR_ACTUAL_TOKEN_SECRET \
  --profile koursia-production

# Activate the profile
modal profile activate koursia-production

# Verify authentication
modal token current
```

### 1.2 Verify GPU Access
```bash
# Check GPU quota and availability
modal quota
modal gpu list
```

## Step 2: Secure Secrets Management

### 2.1 Create Modal Secrets (Replace placeholders with actual values)

**AWS Credentials:**
```bash
modal secret create aws-credentials \
  AWS_ACCESS_KEY_ID=YOUR_ACTUAL_AWS_ACCESS_KEY \
  AWS_SECRET_ACCESS_KEY=YOUR_ACTUAL_AWS_SECRET_KEY \
  AWS_S3_BUCKET=YOUR_ACTUAL_S3_BUCKET \
  AWS_REGION=us-east-1
```

**OpenAI API Key:**
```bash
modal secret create openai-api-key \
  OPENAI_API_KEY=YOUR_ACTUAL_OPENAI_API_KEY
```

**Google Gemini API Key:**
```bash
modal secret create gemini-api-key \
  GEMINI_API_KEY=YOUR_ACTUAL_GEMINI_API_KEY
```

**Stock Media APIs:**
```bash
modal secret create pexels-api-key \
  PEXELS_API_KEY=YOUR_ACTUAL_PEXELS_API_KEY

modal secret create pixabay-api-key \
  PIXABAY_API_KEY=YOUR_ACTUAL_PIXABAY_API_KEY
```

**TTS Services:**
```bash
modal secret create chatterbox-api-key \
  CHATTERBOX_API_KEY=YOUR_ACTUAL_CHATTERBOX_API_KEY

modal secret create kokoro-api-key \
  KOKORO_API_KEY=YOUR_ACTUAL_KOKORO_API_KEY
```

**Ecomimic V2:**
```bash
modal secret create ecomimic-api-key \
  ECOMIMIC_API_KEY=YOUR_ACTUAL_ECOMIMIC_API_KEY
```

### 2.2 Verify Secrets
```bash
# List all secrets (values are hidden for security)
modal secret list

# Test secret access without revealing values
modal run --secret aws-credentials -- python -c "import os; print('AWS configured:', bool(os.getenv('AWS_ACCESS_KEY_ID')))"
```

## Step 3: Deploy AI Services

### 3.1 Deploy Whisper Service
```bash
cd modal-services
modal deploy whisper_service.py
```

### 3.2 Deploy TTS Service
```bash
modal deploy tts_service.py
```

### 3.3 Deploy Ecomimic Service
```bash
modal deploy ecomimic_service.py
```

### 3.4 Verify Deployments
```bash
# Check deployed apps
modal app list

# Test health endpoints
modal run whisper_service.py::whisper_health
modal run tts_service.py::tts_health
modal run ecomimic_service.py::ecomimic_health
```

## Step 4: Get Service Endpoints

### 4.1 Retrieve Service URLs
```bash
# Get app details with endpoints
modal app list --json | jq '.[] | select(.name=="koursia-ai-services")'
```

### 4.2 Update Backend Configuration

Add the following to your `.env` file (replace with actual URLs):
```bash
# Modal AI Services (Replace with actual URLs from deployment)
MODAL_WHISPER_URL=https://YOUR_ACTUAL_WHISPER_WEBHOOK_URL
MODAL_TTS_URL=https://YOUR_ACTUAL_TTS_WEBHOOK_URL
MODAL_ECOMIMIC_URL=https://YOUR_ACTUAL_ECOMIMIC_WEBHOOK_URL
USE_MODAL_SERVICES=true
MODAL_FALLBACK_TO_LOCAL=true
```

## Step 5: Test Integration

### 5.1 Test Whisper Service
```bash
# Test audio transcription
curl -X POST "YOUR_WHISPER_WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "transcribe",
    "audio_url": "https://example.com/test-audio.mp3",
    "model": "base"
  }'
```

### 5.2 Test TTS Service
```bash
# Test text-to-speech
curl -X POST "YOUR_TTS_WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "generate",
    "text": "Hello, this is a test.",
    "voice_settings": {
      "provider": "chatterbox",
      "voice_id": "en-US-AriaNeural"
    }
  }'
```

### 5.3 Test Ecomimic Service
```bash
# Test avatar generation
curl -X POST "YOUR_ECOMIMIC_WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "generate",
    "lesson_id": "test-lesson-001",
    "script": "Welcome to this test lesson!",
    "avatar_settings": {
      "style": "professional",
      "background": "office"
    }
  }'
```

## Step 6: Security Hardening

### 6.1 Network Security
- All Modal services use HTTPS by default
- Webhook endpoints are publicly accessible but should validate requests
- Consider implementing API key authentication for webhook endpoints

### 6.2 Access Control
```bash
# Monitor Modal usage
modal usage

# Set up billing alerts
modal billing alerts create --threshold 100 --email <EMAIL>
```

### 6.3 Credential Rotation
- Rotate AWS keys every 90 days
- Rotate API keys every 6 months
- Monitor for unauthorized usage
- Use different credentials for dev/staging/production

## Step 7: Monitoring and Alerting

### 7.1 Set Up Monitoring
```bash
# Create monitoring script
cat > monitor-modal-services.py << 'EOF'
import modal
import requests
import time

@modal.Function.from_name("koursia-ai-services", "monitor")
def check_services():
    services = {
        "whisper": "YOUR_WHISPER_HEALTH_URL",
        "tts": "YOUR_TTS_HEALTH_URL", 
        "ecomimic": "YOUR_ECOMIMIC_HEALTH_URL"
    }
    
    for name, url in services.items():
        try:
            response = requests.get(url, timeout=10)
            print(f"{name}: {'✅' if response.status_code == 200 else '❌'}")
        except Exception as e:
            print(f"{name}: ❌ Error: {e}")

if __name__ == "__main__":
    with modal.run():
        check_services.remote()
EOF

# Run monitoring
python monitor-modal-services.py
```

### 7.2 Cost Monitoring
```bash
# Check current usage and costs
modal usage --json

# Set up cost alerts
modal billing alerts create \
  --threshold 500 \
  --email <EMAIL> \
  --name "Koursia AI Services Cost Alert"
```

## Step 8: Troubleshooting

### 8.1 Common Issues

**Service Not Responding:**
```bash
# Check service logs
modal logs whisper_service.py::whisper_webhook --follow

# Restart service
modal app restart koursia-ai-services
```

**GPU Memory Issues:**
```bash
# Check GPU utilization
modal gpu usage

# Reduce batch sizes in service configuration
```

**Authentication Errors:**
```bash
# Verify secrets
modal secret list

# Test secret access
modal run --secret openai-api-key -- python -c "import os; print(bool(os.getenv('OPENAI_API_KEY')))"
```

### 8.2 Performance Optimization

**Reduce Cold Starts:**
- Increase `keep_warm` instances
- Use smaller models for faster loading
- Implement model caching

**Cost Optimization:**
- Use preemptible instances
- Implement auto-scaling
- Monitor usage patterns

## Security Checklist

- [ ] All credentials stored as Modal secrets
- [ ] No credentials in version control
- [ ] Different credentials for each environment
- [ ] Billing alerts configured
- [ ] Usage monitoring enabled
- [ ] Regular credential rotation scheduled
- [ ] Access logs monitored
- [ ] Network security configured
- [ ] Backup and recovery plan in place
- [ ] Incident response plan documented

## Support and Resources

- **Modal Documentation**: https://modal.com/docs
- **Modal Discord**: https://discord.gg/modal
- **Koursia Support**: <EMAIL>
- **Security Issues**: <EMAIL>

## Emergency Procedures

### Service Outage
1. Check Modal status page
2. Verify service health endpoints
3. Check Modal logs for errors
4. Fallback to local services if configured
5. Contact Modal support if needed

### Security Incident
1. Immediately rotate all affected credentials
2. Review access logs
3. Update Modal secrets
4. Notify security team
5. Document incident for review

---

**Remember: Security is everyone's responsibility. Always follow the principle of least privilege and never share credentials.**
