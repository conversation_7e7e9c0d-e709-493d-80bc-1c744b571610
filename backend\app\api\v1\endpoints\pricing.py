"""
Pricing and subscription API endpoints
"""

from typing import List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.core.config import settings
from app.models.user import User
from app.models.subscription import Plan, Subscription, PlanType, BillingCycle, SubscriptionStatus
from app.schemas.pricing import (
    PlanResponse,
    SubscriptionResponse,
    SubscriptionCreate,
    SubscriptionUpdate,
    UsageResponse,
    PricingCalculation,
)
from app.services.subscription_service import SubscriptionService
from app.services.billing_service import BillingService

router = APIRouter()


@router.get("/plans", response_model=List[PlanResponse])
def get_plans(
    *,
    db: Session = Depends(deps.get_db),
    include_inactive: bool = False,
) -> List[Plan]:
    """
    Get all available subscription plans
    """
    query = db.query(Plan)
    
    if not include_inactive:
        query = query.filter(Plan.is_active == True)
    
    plans = query.order_by(Plan.monthly_price).all()
    return plans


@router.get("/plans/{plan_id}", response_model=PlanResponse)
def get_plan(
    *,
    db: Session = Depends(deps.get_db),
    plan_id: str,
) -> Plan:
    """
    Get specific plan details
    """
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    return plan


@router.post("/calculate", response_model=PricingCalculation)
def calculate_pricing(
    *,
    db: Session = Depends(deps.get_db),
    plan_id: str,
    billing_cycle: BillingCycle,
    promo_code: Optional[str] = None,
) -> PricingCalculation:
    """
    Calculate pricing for a plan with optional promo code
    """
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    billing_service = BillingService(db)
    calculation = billing_service.calculate_pricing(
        plan=plan,
        billing_cycle=billing_cycle,
        promo_code=promo_code,
    )
    
    return calculation


@router.get("/subscription", response_model=Optional[SubscriptionResponse])
def get_current_subscription(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Optional[Subscription]:
    """
    Get current user's active subscription
    """
    subscription_service = SubscriptionService(db)
    subscription = subscription_service.get_active_subscription(current_user.id)
    return subscription


@router.post("/subscription", response_model=SubscriptionResponse)
def create_subscription(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    subscription_in: SubscriptionCreate,
) -> Subscription:
    """
    Create a new subscription for the current user
    """
    # Check if user already has an active subscription
    subscription_service = SubscriptionService(db)
    existing_subscription = subscription_service.get_active_subscription(current_user.id)
    
    if existing_subscription:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already has an active subscription"
        )
    
    # Validate plan
    plan = db.query(Plan).filter(Plan.id == subscription_in.plan_id).first()
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    # Create subscription
    subscription = subscription_service.create_subscription(
        user_id=current_user.id,
        plan_id=subscription_in.plan_id,
        billing_cycle=subscription_in.billing_cycle,
        payment_method_id=subscription_in.payment_method_id,
        promo_code=subscription_in.promo_code,
    )
    
    return subscription


@router.put("/subscription", response_model=SubscriptionResponse)
def update_subscription(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    subscription_in: SubscriptionUpdate,
) -> Subscription:
    """
    Update current user's subscription
    """
    subscription_service = SubscriptionService(db)
    subscription = subscription_service.get_active_subscription(current_user.id)
    
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No active subscription found"
        )
    
    # Update subscription
    updated_subscription = subscription_service.update_subscription(
        subscription_id=subscription.id,
        **subscription_in.dict(exclude_unset=True)
    )
    
    return updated_subscription


@router.post("/subscription/cancel")
def cancel_subscription(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    immediate: bool = False,
) -> dict:
    """
    Cancel current user's subscription
    """
    subscription_service = SubscriptionService(db)
    subscription = subscription_service.get_active_subscription(current_user.id)
    
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No active subscription found"
        )
    
    # Cancel subscription
    canceled_subscription = subscription_service.cancel_subscription(
        subscription_id=subscription.id,
        immediate=immediate,
    )
    
    return {
        "message": "Subscription canceled successfully",
        "subscription_id": str(canceled_subscription.id),
        "end_date": canceled_subscription.end_date.isoformat() if canceled_subscription.end_date else None,
    }


@router.post("/subscription/reactivate", response_model=SubscriptionResponse)
def reactivate_subscription(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Subscription:
    """
    Reactivate a canceled subscription
    """
    subscription_service = SubscriptionService(db)
    
    # Find the most recent canceled subscription
    canceled_subscription = (
        db.query(Subscription)
        .filter(
            Subscription.user_id == current_user.id,
            Subscription.status == SubscriptionStatus.CANCELED,
        )
        .order_by(Subscription.canceled_at.desc())
        .first()
    )
    
    if not canceled_subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No canceled subscription found"
        )
    
    # Reactivate subscription
    reactivated_subscription = subscription_service.reactivate_subscription(
        subscription_id=canceled_subscription.id
    )
    
    return reactivated_subscription


@router.get("/usage", response_model=UsageResponse)
def get_usage(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> UsageResponse:
    """
    Get current user's usage statistics
    """
    subscription_service = SubscriptionService(db)
    usage = subscription_service.get_usage_statistics(current_user.id)
    return usage


@router.post("/trial/start", response_model=SubscriptionResponse)
def start_trial(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    plan_id: str,
) -> Subscription:
    """
    Start a free trial for a plan
    """
    # Check if user already has an active subscription or trial
    subscription_service = SubscriptionService(db)
    existing_subscription = subscription_service.get_active_subscription(current_user.id)
    
    if existing_subscription:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already has an active subscription or trial"
        )
    
    # Check if user has already used a trial
    has_used_trial = subscription_service.has_used_trial(current_user.id)
    if has_used_trial:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User has already used a trial"
        )
    
    # Validate plan
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    if plan.trial_days <= 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Plan does not offer a trial"
        )
    
    # Start trial
    trial_subscription = subscription_service.start_trial(
        user_id=current_user.id,
        plan_id=plan_id,
    )
    
    return trial_subscription


@router.get("/invoices", response_model=List[dict])
def get_invoices(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    limit: int = 10,
    offset: int = 0,
) -> List[dict]:
    """
    Get user's billing invoices
    """
    billing_service = BillingService(db)
    invoices = billing_service.get_user_invoices(
        user_id=current_user.id,
        limit=limit,
        offset=offset,
    )
    return invoices


@router.get("/invoices/{invoice_id}")
def get_invoice(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    invoice_id: str,
) -> dict:
    """
    Get specific invoice details
    """
    billing_service = BillingService(db)
    invoice = billing_service.get_invoice(
        invoice_id=invoice_id,
        user_id=current_user.id,
    )
    
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    return invoice


@router.post("/webhooks/stripe")
async def stripe_webhook(
    *,
    db: Session = Depends(deps.get_db),
    request: Any,  # Raw request for webhook verification
) -> dict:
    """
    Handle Stripe webhooks for billing events
    """
    billing_service = BillingService(db)
    
    try:
        # Verify webhook signature and process event
        result = await billing_service.handle_stripe_webhook(request)
        return {"status": "success", "processed": result}
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Webhook processing failed: {str(e)}"
        )


@router.get("/features", response_model=dict)
def get_plan_features(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> dict:
    """
    Get current user's plan features and limits
    """
    subscription_service = SubscriptionService(db)
    features = subscription_service.get_user_features(current_user.id)
    return features
