"""
Modal Ecomimic service for avatar generation
"""

import modal
import tempfile
import json
import asyncio
from pathlib import Path
from typing import Dict, Any, List, Optional
import aiofiles
import aiohttp
from datetime import datetime
import base64
import uuid

from modal_config import (
    app,
    ecomimic_image,
    gpu_function,
    webhook_config,
    models_volume,
    temp_volume,
    MODEL_CONFIGS,
    ENVIRONMENT_VARS,
)

@app.function(
    image=ecomimic_image,
    gpu=modal.gpu.A100(count=1, memory=80),
    volumes={
        "/models": models_volume,
        "/tmp": temp_volume,
    },
    secrets=list(ENVIRONMENT_VARS.values()),
    timeout=3600,  # 1 hour for avatar generation
    keep_warm=1,
)
class EcomimicService:
    """Ecomimic V2 avatar generation service"""
    
    def __init__(self):
        self.ecomimic_config = MODEL_CONFIGS["ecomimic"]
        self.output_dir = Path("/tmp/avatars")
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    @modal.enter()
    def setup_ecomimic(self):
        """Setup Ecomimic service"""
        print("Setting up Ecomimic service...")
        
        # Initialize any required models or configurations
        print("✅ Ecomimic service ready")
    
    @modal.method()
    async def generate_avatar_video(
        self,
        lesson_id: str,
        script: str,
        avatar_settings: Dict[str, Any],
        audio_url: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Generate avatar video for a lesson
        
        Args:
            lesson_id: Unique lesson identifier
            script: Text script for the avatar to speak
            avatar_settings: Avatar configuration
            audio_url: Pre-generated audio URL (optional)
        
        Returns:
            Avatar video generation result
        """
        try:
            print(f"Starting avatar generation for lesson {lesson_id}")
            
            # Prepare request data for Ecomimic API
            request_data = {
                "script": script,
                "avatar_style": avatar_settings.get("style", "professional"),
                "avatar_gender": avatar_settings.get("gender", "neutral"),
                "avatar_age": avatar_settings.get("age", "adult"),
                "background": avatar_settings.get("background", "office"),
                "voice_settings": avatar_settings.get("voice_settings", {}),
                "output_format": "mp4",
                "resolution": avatar_settings.get("resolution", "1920x1080"),
                "fps": avatar_settings.get("fps", 30),
                "audio_url": audio_url,
            }
            
            # Start avatar generation job
            job_id = await self._start_avatar_generation(request_data)
            
            if job_id:
                # Wait for completion and get result
                result = await self._wait_for_completion(job_id, lesson_id)
                return result
            else:
                raise Exception("Failed to start avatar generation job")
                
        except Exception as e:
            print(f"❌ Avatar generation failed for lesson {lesson_id}: {e}")
            return {
                "error": str(e),
                "lesson_id": lesson_id,
                "video_url": None,
                "metadata": {
                    "timestamp": datetime.utcnow().isoformat(),
                    "error": True,
                },
            }
    
    async def _start_avatar_generation(
        self,
        request_data: Dict[str, Any],
    ) -> Optional[str]:
        """Start avatar generation job with Ecomimic API"""
        import os
        
        api_key = os.getenv("ECOMIMIC_API_KEY")
        if not api_key:
            raise ValueError("Ecomimic API key not configured")
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    "https://api.ecomimic.com/v2/generate",
                    json=request_data,
                    headers=headers,
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        job_id = data.get("job_id")
                        print(f"✅ Avatar generation job started: {job_id}")
                        return job_id
                    else:
                        error_text = await response.text()
                        raise Exception(f"Ecomimic API error: {response.status} - {error_text}")
                        
        except Exception as e:
            print(f"❌ Failed to start avatar generation: {e}")
            return None
    
    async def _wait_for_completion(
        self,
        job_id: str,
        lesson_id: str,
        max_wait_time: int = 3600,  # 1 hour
    ) -> Dict[str, Any]:
        """Wait for avatar generation to complete"""
        import os
        
        api_key = os.getenv("ECOMIMIC_API_KEY")
        headers = {"Authorization": f"Bearer {api_key}"}
        
        start_time = asyncio.get_event_loop().time()
        
        while True:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"https://api.ecomimic.com/v2/status/{job_id}",
                        headers=headers,
                    ) as response:
                        
                        if response.status == 200:
                            data = await response.json()
                            status = data.get("status")
                            progress = data.get("progress", 0)
                            
                            print(f"Avatar generation progress: {progress}% ({status})")
                            
                            if status == "completed":
                                # Download the generated video
                                download_url = data.get("download_url")
                                if download_url:
                                    video_path = await self._download_avatar_video(
                                        download_url, lesson_id, headers
                                    )
                                    
                                    return {
                                        "video_url": video_path,
                                        "lesson_id": lesson_id,
                                        "job_id": job_id,
                                        "metadata": {
                                            "duration": data.get("duration", 0),
                                            "resolution": data.get("resolution"),
                                            "file_size": data.get("file_size"),
                                            "timestamp": datetime.utcnow().isoformat(),
                                        },
                                    }
                                else:
                                    raise Exception("No download URL provided")
                            
                            elif status == "failed":
                                error_message = data.get("error", "Unknown error")
                                raise Exception(f"Avatar generation failed: {error_message}")
                            
                            elif status in ["pending", "processing"]:
                                # Check if we've exceeded max wait time
                                elapsed_time = asyncio.get_event_loop().time() - start_time
                                if elapsed_time > max_wait_time:
                                    raise Exception(f"Avatar generation timeout for job {job_id}")
                                
                                # Wait before checking again
                                await asyncio.sleep(30)
                                continue
                            
                        else:
                            raise Exception(f"Status check failed: {response.status}")
                            
            except Exception as e:
                print(f"❌ Error waiting for completion: {e}")
                raise e
    
    async def _download_avatar_video(
        self,
        download_url: str,
        lesson_id: str,
        headers: Dict[str, str],
    ) -> str:
        """Download generated avatar video"""
        
        try:
            filename = f"avatar_lesson_{lesson_id}_{uuid.uuid4().hex[:8]}.mp4"
            file_path = self.output_dir / filename
            
            async with aiohttp.ClientSession() as session:
                async with session.get(download_url, headers=headers) as response:
                    if response.status == 200:
                        async with aiofiles.open(file_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        print(f"✅ Avatar video downloaded: {file_path}")
                        return str(file_path)
                    else:
                        raise Exception(f"Download failed: {response.status}")
                        
        except Exception as e:
            print(f"❌ Error downloading avatar video: {e}")
            raise e
    
    @modal.method()
    async def get_available_avatars(self) -> Dict[str, Any]:
        """Get list of available avatar styles"""
        import os
        
        api_key = os.getenv("ECOMIMIC_API_KEY")
        if not api_key:
            return {"error": "Ecomimic API key not configured"}
        
        headers = {"Authorization": f"Bearer {api_key}"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://api.ecomimic.com/v2/avatars",
                    headers=headers,
                ) as response:
                    
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        return {"error": f"API error: {response.status} - {error_text}"}
                        
        except Exception as e:
            return {"error": str(e)}
    
    @modal.method()
    async def get_available_backgrounds(self) -> Dict[str, Any]:
        """Get list of available background styles"""
        import os
        
        api_key = os.getenv("ECOMIMIC_API_KEY")
        if not api_key:
            return {"error": "Ecomimic API key not configured"}
        
        headers = {"Authorization": f"Bearer {api_key}"}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://api.ecomimic.com/v2/backgrounds",
                    headers=headers,
                ) as response:
                    
                    if response.status == 200:
                        return await response.json()
                    else:
                        error_text = await response.text()
                        return {"error": f"API error: {response.status} - {error_text}"}
                        
        except Exception as e:
            return {"error": str(e)}
    
    @modal.method()
    async def generate_avatar_preview(
        self,
        avatar_settings: Dict[str, Any],
        sample_text: str = "Hello, welcome to this course!",
    ) -> Dict[str, Any]:
        """Generate a preview of an avatar"""
        
        try:
            preview_id = f"preview_{uuid.uuid4().hex[:8]}"
            
            # Use shorter text and lower quality for preview
            preview_settings = {
                **avatar_settings,
                "resolution": "720x480",
                "fps": 24,
                "quality": "preview",
            }
            
            result = await self.generate_avatar_video(
                lesson_id=preview_id,
                script=sample_text,
                avatar_settings=preview_settings,
            )
            
            return result
            
        except Exception as e:
            return {"error": str(e)}

# Create webhook endpoint
@app.function(
    image=ecomimic_image,
    gpu=modal.gpu.A100(count=1, memory=80),
    secrets=list(ENVIRONMENT_VARS.values()),
    timeout=3600,
    keep_warm=1,
)
@modal.web_endpoint(**webhook_config)
async def ecomimic_webhook(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """Webhook endpoint for Ecomimic avatar generation"""
    
    service = EcomimicService()
    
    action = request_data.get("action", "generate")
    
    if action == "generate":
        return await service.generate_avatar_video(
            lesson_id=request_data["lesson_id"],
            script=request_data["script"],
            avatar_settings=request_data.get("avatar_settings", {}),
            audio_url=request_data.get("audio_url"),
        )
    
    elif action == "preview":
        return await service.generate_avatar_preview(
            avatar_settings=request_data.get("avatar_settings", {}),
            sample_text=request_data.get("sample_text", "Hello, welcome to this course!"),
        )
    
    elif action == "avatars":
        return await service.get_available_avatars()
    
    elif action == "backgrounds":
        return await service.get_available_backgrounds()
    
    else:
        return {"error": f"Unknown action: {action}"}

# Health check endpoint
@app.function(image=ecomimic_image)
@modal.web_endpoint(method="GET")
def ecomimic_health() -> Dict[str, Any]:
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "ecomimic",
        "timestamp": datetime.utcnow().isoformat(),
        "avatar_styles": MODEL_CONFIGS["ecomimic"]["avatar_styles"],
    }
