# Ecomimic V2 Setup Guide

This guide provides detailed instructions for setting up Ecomimic V2 for avatar-based course creation in the Koursia platform.

## Prerequisites

### Hardware Requirements
- **GPU**: NVIDIA A100-80GB (recommended) or equivalent high-end GPU
- **VRAM**: Minimum 24GB, recommended 80GB for optimal performance
- **RAM**: Minimum 64GB system RAM
- **Storage**: 500GB+ SSD for model storage and temporary files
- **CPU**: High-performance multi-core processor (Intel Xeon or AMD EPYC)

### Software Requirements
- **Operating System**: Ubuntu 20.04 LTS or later
- **CUDA**: Version 11.8 or later
- **Docker**: Version 20.10 or later with GPU support
- **NVIDIA Container Toolkit**: For GPU access in containers

## Installation Steps

### 1. System Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install required system packages
sudo apt install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    python3-dev \
    python3-pip
```

### 2. NVIDIA Driver and CUDA Installation

```bash
# Install NVIDIA drivers (if not already installed)
sudo apt install -y nvidia-driver-525

# Download and install CUDA 11.8
wget https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda_11.8.0_520.61.05_linux.run
sudo sh cuda_11.8.0_520.61.05_linux.run

# Add CUDA to PATH
echo 'export PATH=/usr/local/cuda-11.8/bin:$PATH' >> ~/.bashrc
echo 'export LD_LIBRARY_PATH=/usr/local/cuda-11.8/lib64:$LD_LIBRARY_PATH' >> ~/.bashrc
source ~/.bashrc
```

### 3. Docker GPU Support

```bash
# Install Docker (if not already installed)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install NVIDIA Container Toolkit
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt update
sudo apt install -y nvidia-container-toolkit
sudo systemctl restart docker

# Test GPU access in Docker
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi
```

### 4. Ecomimic V2 Installation

#### Option A: Docker Installation (Recommended)

```bash
# Create Ecomimic directory
mkdir -p /opt/ecomimic-v2
cd /opt/ecomimic-v2

# Download Ecomimic V2 Docker image
docker pull ecomimic/ecomimic-v2:latest

# Create configuration directory
mkdir -p config models data

# Download pre-trained models (this may take several hours)
docker run --rm --gpus all \
    -v $(pwd)/models:/app/models \
    ecomimic/ecomimic-v2:latest \
    python download_models.py
```

#### Option B: Source Installation

```bash
# Clone Ecomimic V2 repository
git clone https://github.com/ecomimic/ecomimic-v2.git
cd ecomimic-v2

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Download models
python scripts/download_models.py
```

### 5. Configuration

#### Create Ecomimic Configuration File

```bash
# Create config.yaml
cat > /opt/ecomimic-v2/config/config.yaml << EOF
# Ecomimic V2 Configuration

# Server settings
server:
  host: "0.0.0.0"
  port: 8003
  workers: 1

# GPU settings
gpu:
  device: "cuda:0"
  memory_fraction: 0.9
  allow_growth: true

# Model settings
models:
  base_model: "ecomimic-v2-base"
  voice_model: "ecomimic-v2-voice"
  face_model: "ecomimic-v2-face"
  
# Generation settings
generation:
  max_duration: 1800  # 30 minutes
  default_fps: 30
  default_resolution: "1920x1080"
  batch_size: 1

# Storage settings
storage:
  temp_dir: "/tmp/ecomimic"
  output_dir: "/app/output"
  cleanup_after: 3600  # 1 hour

# API settings
api:
  max_concurrent_jobs: 2
  job_timeout: 1800
  rate_limit: "10/minute"

# Logging
logging:
  level: "INFO"
  file: "/app/logs/ecomimic.log"
EOF
```

#### Create Docker Compose Configuration

```bash
# Create docker-compose.ecomimic.yml
cat > docker-compose.ecomimic.yml << EOF
version: '3.8'

services:
  ecomimic-v2:
    image: ecomimic/ecomimic-v2:latest
    container_name: koursia-ecomimic-v2
    restart: unless-stopped
    ports:
      - "8003:8003"
    volumes:
      - ./config:/app/config
      - ./models:/app/models
      - ./data:/app/data
      - ./logs:/app/logs
      - /tmp/ecomimic:/tmp/ecomimic
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - koursia-network

networks:
  koursia-network:
    external: true
EOF
```

### 6. Starting Ecomimic V2

```bash
# Start Ecomimic V2 service
docker-compose -f docker-compose.ecomimic.yml up -d

# Check logs
docker-compose -f docker-compose.ecomimic.yml logs -f ecomimic-v2

# Test API endpoint
curl http://localhost:8003/health
```

## API Configuration

### Environment Variables

Add these to your main `.env` file:

```bash
# Ecomimic V2 Configuration
ECOMIMIC_API_URL=http://localhost:8003
ECOMIMIC_API_KEY=your-ecomimic-api-key

# GPU Settings
CUDA_VISIBLE_DEVICES=0
NVIDIA_VISIBLE_DEVICES=all
```

### API Endpoints

The Ecomimic V2 service provides the following endpoints:

- `GET /health` - Health check
- `GET /api/v2/avatars` - List available avatars
- `GET /api/v2/backgrounds` - List available backgrounds
- `POST /api/v2/generate` - Start avatar generation
- `GET /api/v2/status/{job_id}` - Check generation status
- `POST /api/v2/preview` - Generate avatar preview

## Performance Optimization

### GPU Memory Optimization

```bash
# Monitor GPU usage
nvidia-smi -l 1

# Optimize GPU memory settings in config.yaml
gpu:
  memory_fraction: 0.8  # Use 80% of GPU memory
  allow_growth: true
  mixed_precision: true  # Enable mixed precision for better performance
```

### Batch Processing

```yaml
# Enable batch processing for multiple requests
generation:
  batch_size: 2  # Process 2 avatars simultaneously
  queue_size: 10  # Maximum queued requests
```

## Troubleshooting

### Common Issues

1. **GPU Not Detected**
   ```bash
   # Check GPU availability
   nvidia-smi
   docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi
   ```

2. **Out of Memory Errors**
   ```yaml
   # Reduce memory usage in config.yaml
   gpu:
     memory_fraction: 0.6
   generation:
     batch_size: 1
   ```

3. **Model Download Issues**
   ```bash
   # Manually download models
   docker exec -it koursia-ecomimic-v2 python download_models.py --force
   ```

4. **API Connection Issues**
   ```bash
   # Check service status
   docker-compose -f docker-compose.ecomimic.yml ps
   
   # Check logs
   docker-compose -f docker-compose.ecomimic.yml logs ecomimic-v2
   ```

### Performance Monitoring

```bash
# Monitor system resources
htop

# Monitor GPU usage
watch -n 1 nvidia-smi

# Monitor Docker container resources
docker stats koursia-ecomimic-v2
```

## Security Considerations

1. **API Key Management**
   - Use strong, unique API keys
   - Rotate keys regularly
   - Store keys securely (environment variables, secrets management)

2. **Network Security**
   - Use internal networks for service communication
   - Implement proper firewall rules
   - Consider VPN for remote access

3. **Data Protection**
   - Encrypt data at rest
   - Secure temporary file cleanup
   - Implement proper access controls

## Scaling Considerations

### Horizontal Scaling

```yaml
# Scale Ecomimic service
services:
  ecomimic-v2-1:
    # ... configuration
  ecomimic-v2-2:
    # ... configuration with different GPU
```

### Load Balancing

```yaml
# Add load balancer
services:
  ecomimic-lb:
    image: nginx:alpine
    ports:
      - "8003:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

## Maintenance

### Regular Tasks

1. **Model Updates**
   ```bash
   # Update models monthly
   docker exec -it koursia-ecomimic-v2 python update_models.py
   ```

2. **Log Rotation**
   ```bash
   # Setup log rotation
   sudo logrotate -f /etc/logrotate.d/ecomimic
   ```

3. **Cleanup**
   ```bash
   # Clean temporary files
   docker exec -it koursia-ecomimic-v2 python cleanup.py
   ```

## Support

For additional support and advanced configuration:

- Ecomimic V2 Documentation: [docs.ecomimic.com](https://docs.ecomimic.com)
- Community Forum: [forum.ecomimic.com](https://forum.ecomimic.com)
- GitHub Issues: [github.com/ecomimic/ecomimic-v2](https://github.com/ecomimic/ecomimic-v2)
