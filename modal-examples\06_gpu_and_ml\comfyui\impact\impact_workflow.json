{"last_node_id": 61, "last_link_id": 170, "nodes": [{"id": 28, "type": "K<PERSON><PERSON><PERSON>", "pos": {"0": 530, "1": 840}, "size": {"0": 320, "1": 600}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 65}, {"name": "positive", "type": "CONDITIONING", "link": 57}, {"name": "negative", "type": "CONDITIONING", "link": 170}, {"name": "latent_image", "type": "LATENT", "link": 59, "slot_index": 3}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [60], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [431433362471142, "fixed", 20, 8, "euler", "normal", 1], "color": "#322", "bgcolor": "#533"}, {"id": 52, "type": "PreviewImage", "pos": {"0": 2390, "1": 210}, "size": {"0": 230, "1": 310}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 146}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 30, "type": "VAEDecode", "pos": {"0": 1010, "1": 840}, "size": {"0": 140, "1": 50}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 60}, {"name": "vae", "type": "VAE", "link": 164}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [78, 152], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 58, "type": "Reroute", "pos": {"0": 850, "1": 220}, "size": [75, 26], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 163}], "outputs": [{"name": "", "type": "VAE", "links": [164], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 60, "type": "Reroute", "pos": {"0": 340, "1": 540}, "size": [75, 26], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 167}], "outputs": [{"name": "", "type": "CONDITIONING", "links": [168, 170], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 31, "type": "Reroute", "pos": {"0": 130, "1": 190}, "size": [82, 26], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 64}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [65], "slot_index": 0}], "properties": {"showOutputText": true, "horizontal": false}}, {"id": 17, "type": "MaskToImage", "pos": {"0": 2150, "1": 590}, "size": {"0": 176.39999389648438, "1": 26}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 153}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [107], "slot_index": 0}], "properties": {"Node name for S&R": "MaskToImage"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 18, "type": "PreviewImage", "pos": {"0": 2390, "1": 590}, "size": {"0": 230, "1": 290}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 107}], "outputs": [], "title": "Mask", "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 55, "type": "Reroute", "pos": {"0": -190, "1": -310}, "size": [75, 26], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 157}], "outputs": [{"name": "", "type": "MODEL", "links": [158], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 56, "type": "Reroute", "pos": {"0": -190, "1": -290}, "size": [75, 26], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 159}], "outputs": [{"name": "", "type": "CLIP", "links": [160], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 57, "type": "Reroute", "pos": {"0": -190, "1": -270}, "size": [75, 26], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 161}], "outputs": [{"name": "", "type": "VAE", "links": [162], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 59, "type": "Reroute", "pos": {"0": 290, "1": -250}, "size": [75, 26], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 165}], "outputs": [{"name": "", "type": "CONDITIONING", "links": [166], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 61, "type": "Reroute", "pos": {"0": 520, "1": -230}, "size": [75, 26], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 168}], "outputs": [{"name": "", "type": "CONDITIONING", "links": [169], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 4, "type": "CheckpointLoaderSimple", "pos": {"0": -640, "1": 190}, "size": {"0": 312.0885314941406, "1": 98}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [64, 157], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [148, 149, 159], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [161, 163], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["v1-5-pruned.safetensors"], "color": "#222", "bgcolor": "#000"}, {"id": 51, "type": "FaceDetailer", "pos": {"0": 1720, "1": -330}, "size": {"0": 350, "1": 1180}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 152}, {"name": "model", "type": "MODEL", "link": 158}, {"name": "clip", "type": "CLIP", "link": 160}, {"name": "vae", "type": "VAE", "link": 162}, {"name": "positive", "type": "CONDITIONING", "link": 166}, {"name": "negative", "type": "CONDITIONING", "link": 169}, {"name": "bbox_detector", "type": "BBOX_DETECTOR", "link": 150}, {"name": "sam_model_opt", "type": "SAM_MODEL", "link": 151, "shape": 7}, {"name": "segm_detector_opt", "type": "SEGM_DETECTOR", "link": null, "shape": 7}, {"name": "detailer_hook", "type": "DETAILER_HOOK", "link": null, "shape": 7}, {"name": "scheduler_func_opt", "type": "SCHEDULER_FUNC", "link": null, "shape": 7}], "outputs": [{"name": "image", "type": "IMAGE", "links": [141], "slot_index": 0, "shape": 3}, {"name": "cropped_refined", "type": "IMAGE", "links": [], "slot_index": 1, "shape": 6}, {"name": "cropped_enhanced_alpha", "type": "IMAGE", "links": [146], "slot_index": 2, "shape": 6}, {"name": "mask", "type": "MASK", "links": [153], "slot_index": 3, "shape": 3}, {"name": "detailer_pipe", "type": "DETAILER_PIPE", "links": null, "shape": 3}, {"name": "cnet_images", "type": "IMAGE", "links": null, "shape": 6}], "properties": {"Node name for S&R": "FaceDetailer"}, "widgets_values": [360, true, 768, 0, "fixed", 20, 8, "euler", "normal", 0.5, 5, true, false, 0.5, 15, 3, "center-1", 0, 0.93, 0, 0.7, "False", 10, "", 1, false, 20], "color": "#223", "bgcolor": "#335"}, {"id": 6, "type": "CLIPTextEncode", "pos": {"0": -120, "1": 540}, "size": {"0": 451.25177001953125, "1": 307.1271667480469}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 149}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [167], "slot_index": 0}], "title": "Negative", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["out of frame, cut off, low contrast, underexposed, overexposed, bad art, beginner, amateur"], "color": "#322", "bgcolor": "#533"}, {"id": 5, "type": "CLIPTextEncode", "pos": {"0": -120, "1": 300}, "size": {"0": 310, "1": 180}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 148}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [57, 165], "slot_index": 0}], "title": "Positive", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["A smiling woman with short hair"], "color": "#322", "bgcolor": "#533"}, {"id": 53, "type": "UltralyticsDetectorProvider", "pos": {"0": 1316, "1": -212}, "size": {"0": 315, "1": 78}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "BBOX_DETECTOR", "type": "BBOX_DETECTOR", "links": [150], "slot_index": 0, "shape": 3}, {"name": "SEGM_DETECTOR", "type": "SEGM_DETECTOR", "links": null, "shape": 3}], "properties": {"Node name for S&R": "UltralyticsDetectorProvider"}, "widgets_values": ["bbox/face_yolov8m.pt"], "color": "#223", "bgcolor": "#335"}, {"id": 16, "type": "SAMLoader", "pos": {"0": 1318, "1": -72}, "size": {"0": 320, "1": 82}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "SAM_MODEL", "type": "SAM_MODEL", "links": [151], "slot_index": 0}], "properties": {"Node name for S&R": "SAMLoader"}, "widgets_values": ["sam_vit_b_01ec64.pth", "AUTO"], "color": "#223", "bgcolor": "#335"}, {"id": 33, "type": "PreviewImage", "pos": {"0": 1303, "1": 352}, "size": {"0": 360, "1": 630}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 78}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#322", "bgcolor": "#533"}, {"id": 7, "type": "PreviewImage", "pos": {"0": 2660, "1": -320}, "size": {"0": 430, "1": 650}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 141}], "outputs": [], "title": "Enhanced", "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 29, "type": "EmptyLatentImage", "pos": {"0": -120, "1": 900}, "size": {"0": 310, "1": 130}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [59]}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1], "color": "#322", "bgcolor": "#533"}], "links": [[57, 5, 0, 28, 1, "CONDITIONING"], [59, 29, 0, 28, 3, "LATENT"], [60, 28, 0, 30, 0, "LATENT"], [64, 4, 0, 31, 0, "*"], [65, 31, 0, 28, 0, "MODEL"], [78, 30, 0, 33, 0, "IMAGE"], [107, 17, 0, 18, 0, "IMAGE"], [141, 51, 0, 7, 0, "IMAGE"], [146, 51, 2, 52, 0, "IMAGE"], [148, 4, 1, 5, 0, "CLIP"], [149, 4, 1, 6, 0, "CLIP"], [150, 53, 0, 51, 6, "BBOX_DETECTOR"], [151, 16, 0, 51, 7, "SAM_MODEL"], [152, 30, 0, 51, 0, "IMAGE"], [153, 51, 3, 17, 0, "MASK"], [157, 4, 0, 55, 0, "*"], [158, 55, 0, 51, 1, "MODEL"], [159, 4, 1, 56, 0, "*"], [160, 56, 0, 51, 2, "CLIP"], [161, 4, 2, 57, 0, "*"], [162, 57, 0, 51, 3, "VAE"], [163, 4, 2, 58, 0, "*"], [164, 58, 0, 30, 1, "VAE"], [165, 5, 0, 59, 0, "*"], [166, 59, 0, 51, 4, "CONDITIONING"], [167, 6, 0, 60, 0, "*"], [168, 60, 0, 61, 0, "*"], [169, 61, 0, 51, 5, "CONDITIONING"], [170, 60, 0, 28, 2, "CONDITIONING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.6303940863128494, "offset": [-718.9439775265187, 453.43366331234205]}, "groupNodes": {}}, "version": 0.4}