# Koursia - AI-Powered Course Creation Platform

Koursia is a comprehensive AI-powered platform for creating engaging educational courses with automated content generation, voice synthesis, and avatar integration.

## Features

### Smart Course Creation
- AI-powered content generation using Gemini API
- Automated course structure and script creation
- Voice synthesis with Chatterbox TTS and Kokoro TTS
- Slide generation with Marp
- Video assembly with FFmpeg and Whisper

### Avatar Course Creation
- AI avatar integration with Ecomimic V2
- Synchronized avatar and media content
- Professional video output with avatar narration

### Media Integration
- Stock media from Pexels and Pixabay APIs
- Custom media upload and management
- AWS S3 storage for scalable media handling

## Architecture

```
koursia/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configuration
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utilities
│   ├── tests/              # Backend tests
│   └── requirements.txt
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── utils/          # Utilities
│   ├── public/
│   └── package.json
├── ai-services/            # AI integration services
│   ├── gemini/            # Gemini API integration
│   ├── tts/               # Text-to-speech services
│   ├── whisper/           # Speech recognition
│   └── ecomimic/          # Avatar generation
├── media-processing/       # Media processing services
│   ├── ffmpeg/            # Video processing
│   ├── marp/              # Slide generation
│   └── stock-apis/        # Stock media APIs
├── docker/                 # Docker configurations
├── docs/                   # Documentation
└── scripts/               # Utility scripts
```

## Technology Stack

### Backend
- **Framework**: FastAPI
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Authentication**: JWT with FastAPI Security
- **Background Jobs**: Celery with Redis
- **Storage**: AWS S3

### Frontend
- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **UI Library**: Material-UI (MUI)
- **State Management**: Redux Toolkit
- **HTTP Client**: Axios

### AI & Media Processing
- **Content Generation**: Google Gemini API
- **Text-to-Speech**: Chatterbox TTS, Kokoro TTS
- **Speech Recognition**: OpenAI Whisper
- **Avatar Generation**: Ecomimic V2
- **Video Processing**: FFmpeg
- **Slide Generation**: Marp

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Cloud Storage**: AWS S3
- **Database**: PostgreSQL
- **Cache/Queue**: Redis
- **Monitoring**: Prometheus & Grafana

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local frontend development)
- Python 3.11+ (for local backend development)
- NVIDIA GPU with CUDA support (for avatar generation)

### Development Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd koursia
```

2. Copy environment configuration:
```bash
cp .env.example .env
# Edit .env with your API keys and configuration
```

3. Start the development environment:
```bash
docker-compose up -d
```

4. Access the application:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## Environment Variables

See `.env.example` for required environment variables including:
- Database configuration
- AWS S3 credentials
- AI service API keys (Gemini, Chatterbox, etc.)
- JWT secret keys

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please open an issue in the GitHub repository.
