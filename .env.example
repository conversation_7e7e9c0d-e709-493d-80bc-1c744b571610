# Database Configuration
POSTGRES_DB=koursia
POSTGRES_USER=koursia_user
POSTGRES_PASSWORD=koursia_password
DATABASE_URL=postgresql://koursia_user:koursia_password@localhost:5432/koursia

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_S3_BUCKET=koursia-media-bucket
AWS_REGION=us-east-1

# AI Service API Keys
GEMINI_API_KEY=your-gemini-api-key
CHATTERBOX_API_KEY=your-chatterbox-api-key
KOKORO_API_KEY=your-kokoro-api-key
OPENAI_API_KEY=your-openai-api-key

# Stock Media API Keys
PEXELS_API_KEY=your-pexels-api-key
PIXABAY_API_KEY=your-pixabay-api-key

# Ecomimic V2 Configuration
ECOMIMIC_API_URL=http://localhost:8003
ECOMIMIC_API_KEY=your-ecomimic-api-key

# Application Configuration
APP_NAME=Koursia
APP_VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# File Upload Configuration
MAX_FILE_SIZE_MB=100
ALLOWED_FILE_TYPES=mp4,mp3,wav,jpg,jpeg,png,gif,pdf,pptx

# Video Processing Configuration
FFMPEG_THREADS=4
VIDEO_QUALITY=high
DEFAULT_VIDEO_FORMAT=mp4
DEFAULT_AUDIO_FORMAT=mp3

# TTS Configuration
DEFAULT_TTS_PROVIDER=chatterbox
DEFAULT_VOICE=en-US-AriaNeural
SPEECH_RATE=1.0
SPEECH_PITCH=0

# Course Generation Configuration
DEFAULT_COURSE_DURATION_MINUTES=30
MAX_MODULES_PER_COURSE=10
MAX_LESSONS_PER_MODULE=20

# Monitoring and Logging
LOG_LEVEL=INFO
SENTRY_DSN=your-sentry-dsn-for-error-tracking

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
FROM_EMAIL=<EMAIL>

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# Cache Configuration
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=1000
