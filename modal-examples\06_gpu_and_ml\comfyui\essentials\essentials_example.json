{"last_node_id": 42, "last_link_id": 61, "nodes": [{"id": 9, "type": "ConsoleDebug+", "pos": {"0": 720, "1": 140}, "size": {"0": 210, "1": 60}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "value", "type": "*", "link": 3}], "outputs": [], "properties": {"Node name for S&R": "ConsoleDebug+"}, "widgets_values": ["Height:"]}, {"id": 28, "type": "PreviewImage", "pos": {"0": 860, "1": 1180}, "size": {"0": 210, "1": 246}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 23}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 12, "type": "PreviewImage", "pos": {"0": 860, "1": 580}, "size": {"0": 210, "1": 246}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 11}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 14, "type": "PreviewImage", "pos": {"0": 860, "1": 880}, "size": {"0": 210, "1": 246}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 13}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 18, "type": "MaskPreview+", "pos": {"0": 2100, "1": 90}, "size": {"0": 210, "1": 246}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 19}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 1, "type": "GetImageSize+", "pos": {"0": 450, "1": 80}, "size": {"0": 210, "1": 66}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1}], "outputs": [{"name": "width", "type": "INT", "links": [2], "slot_index": 0, "shape": 3}, {"name": "height", "type": "INT", "links": [3], "slot_index": 1, "shape": 3}, {"name": "count", "type": "INT", "links": null}], "properties": {"Node name for S&R": "GetImageSize+"}, "widgets_values": []}, {"id": 8, "type": "ConsoleDebug+", "pos": {"0": 720, "1": 40}, "size": {"0": 210, "1": 60}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "value", "type": "*", "link": 2}], "outputs": [], "properties": {"Node name for S&R": "ConsoleDebug+"}, "widgets_values": ["Width:"]}, {"id": 10, "type": "PreviewImage", "pos": {"0": 860, "1": 280}, "size": {"0": 210, "1": 246}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 4, "type": "ImageFlip+", "pos": {"0": 430, "1": 800}, "size": {"0": 310, "1": 60}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 6}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [11], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageFlip+"}, "widgets_values": ["xy"]}, {"id": 16, "type": "MaskFlip+", "pos": {"0": 1690, "1": 270}, "size": {"0": 310, "1": 60}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 15}], "outputs": [{"name": "MASK", "type": "MASK", "links": [18], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskFlip+"}, "widgets_values": ["xy"]}, {"id": 13, "type": "PreviewImage", "pos": {"0": 1100, "1": 760}, "size": {"0": 210, "1": 246}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 49}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 11, "type": "PreviewImage", "pos": {"0": 1100, "1": 450}, "size": {"0": 210, "1": 246}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 58}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 20, "type": "LoadImageMask", "pos": {"0": 1400, "1": 260}, "size": {"0": 220.70516967773438, "1": 318}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MASK", "type": "MASK", "links": [14, 15], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "LoadImageMask"}, "widgets_values": ["simple_mask.png", "alpha", "image"]}, {"id": 21, "type": "MaskPreview+", "pos": {"0": 2100, "1": 380}, "size": {"0": 210, "1": 246}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 18}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+"}, "widgets_values": []}, {"id": 2, "type": "ImageResize+", "pos": {"0": 430, "1": 340}, "size": {"0": 310, "1": 218}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 4}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9], "slot_index": 0, "shape": 3}, {"name": "width", "type": "INT", "links": [44], "slot_index": 1, "shape": 3}, {"name": "height", "type": "INT", "links": [45], "slot_index": 2, "shape": 3}], "properties": {"Node name for S&R": "ImageResize+"}, "widgets_values": [256, 64, "lanc<PERSON>s", "stretch", "always", 0]}, {"id": 7, "type": "LoadImage", "pos": {"0": -90, "1": 650}, "size": {"0": 315, "1": 314}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1, 4, 6, 8, 22, 48, 57], "slot_index": 0, "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["vernere.jpg", "image"]}, {"id": 15, "type": "MaskBlur+", "pos": {"0": 1690, "1": 130}, "size": {"0": 310, "1": 82}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 14}], "outputs": [{"name": "MASK", "type": "MASK", "links": [19], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "MaskBlur+"}, "widgets_values": [45, "auto"]}, {"id": 36, "type": "SimpleMath+", "pos": {"0": 1650, "1": 780}, "size": {"0": 210, "1": 98}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "a", "type": "*", "link": 44, "shape": 7}, {"name": "b", "type": "*", "link": 45, "shape": 7}, {"name": "c", "type": "*", "link": null, "shape": 7}], "outputs": [{"name": "INT", "type": "INT", "links": [46], "slot_index": 0, "shape": 3}, {"name": "FLOAT", "type": "FLOAT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "SimpleMath+"}, "widgets_values": ["a*b"]}, {"id": 23, "type": "ConsoleDebug+", "pos": {"0": 1920, "1": 780}, "size": {"0": 210, "1": 60}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "value", "type": "*", "link": 46}], "outputs": [], "properties": {"Node name for S&R": "ConsoleDebug+"}, "widgets_values": ["Value:"]}, {"id": 40, "type": "ImageCrop+", "pos": {"0": 443, "1": 565}, "size": {"0": 310, "1": 194}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 57}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [58], "slot_index": 0, "shape": 3}, {"name": "x", "type": "INT", "links": null, "shape": 3}, {"name": "y", "type": "INT", "links": null, "shape": 3}], "properties": {"Node name for S&R": "ImageCrop+"}, "widgets_values": [256, 256, "center", 0, 0]}, {"id": 37, "type": "ImageDesaturate+", "pos": {"0": 535, "1": 833}, "size": {"0": 210, "1": 82}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 48}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [49], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageDesaturate+"}, "widgets_values": [1, "luminance (Rec.709)"]}, {"id": 6, "type": "ImagePosterize+", "pos": {"0": 444, "1": 990}, "size": {"0": 310, "1": 60}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 8}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [13], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImagePosterize+"}, "widgets_values": [0.5]}, {"id": 27, "type": "ImageCASharpening+", "pos": {"0": 417, "1": 1110}, "size": {"0": 310.79998779296875, "1": 60}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 22}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [23], "slot_index": 0, "shape": 3}], "properties": {"Node name for S&R": "ImageCASharpening+"}, "widgets_values": [0.8]}], "links": [[1, 7, 0, 1, 0, "IMAGE"], [2, 1, 0, 8, 0, "*"], [3, 1, 1, 9, 0, "*"], [4, 7, 0, 2, 0, "IMAGE"], [6, 7, 0, 4, 0, "IMAGE"], [8, 7, 0, 6, 0, "IMAGE"], [9, 2, 0, 10, 0, "IMAGE"], [11, 4, 0, 12, 0, "IMAGE"], [13, 6, 0, 14, 0, "IMAGE"], [14, 20, 0, 15, 0, "MASK"], [15, 20, 0, 16, 0, "MASK"], [18, 16, 0, 21, 0, "MASK"], [19, 15, 0, 18, 0, "MASK"], [22, 7, 0, 27, 0, "IMAGE"], [23, 27, 0, 28, 0, "IMAGE"], [44, 2, 1, 36, 0, "INT,FLOAT"], [45, 2, 2, 36, 1, "INT,FLOAT"], [46, 36, 0, 23, 0, "*"], [48, 7, 0, 37, 0, "IMAGE"], [49, 37, 0, 13, 0, "IMAGE"], [57, 7, 0, 40, 0, "IMAGE"], [58, 40, 0, 11, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.5644739300537777, "offset": {"0": 541.0386352539062, "1": 181.41673278808594}}}, "version": 0.4}