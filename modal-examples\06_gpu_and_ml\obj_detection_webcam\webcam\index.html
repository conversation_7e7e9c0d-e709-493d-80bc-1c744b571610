<h1>Real-time object classification</h1>
<div style="position: relative">
  <video
    id="video"
    width="480"
    autoplay
    muted
    style="position: relative; top: 0; left: 0"
    loop
    muted
    playsinline
  ></video
  ><br />
  <img id="outputImg" style="position: absolute; top: 0; left: 0" />
  <div
    id="message"
    style="position: absolute; top: 0; left: 0; color: red"
  ></div>
</div>
<canvas id="canvas" style="display: none"> </canvas>

<script src="https://webrtc.github.io/adapter/adapter-1.0.2.js"></script>
<script>
  const getNextFrameLoop = () => {
    const context = canvas.getContext("2d");
    if (!video.videoWidth || !video.videoHeight) {
      setTimeout(getNextFrameLoop, 1000);
      return;
    }
    canvas.width = 480; // resize the canvas to 480 times whatever
    canvas.height = (480 * video.videoHeight) / video.videoWidth;
    context.drawImage(
      video,
      0,
      0,
      video.videoWidth,
      video.videoHeight,
      0,
      0,
      canvas.width,
      canvas.height
    );
    // message.textContent = video.videoWidth + " " + video.videoHeight + " " + canvas.width + " " + canvas.height;
    const data = canvas.toDataURL("image/png");
    fetch("/predict", {
      method: "POST",
      headers: { "Content-Type": "text/plain" },
      body: data,
    })
      .then((res) => res.text())
      .then((text) => {
        message.textContent = "";
        outputImg.src = text;
        setTimeout(getNextFrameLoop, 10);
      })
      .catch((e) => {
        message.textContent = e.name + ": " + e.message;
        setTimeout(getNextFrameLoop, 1000);
      });
  };

  navigator.mediaDevices
    .getUserMedia({ video: true, audio: false })
    .then((stream) => {
      video.srcObject = stream;
      message.textContent = "Waiting for classifier...";
      getNextFrameLoop();
    })
    .catch((e) => {
      message.textContent = e.name + ": " + e.message;
    });
</script>
