#!/bin/bash

# Koursia Platform Setup Script
# This script sets up the development environment for Koursia

set -e

echo "🚀 Setting up Koursia - AI-Powered Course Creation Platform"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your API keys and configuration"
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p media/{uploads,generated,temp}
mkdir -p logs
mkdir -p data/postgres
mkdir -p data/redis

# Set permissions
chmod -R 755 media
chmod -R 755 logs

# Build and start services
echo "🔨 Building Docker images..."
docker-compose build

echo "🚀 Starting services..."
docker-compose up -d postgres redis

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Run database migrations
echo "🗃️  Running database migrations..."
docker-compose run --rm backend alembic upgrade head

# Start all services
echo "🌟 Starting all services..."
docker-compose up -d

echo "✅ Setup complete!"
echo ""
echo "🌐 Access the application:"
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:8000"
echo "   API Documentation: http://localhost:8000/docs"
echo ""
echo "📋 Next steps:"
echo "   1. Edit .env file with your API keys"
echo "   2. Restart services: docker-compose restart"
echo "   3. Check logs: docker-compose logs -f"
echo ""
echo "🛠️  Development commands:"
echo "   - View logs: docker-compose logs -f [service]"
echo "   - Stop services: docker-compose down"
echo "   - Rebuild: docker-compose build [service]"
echo "   - Shell access: docker-compose exec [service] bash"
