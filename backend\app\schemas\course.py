"""
Course schemas
"""

from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID

from app.models.course import CourseStatus, CourseDifficulty


class CategoryBase(BaseModel):
    """Base category schema"""
    name: str
    description: Optional[str] = None
    slug: str
    icon: Optional[str] = None
    color: Optional[str] = None


class CategoryResponse(CategoryBase):
    """Category response schema"""
    id: UUID
    parent_id: Optional[UUID] = None
    order_index: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class CourseBase(BaseModel):
    """Base course schema"""
    title: str
    description: Optional[str] = None
    short_description: Optional[str] = None
    slug: str
    difficulty: CourseDifficulty = CourseDifficulty.BEGINNER
    language: str = "en"
    target_audience: Optional[str] = None
    learning_objectives: Optional[List[str]] = None
    prerequisites: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    category_id: Optional[UUID] = None


class CourseCreate(CourseBase):
    """Course creation schema"""
    use_avatar: bool = False
    avatar_style: Optional[str] = None
    voice_settings: Optional[Dict[str, Any]] = None
    slide_template: Optional[str] = None


class CourseUpdate(BaseModel):
    """Course update schema"""
    title: Optional[str] = None
    description: Optional[str] = None
    short_description: Optional[str] = None
    difficulty: Optional[CourseDifficulty] = None
    language: Optional[str] = None
    target_audience: Optional[str] = None
    learning_objectives: Optional[List[str]] = None
    prerequisites: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    category_id: Optional[UUID] = None
    status: Optional[CourseStatus] = None
    thumbnail_url: Optional[str] = None
    cover_image_url: Optional[str] = None
    use_avatar: Optional[bool] = None
    avatar_style: Optional[str] = None
    voice_settings: Optional[Dict[str, Any]] = None
    slide_template: Optional[str] = None
    is_featured: Optional[bool] = None
    is_free: Optional[bool] = None
    price: Optional[float] = None


class CourseResponse(CourseBase):
    """Course response schema"""
    id: UUID
    status: CourseStatus
    duration_minutes: Optional[int] = None
    thumbnail_url: Optional[str] = None
    trailer_url: Optional[str] = None
    cover_image_url: Optional[str] = None
    use_avatar: bool
    avatar_style: Optional[str] = None
    voice_settings: Optional[Dict[str, Any]] = None
    slide_template: Optional[str] = None
    view_count: int
    enrollment_count: int
    rating_average: float
    rating_count: int
    published_at: Optional[datetime] = None
    is_featured: bool
    is_free: bool
    price: Optional[float] = None
    creator_id: UUID
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class CourseListResponse(BaseModel):
    """Course list response schema"""
    courses: List[CourseResponse]
    total: int
    page: int
    per_page: int
    pages: int


class ModuleBase(BaseModel):
    """Base module schema"""
    title: str
    description: Optional[str] = None
    order_index: int
    objectives: Optional[List[str]] = None


class ModuleCreate(ModuleBase):
    """Module creation schema"""
    pass


class ModuleUpdate(BaseModel):
    """Module update schema"""
    title: Optional[str] = None
    description: Optional[str] = None
    order_index: Optional[int] = None
    objectives: Optional[List[str]] = None


class ModuleResponse(ModuleBase):
    """Module response schema"""
    id: UUID
    course_id: UUID
    duration_minutes: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class LessonBase(BaseModel):
    """Base lesson schema"""
    title: str
    description: Optional[str] = None
    content: Optional[str] = None
    order_index: int
    is_preview: bool = False


class LessonCreate(LessonBase):
    """Lesson creation schema"""
    pass


class LessonUpdate(BaseModel):
    """Lesson update schema"""
    title: Optional[str] = None
    description: Optional[str] = None
    content: Optional[str] = None
    order_index: Optional[int] = None
    is_preview: Optional[bool] = None
    video_url: Optional[str] = None
    audio_url: Optional[str] = None
    slides_url: Optional[str] = None
    transcript_url: Optional[str] = None


class LessonResponse(LessonBase):
    """Lesson response schema"""
    id: UUID
    module_id: UUID
    duration_minutes: Optional[int] = None
    video_url: Optional[str] = None
    audio_url: Optional[str] = None
    slides_url: Optional[str] = None
    transcript_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class EnrollmentResponse(BaseModel):
    """Enrollment response schema"""
    id: UUID
    user_id: UUID
    course_id: UUID
    progress_percentage: float
    completed_lessons: Optional[List[UUID]] = None
    current_lesson_id: Optional[UUID] = None
    enrolled_at: datetime
    completed_at: Optional[datetime] = None
    last_accessed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
