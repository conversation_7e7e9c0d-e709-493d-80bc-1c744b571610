import { configureStore } from '@reduxjs/toolkit'
import { setupListeners } from '@reduxjs/toolkit/query'

import authSlice from './slices/authSlice'
import coursesSlice from './slices/coursesSlice'
import generationSlice from './slices/generationSlice'
import mediaSlice from './slices/mediaSlice'
import uiSlice from './slices/uiSlice'

import { api } from './api'

export const store = configureStore({
  reducer: {
    auth: authSlice,
    courses: coursesSlice,
    generation: generationSlice,
    media: mediaSlice,
    ui: uiSlice,
    [api.reducerPath]: api.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }).concat(api.middleware),
})

setupListeners(store.dispatch)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
