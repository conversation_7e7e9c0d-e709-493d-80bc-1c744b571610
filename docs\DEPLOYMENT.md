# Koursia Deployment Guide

This guide provides comprehensive instructions for deploying the Koursia AI-powered course creation platform.

## Prerequisites

### System Requirements

#### Minimum Requirements
- **CPU**: 4 cores, 2.4GHz
- **RAM**: 16GB
- **Storage**: 100GB SSD
- **Network**: 100 Mbps

#### Recommended Requirements (Production)
- **CPU**: 8+ cores, 3.0GHz+
- **RAM**: 32GB+
- **Storage**: 500GB+ NVMe SSD
- **Network**: 1 Gbps
- **GPU**: NVIDIA RTX 4090 or A100 (for AI services)

### Software Requirements

- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.30+
- **Node.js**: 18+ (for local development)
- **Python**: 3.11+ (for local development)

### GPU Requirements (Optional but Recommended)

For AI services (Ecomimic V2, Whisper):
- **NVIDIA GPU**: RTX 3080+ or A100
- **VRAM**: 12GB+ (24GB+ recommended)
- **CUDA**: 11.8+
- **NVIDIA Container Toolkit**

## Quick Start

### 1. Clone Repository

```bash
git clone https://github.com/your-org/koursia.git
cd koursia
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### 3. Deploy

```bash
# Make deployment script executable (Linux/Mac)
chmod +x scripts/deploy.sh

# Deploy for development
./scripts/deploy.sh development

# Deploy for production
./scripts/deploy.sh production
```

## Environment Configuration

### Required Environment Variables

Create a `.env` file in the project root:

```bash
# Database Configuration
POSTGRES_DB=koursia
POSTGRES_USER=koursia_user
POSTGRES_PASSWORD=your_secure_password
DATABASE_URL=************************************************************/koursia

# Redis Configuration
REDIS_URL=redis://redis:6379

# Security
SECRET_KEY=your_very_secure_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI Service API Keys
GEMINI_API_KEY=your_gemini_api_key
OPENAI_API_KEY=your_openai_api_key
CHATTERBOX_API_KEY=your_chatterbox_api_key
KOKORO_API_KEY=your_kokoro_api_key

# Ecomimic V2 Configuration
ECOMIMIC_API_URL=http://localhost:8003
ECOMIMIC_API_KEY=your_ecomimic_api_key

# Stock Media APIs
PEXELS_API_KEY=your_pexels_api_key
PIXABAY_API_KEY=your_pixabay_api_key

# AWS Configuration (for S3 storage)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET=your_s3_bucket_name
AWS_REGION=us-east-1

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Environment
ENVIRONMENT=development
DEBUG=true
```

### Optional Environment Variables

```bash
# File Upload Limits
MAX_FILE_SIZE_MB=100
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mp3,pdf,doc,docx

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# Monitoring
SENTRY_DSN=your_sentry_dsn

# Analytics
GOOGLE_ANALYTICS_ID=your_ga_id
```

## Deployment Methods

### Method 1: Docker Compose (Recommended)

#### Development Deployment

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

#### Production Deployment

```bash
# Use production compose file
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Or use deployment script
./scripts/deploy.sh production
```

### Method 2: Manual Deployment

#### Backend Deployment

```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Run migrations
alembic upgrade head

# Start server
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

#### Frontend Deployment

```bash
cd frontend

# Install dependencies
npm install

# Build for production
npm run build

# Serve with nginx or static server
npm run preview
```

### Method 3: Kubernetes Deployment

See `k8s/` directory for Kubernetes manifests.

```bash
# Apply configurations
kubectl apply -f k8s/

# Check status
kubectl get pods -n koursia
```

## Database Setup

### Initial Migration

```bash
# Run database migrations
docker-compose exec backend alembic upgrade head

# Create initial data
docker-compose exec backend python scripts/seed_data.py
```

### Backup and Restore

```bash
# Create backup
docker-compose exec postgres pg_dump -U koursia_user koursia > backup.sql

# Restore backup
docker-compose exec -T postgres psql -U koursia_user koursia < backup.sql
```

## SSL/TLS Configuration

### Let's Encrypt (Recommended)

```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Custom Certificates

```bash
# Place certificates in nginx/ssl/
cp your-cert.pem nginx/ssl/cert.pem
cp your-key.pem nginx/ssl/key.pem

# Update nginx configuration
# Uncomment HTTPS server block in nginx/nginx.conf
```

## Monitoring and Logging

### Health Checks

```bash
# Check service health
curl http://localhost/health

# Check individual services
curl http://localhost:8000/health  # Backend
curl http://localhost:8001/health  # AI Services
curl http://localhost:8002/health  # Media Processing
```

### Log Management

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Log rotation (production)
# Configure in docker-compose.prod.yml
```

### Monitoring Setup

#### Prometheus + Grafana

```bash
# Start monitoring stack
docker-compose -f docker-compose.monitoring.yml up -d

# Access Grafana
open http://localhost:3001
```

#### Application Monitoring

```bash
# Install Sentry for error tracking
pip install sentry-sdk

# Configure in backend/app/core/config.py
SENTRY_DSN = "your_sentry_dsn"
```

## Performance Optimization

### Database Optimization

```sql
-- Create additional indexes
CREATE INDEX CONCURRENTLY idx_course_search ON course USING gin(to_tsvector('english', title || ' ' || description));

-- Analyze tables
ANALYZE course;
ANALYZE "user";
```

### Caching

```bash
# Redis configuration
# Increase memory limit in docker-compose.yml
command: redis-server --maxmemory 2gb --maxmemory-policy allkeys-lru
```

### CDN Setup

```bash
# Configure CloudFlare or AWS CloudFront
# Update STATIC_URL in environment variables
STATIC_URL=https://your-cdn-domain.com/static/
```

## Security Hardening

### Firewall Configuration

```bash
# UFW (Ubuntu)
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable

# Deny direct access to application ports
sudo ufw deny 8000
sudo ufw deny 8001
sudo ufw deny 8002
```

### Security Headers

Already configured in `nginx/nginx.conf`:
- X-Frame-Options
- X-XSS-Protection
- X-Content-Type-Options
- Content-Security-Policy
- Strict-Transport-Security (HTTPS)

### Rate Limiting

Configured in nginx:
- API endpoints: 10 requests/second
- Auth endpoints: 5 requests/second

## Troubleshooting

### Common Issues

#### 1. Database Connection Failed

```bash
# Check database status
docker-compose ps postgres

# Check logs
docker-compose logs postgres

# Reset database
docker-compose down -v
docker-compose up -d postgres
```

#### 2. AI Services Not Starting

```bash
# Check GPU availability
nvidia-smi

# Check CUDA installation
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi

# Check AI service logs
docker-compose logs ai-services
```

#### 3. Frontend Build Errors

```bash
# Clear node modules
rm -rf frontend/node_modules
rm frontend/package-lock.json

# Reinstall dependencies
cd frontend
npm install
```

#### 4. Media Processing Issues

```bash
# Check FFmpeg installation
docker-compose exec media-processing ffmpeg -version

# Check disk space
df -h

# Check media permissions
ls -la media/
```

### Performance Issues

#### High CPU Usage

```bash
# Check container resources
docker stats

# Limit container resources in docker-compose.yml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 4G
```

#### High Memory Usage

```bash
# Monitor memory usage
free -h

# Check for memory leaks
docker-compose exec backend python -m memory_profiler app/main.py
```

### Log Analysis

```bash
# Search for errors
docker-compose logs | grep ERROR

# Monitor real-time logs
docker-compose logs -f --tail=100

# Export logs
docker-compose logs > koursia-logs-$(date +%Y%m%d).log
```

## Backup and Recovery

### Automated Backups

```bash
# Create backup script
cat > scripts/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Database backup
docker-compose exec -T postgres pg_dump -U koursia_user koursia > "${BACKUP_DIR}/db_${DATE}.sql"

# Media backup
tar -czf "${BACKUP_DIR}/media_${DATE}.tar.gz" media/

# Keep only last 7 days
find "${BACKUP_DIR}" -name "*.sql" -mtime +7 -delete
find "${BACKUP_DIR}" -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x scripts/backup.sh

# Schedule with cron
crontab -e
# Add: 0 2 * * * /path/to/koursia/scripts/backup.sh
```

### Disaster Recovery

```bash
# Stop services
docker-compose down

# Restore database
docker-compose up -d postgres
docker-compose exec -T postgres psql -U koursia_user koursia < backup.sql

# Restore media files
tar -xzf media_backup.tar.gz

# Start all services
docker-compose up -d
```

## Scaling

### Horizontal Scaling

```bash
# Scale specific services
docker-compose up -d --scale backend=3 --scale ai-services=2

# Load balancer configuration
# Update nginx/nginx.conf upstream blocks
```

### Vertical Scaling

```bash
# Increase container resources
# Update docker-compose.yml deploy.resources sections
```

## Maintenance

### Regular Tasks

```bash
# Update dependencies (monthly)
docker-compose pull
docker-compose build --no-cache

# Clean up Docker resources (weekly)
docker system prune -f

# Database maintenance (weekly)
docker-compose exec postgres psql -U koursia_user koursia -c "VACUUM ANALYZE;"

# Log rotation (daily)
find logs/ -name "*.log" -mtime +30 -delete
```

### Updates

```bash
# Update application
git pull origin main
docker-compose build --no-cache
docker-compose up -d

# Run migrations if needed
docker-compose exec backend alembic upgrade head
```

## Support

For additional support:

- Documentation: [docs.koursia.com](https://docs.koursia.com)
- Issues: [GitHub Issues](https://github.com/your-org/koursia/issues)
- Community: [Discord](https://discord.gg/koursia)
- Email: <EMAIL>
