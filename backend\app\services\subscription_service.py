"""
Subscription service for managing user subscriptions and billing
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.models.user import User
from app.models.subscription import (
    Plan, Subscription, UsageRecord, PromoCode,
    PlanType, BillingCycle, SubscriptionStatus
)
from app.schemas.pricing import UsageResponse, FeatureFlags
from app.core.config import settings


class SubscriptionService:
    def __init__(self, db: Session):
        self.db = db

    def get_active_subscription(self, user_id: str) -> Optional[Subscription]:
        """Get user's active subscription"""
        return (
            self.db.query(Subscription)
            .filter(
                Subscription.user_id == user_id,
                Subscription.status.in_([
                    SubscriptionStatus.ACTIVE,
                    SubscriptionStatus.TRIALING
                ])
            )
            .first()
        )

    def create_subscription(
        self,
        user_id: str,
        plan_id: str,
        billing_cycle: BillingCycle,
        payment_method_id: Optional[str] = None,
        promo_code: Optional[str] = None,
    ) -> Subscription:
        """Create a new subscription"""
        plan = self.db.query(Plan).filter(Plan.id == plan_id).first()
        if not plan:
            raise ValueError("Plan not found")

        # Calculate pricing
        amount = plan.yearly_price if billing_cycle == BillingCycle.YEARLY else plan.monthly_price
        
        # Apply promo code if provided
        if promo_code:
            discount = self._apply_promo_code(promo_code, plan_id, user_id)
            amount = max(0, amount - discount)

        # Create subscription
        subscription = Subscription(
            user_id=user_id,
            plan_id=plan_id,
            billing_cycle=billing_cycle,
            amount=amount,
            status=SubscriptionStatus.ACTIVE,
            start_date=datetime.utcnow(),
        )

        # Set trial if applicable
        if plan.trial_days > 0 and not self.has_used_trial(user_id):
            subscription.status = SubscriptionStatus.TRIALING
            subscription.trial_start = datetime.utcnow()
            subscription.trial_end = datetime.utcnow() + timedelta(days=plan.trial_days)

        self.db.add(subscription)
        self.db.commit()
        self.db.refresh(subscription)

        return subscription

    def update_subscription(
        self,
        subscription_id: str,
        **kwargs
    ) -> Subscription:
        """Update an existing subscription"""
        subscription = self.db.query(Subscription).filter(
            Subscription.id == subscription_id
        ).first()
        
        if not subscription:
            raise ValueError("Subscription not found")

        for key, value in kwargs.items():
            if hasattr(subscription, key):
                setattr(subscription, key, value)

        self.db.commit()
        self.db.refresh(subscription)
        return subscription

    def cancel_subscription(
        self,
        subscription_id: str,
        immediate: bool = False,
    ) -> Subscription:
        """Cancel a subscription"""
        subscription = self.db.query(Subscription).filter(
            Subscription.id == subscription_id
        ).first()
        
        if not subscription:
            raise ValueError("Subscription not found")

        subscription.status = SubscriptionStatus.CANCELED
        subscription.canceled_at = datetime.utcnow()
        
        if immediate:
            subscription.end_date = datetime.utcnow()
        else:
            # Cancel at end of billing period
            if subscription.billing_cycle == BillingCycle.MONTHLY:
                subscription.end_date = subscription.start_date + timedelta(days=30)
            else:
                subscription.end_date = subscription.start_date + timedelta(days=365)

        self.db.commit()
        self.db.refresh(subscription)
        return subscription

    def reactivate_subscription(self, subscription_id: str) -> Subscription:
        """Reactivate a canceled subscription"""
        subscription = self.db.query(Subscription).filter(
            Subscription.id == subscription_id
        ).first()
        
        if not subscription:
            raise ValueError("Subscription not found")

        subscription.status = SubscriptionStatus.ACTIVE
        subscription.canceled_at = None
        subscription.end_date = None
        subscription.start_date = datetime.utcnow()

        self.db.commit()
        self.db.refresh(subscription)
        return subscription

    def start_trial(self, user_id: str, plan_id: str) -> Subscription:
        """Start a trial subscription"""
        plan = self.db.query(Plan).filter(Plan.id == plan_id).first()
        if not plan:
            raise ValueError("Plan not found")

        if plan.trial_days <= 0:
            raise ValueError("Plan does not offer a trial")

        subscription = Subscription(
            user_id=user_id,
            plan_id=plan_id,
            billing_cycle=BillingCycle.MONTHLY,  # Default to monthly after trial
            amount=0.0,  # Trial is free
            status=SubscriptionStatus.TRIALING,
            start_date=datetime.utcnow(),
            trial_start=datetime.utcnow(),
            trial_end=datetime.utcnow() + timedelta(days=plan.trial_days),
        )

        self.db.add(subscription)
        self.db.commit()
        self.db.refresh(subscription)
        return subscription

    def has_used_trial(self, user_id: str) -> bool:
        """Check if user has already used a trial"""
        trial_count = (
            self.db.query(Subscription)
            .filter(
                Subscription.user_id == user_id,
                Subscription.trial_start.isnot(None)
            )
            .count()
        )
        return trial_count > 0

    def get_usage_statistics(self, user_id: str) -> UsageResponse:
        """Get user's current usage statistics"""
        subscription = self.get_active_subscription(user_id)
        
        if not subscription:
            # Return free plan limits if no subscription
            free_plan = self.db.query(Plan).filter(Plan.plan_type == PlanType.FREE).first()
            return self._create_usage_response(None, free_plan, user_id)
        
        return self._create_usage_response(subscription, subscription.plan, user_id)

    def get_user_features(self, user_id: str) -> FeatureFlags:
        """Get user's available features based on their plan"""
        subscription = self.get_active_subscription(user_id)
        
        if not subscription:
            # Return free plan features
            free_plan = self.db.query(Plan).filter(Plan.plan_type == PlanType.FREE).first()
            return self._create_feature_flags(free_plan, user_id)
        
        return self._create_feature_flags(subscription.plan, user_id)

    def record_usage(
        self,
        user_id: str,
        feature_type: str,
        quantity: int = 1,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> UsageRecord:
        """Record feature usage for billing and limits"""
        subscription = self.get_active_subscription(user_id)
        
        # Get current billing period
        now = datetime.utcnow()
        if subscription:
            period_start = subscription.last_usage_reset
            if subscription.billing_cycle == BillingCycle.MONTHLY:
                period_end = period_start + timedelta(days=30)
            else:
                period_end = period_start + timedelta(days=365)
        else:
            # For free users, use monthly periods
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            period_end = (period_start + timedelta(days=32)).replace(day=1) - timedelta(seconds=1)

        usage_record = UsageRecord(
            user_id=user_id,
            subscription_id=subscription.id if subscription else None,
            feature_type=feature_type,
            quantity=quantity,
            metadata=str(metadata) if metadata else None,
            usage_date=now,
            billing_period_start=period_start,
            billing_period_end=period_end,
        )

        self.db.add(usage_record)
        
        # Update subscription usage counters
        if subscription:
            if feature_type == "smart_course":
                subscription.smart_courses_used += quantity
            elif feature_type == "avatar_course":
                subscription.avatar_courses_used += quantity

        self.db.commit()
        return usage_record

    def check_usage_limits(self, user_id: str, feature_type: str, quantity: int = 1) -> bool:
        """Check if user can use a feature without exceeding limits"""
        subscription = self.get_active_subscription(user_id)
        
        if not subscription:
            # Check free plan limits
            free_plan = self.db.query(Plan).filter(Plan.plan_type == PlanType.FREE).first()
            if not free_plan:
                return False
            
            current_usage = self._get_current_usage(user_id, feature_type)
            
            if feature_type == "smart_course":
                return current_usage + quantity <= free_plan.max_smart_courses_per_month
            elif feature_type == "avatar_course":
                return current_usage + quantity <= free_plan.max_avatar_courses_per_month
        
        else:
            plan = subscription.plan
            
            if feature_type == "smart_course":
                if plan.max_smart_courses_per_month == -1:  # Unlimited
                    return True
                return subscription.smart_courses_used + quantity <= plan.max_smart_courses_per_month
            
            elif feature_type == "avatar_course":
                if plan.max_avatar_courses_per_month == -1:  # Unlimited
                    return True
                return subscription.avatar_courses_used + quantity <= plan.max_avatar_courses_per_month
        
        return True

    def _apply_promo_code(self, code: str, plan_id: str, user_id: str) -> float:
        """Apply promo code and return discount amount"""
        promo = (
            self.db.query(PromoCode)
            .filter(
                PromoCode.code == code,
                PromoCode.is_active == True,
                PromoCode.valid_from <= datetime.utcnow(),
                PromoCode.valid_until >= datetime.utcnow(),
            )
            .first()
        )
        
        if not promo:
            return 0.0
        
        # Check usage limits
        if promo.max_uses and promo.uses_count >= promo.max_uses:
            return 0.0
        
        # Check if applicable to plan
        if promo.applicable_plans:
            import json
            applicable_plans = json.loads(promo.applicable_plans)
            if plan_id not in applicable_plans:
                return 0.0
        
        # Check first-time only restriction
        if promo.first_time_only:
            existing_subscriptions = (
                self.db.query(Subscription)
                .filter(Subscription.user_id == user_id)
                .count()
            )
            if existing_subscriptions > 0:
                return 0.0
        
        # Calculate discount
        plan = self.db.query(Plan).filter(Plan.id == plan_id).first()
        if not plan:
            return 0.0
        
        if promo.discount_type == "percentage":
            discount = plan.monthly_price * (promo.discount_value / 100)
        else:  # fixed_amount
            discount = promo.discount_value
        
        # Update usage count
        promo.uses_count += 1
        self.db.commit()
        
        return discount

    def _get_current_usage(self, user_id: str, feature_type: str) -> int:
        """Get current usage for a feature in the current billing period"""
        now = datetime.utcnow()
        period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        usage = (
            self.db.query(func.sum(UsageRecord.quantity))
            .filter(
                UsageRecord.user_id == user_id,
                UsageRecord.feature_type == feature_type,
                UsageRecord.usage_date >= period_start,
            )
            .scalar()
        )
        
        return usage or 0

    def _create_usage_response(
        self,
        subscription: Optional[Subscription],
        plan: Plan,
        user_id: str,
    ) -> UsageResponse:
        """Create usage response object"""
        # Get current usage
        smart_courses_used = self._get_current_usage(user_id, "smart_course")
        avatar_courses_used = self._get_current_usage(user_id, "avatar_course")
        api_calls_used = self._get_current_usage(user_id, "api_call")
        
        # Calculate usage percentages
        smart_usage_percent = 0.0
        if plan.max_smart_courses_per_month > 0:
            smart_usage_percent = (smart_courses_used / plan.max_smart_courses_per_month) * 100
        
        avatar_usage_percent = 0.0
        if plan.max_avatar_courses_per_month > 0:
            avatar_usage_percent = (avatar_courses_used / plan.max_avatar_courses_per_month) * 100
        
        # Determine billing period
        now = datetime.utcnow()
        if subscription:
            period_start = subscription.last_usage_reset
            if subscription.billing_cycle == BillingCycle.MONTHLY:
                period_end = period_start + timedelta(days=30)
            else:
                period_end = period_start + timedelta(days=365)
        else:
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            period_end = (period_start + timedelta(days=32)).replace(day=1) - timedelta(seconds=1)
        
        # Check for warnings
        approaching_limits = []
        exceeded_limits = []
        
        if smart_usage_percent >= 80:
            if smart_usage_percent >= 100:
                exceeded_limits.append("smart_courses")
            else:
                approaching_limits.append("smart_courses")
        
        if avatar_usage_percent >= 80:
            if avatar_usage_percent >= 100:
                exceeded_limits.append("avatar_courses")
            else:
                approaching_limits.append("avatar_courses")
        
        return UsageResponse(
            smart_courses_used=smart_courses_used,
            avatar_courses_used=avatar_courses_used,
            api_calls_used=api_calls_used,
            smart_courses_limit=plan.max_smart_courses_per_month,
            avatar_courses_limit=plan.max_avatar_courses_per_month,
            api_calls_limit=-1,  # Unlimited for now
            smart_courses_usage_percent=smart_usage_percent,
            avatar_courses_usage_percent=avatar_usage_percent,
            api_calls_usage_percent=0.0,
            billing_period_start=period_start,
            billing_period_end=period_end,
            current_plan=plan,
            approaching_limits=approaching_limits,
            exceeded_limits=exceeded_limits,
        )

    def _create_feature_flags(self, plan: Plan, user_id: str) -> FeatureFlags:
        """Create feature flags object based on plan"""
        # Get current usage
        smart_courses_used = self._get_current_usage(user_id, "smart_course")
        avatar_courses_used = self._get_current_usage(user_id, "avatar_course")
        api_calls_used = self._get_current_usage(user_id, "api_call")
        
        return FeatureFlags(
            can_create_smart_courses=plan.max_smart_courses_per_month != 0,
            can_create_avatar_courses=plan.max_avatar_courses_per_month > 0,
            max_course_duration=plan.max_course_duration_minutes,
            has_premium_templates=plan.has_premium_templates,
            has_custom_branding=plan.has_custom_branding,
            can_remove_watermark=not plan.has_watermark,
            has_basic_analytics=True,  # All plans have basic analytics
            has_advanced_analytics=plan.has_advanced_analytics,
            has_team_collaboration=plan.has_team_collaboration,
            can_share_courses=True,  # All plans can share
            has_api_access=plan.has_api_access,
            has_webhook_support=plan.has_api_access,
            has_custom_integrations=plan.has_custom_integrations,
            has_priority_support=plan.has_priority_support,
            has_sla_guarantee=plan.has_sla_guarantee,
            has_dedicated_support=plan.plan_type == PlanType.ENTERPRISE,
            has_white_label=plan.has_white_label,
            can_customize_domain=plan.has_white_label,
            max_video_resolution=plan.max_video_resolution,
            supported_formats=["mp4", "webm", "avi"],
            monthly_smart_courses=plan.max_smart_courses_per_month,
            monthly_avatar_courses=plan.max_avatar_courses_per_month,
            monthly_api_calls=-1,  # Unlimited for now
            current_smart_courses=smart_courses_used,
            current_avatar_courses=avatar_courses_used,
            current_api_calls=api_calls_used,
        )
