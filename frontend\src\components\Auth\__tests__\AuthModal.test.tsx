import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { toast } from 'react-toastify'

import AuthModal from '../AuthModal'
import authSlice from '../../../store/slices/authSlice'

// Mock react-toastify
jest.mock('react-toastify', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

// Mock fetch
global.fetch = jest.fn()

const mockStore = configureStore({
  reducer: {
    auth: authSlice,
  },
  preloadedState: {
    auth: {
      user: null,
      token: null,
      isLoading: false,
      error: null,
    },
  },
})

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  )
}

describe('AuthModal', () => {
  const mockOnClose = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    ;(fetch as jest.Mock).mockClear()
  })

  it('renders login form by default', () => {
    renderWithProviders(
      <AuthModal open={true} onClose={mockOnClose} initialMode="login" />
    )

    expect(screen.getByText('Sign In')).toBeInTheDocument()
    expect(screen.getByLabelText(/email or username/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
  })

  it('renders register form when switched', async () => {
    const user = userEvent.setup()
    
    renderWithProviders(
      <AuthModal open={true} onClose={mockOnClose} initialMode="login" />
    )

    await user.click(screen.getByText('Create Account'))

    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/username/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/^password/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument()
  })

  it('validates login form fields', async () => {
    const user = userEvent.setup()
    
    renderWithProviders(
      <AuthModal open={true} onClose={mockOnClose} initialMode="login" />
    )

    const submitButton = screen.getByRole('button', { name: /sign in/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Email or username is required')).toBeInTheDocument()
      expect(screen.getByText('Password is required')).toBeInTheDocument()
    })
  })

  it('validates register form fields', async () => {
    const user = userEvent.setup()
    
    renderWithProviders(
      <AuthModal open={true} onClose={mockOnClose} initialMode="register" />
    )

    const submitButton = screen.getByRole('button', { name: /create account/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Full name is required')).toBeInTheDocument()
      expect(screen.getByText('Username is required')).toBeInTheDocument()
      expect(screen.getByText('Email is required')).toBeInTheDocument()
      expect(screen.getByText('Password is required')).toBeInTheDocument()
    })
  })

  it('validates password confirmation', async () => {
    const user = userEvent.setup()
    
    renderWithProviders(
      <AuthModal open={true} onClose={mockOnClose} initialMode="register" />
    )

    await user.type(screen.getByLabelText(/^password/i), 'password123')
    await user.type(screen.getByLabelText(/confirm password/i), 'different')

    const submitButton = screen.getByRole('button', { name: /create account/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('Passwords do not match')).toBeInTheDocument()
    })
  })

  it('submits login form successfully', async () => {
    const user = userEvent.setup()
    
    ;(fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ access_token: 'mock-token' }),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ id: '1', email: '<EMAIL>' }),
      })

    renderWithProviders(
      <AuthModal open={true} onClose={mockOnClose} initialMode="login" />
    )

    await user.type(screen.getByLabelText(/email or username/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'password123')

    const submitButton = screen.getByRole('button', { name: /sign in/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/v1/auth/login/json', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email_or_username: '<EMAIL>',
          password: 'password123',
        }),
      })
    })

    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Welcome back!')
      expect(mockOnClose).toHaveBeenCalled()
    })
  })

  it('submits register form successfully', async () => {
    const user = userEvent.setup()
    
    ;(fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ id: '1', email: '<EMAIL>' }),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ access_token: 'mock-token' }),
      })

    renderWithProviders(
      <AuthModal open={true} onClose={mockOnClose} initialMode="register" />
    )

    await user.type(screen.getByLabelText(/full name/i), 'Test User')
    await user.type(screen.getByLabelText(/username/i), 'testuser')
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/^password/i), 'password123')
    await user.type(screen.getByLabelText(/confirm password/i), 'password123')

    const submitButton = screen.getByRole('button', { name: /create account/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          full_name: 'Test User',
          username: 'testuser',
          email: '<EMAIL>',
          password: 'password123',
        }),
      })
    })

    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Welcome to Koursia! Your account has been created.')
      expect(mockOnClose).toHaveBeenCalled()
    })
  })

  it('handles login error', async () => {
    const user = userEvent.setup()
    
    ;(fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      json: () => Promise.resolve({ detail: 'Invalid credentials' }),
    })

    renderWithProviders(
      <AuthModal open={true} onClose={mockOnClose} initialMode="login" />
    )

    await user.type(screen.getByLabelText(/email or username/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'wrongpassword')

    const submitButton = screen.getByRole('button', { name: /sign in/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Invalid credentials')
    })
  })

  it('toggles password visibility', async () => {
    const user = userEvent.setup()
    
    renderWithProviders(
      <AuthModal open={true} onClose={mockOnClose} initialMode="login" />
    )

    const passwordInput = screen.getByLabelText(/password/i)
    const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i })

    expect(passwordInput).toHaveAttribute('type', 'password')

    await user.click(toggleButton)
    expect(passwordInput).toHaveAttribute('type', 'text')

    await user.click(toggleButton)
    expect(passwordInput).toHaveAttribute('type', 'password')
  })

  it('closes modal when close button is clicked', async () => {
    const user = userEvent.setup()
    
    renderWithProviders(
      <AuthModal open={true} onClose={mockOnClose} initialMode="login" />
    )

    const closeButton = screen.getByRole('button', { name: /close/i })
    await user.click(closeButton)

    expect(mockOnClose).toHaveBeenCalled()
  })

  it('shows loading state during submission', async () => {
    const user = userEvent.setup()
    
    // Mock a delayed response
    ;(fetch as jest.Mock).mockImplementationOnce(
      () => new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: () => Promise.resolve({ access_token: 'mock-token' }),
      }), 100))
    )

    renderWithProviders(
      <AuthModal open={true} onClose={mockOnClose} initialMode="login" />
    )

    await user.type(screen.getByLabelText(/email or username/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/password/i), 'password123')

    const submitButton = screen.getByRole('button', { name: /sign in/i })
    await user.click(submitButton)

    // Check for loading spinner
    expect(screen.getByRole('progressbar')).toBeInTheDocument()
    expect(submitButton).toBeDisabled()
  })
})
