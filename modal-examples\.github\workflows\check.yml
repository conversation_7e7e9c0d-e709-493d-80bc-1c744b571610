name: Check
on:
  push:
    branches:
      - main
  pull_request:
  workflow_dispatch:

jobs:
  ruff:
    name: Ruff
    runs-on: ubuntu-24.04

    steps:
      - uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3
        with:
          fetch-depth: 1
      - uses: ./.github/actions/setup

      - run: ruff check

      - run: ruff format --check

  nbconvert:
    name: Nb<PERSON>onvert
    runs-on: ubuntu-24.04

    steps:
      - uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3
        with:
          fetch-depth: 1
      - uses: ./.github/actions/setup

      - name: Check notebooks are cleaned
        run: |
          jupyter nbconvert --clear-output --inplace 11_notebooks/*.ipynb
          git diff --quiet 11_notebooks/*.ipynb && git diff --cached --quiet 11_notebooks/*.ipynb || exit 1

  pytest:
    name: Pytest
    runs-on: ubuntu-24.04

    steps:
      - uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3
        with:
          fetch-depth: 1
      - uses: ./.github/actions/setup

      - name: Run
        run: pytest -v .
