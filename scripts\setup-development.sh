#!/bin/bash

# Koursia Platform Development Setup Script
# This script automates the complete setup process for local development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="Koursia"
BACKEND_DIR="backend"
FRONTEND_DIR="frontend"
VENV_NAME="venv"

echo -e "${BLUE}🚀 Starting ${PROJECT_NAME} Development Setup${NC}"
echo "=" * 60

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    local missing_deps=()
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_status "Node.js found: $NODE_VERSION"
    else
        missing_deps+=("Node.js (18.x or higher)")
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_status "npm found: $NPM_VERSION"
    else
        missing_deps+=("npm")
    fi
    
    # Check Python
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version)
        print_status "Python found: $PYTHON_VERSION"
    else
        missing_deps+=("Python 3.9+")
    fi
    
    # Check pip
    if command_exists pip3; then
        PIP_VERSION=$(pip3 --version)
        print_status "pip found: $PIP_VERSION"
    else
        missing_deps+=("pip3")
    fi
    
    # Check Docker
    if command_exists docker; then
        DOCKER_VERSION=$(docker --version)
        print_status "Docker found: $DOCKER_VERSION"
    else
        missing_deps+=("Docker")
    fi
    
    # Check Docker Compose
    if command_exists docker-compose; then
        COMPOSE_VERSION=$(docker-compose --version)
        print_status "Docker Compose found: $COMPOSE_VERSION"
    else
        missing_deps+=("Docker Compose")
    fi
    
    # Check Git
    if command_exists git; then
        GIT_VERSION=$(git --version)
        print_status "Git found: $GIT_VERSION"
    else
        missing_deps+=("Git")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing dependencies:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        echo
        print_info "Please install the missing dependencies and run this script again."
        exit 1
    fi
    
    print_status "All prerequisites satisfied!"
}

# Function to setup environment file
setup_environment() {
    print_info "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.template" ]; then
            cp .env.template .env
            print_status "Created .env file from template"
        else
            print_error ".env.template not found"
            return 1
        fi
    else
        print_warning ".env file already exists, skipping..."
    fi
    
    # Generate secret keys
    print_info "Generating secret keys..."
    
    if command_exists openssl; then
        SECRET_KEY=$(openssl rand -hex 32)
        JWT_SECRET_KEY=$(openssl rand -hex 32)
        
        # Update .env file with generated keys
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s/your_secret_key_here_generate_with_openssl_rand_hex_32/$SECRET_KEY/" .env
            sed -i '' "s/your_jwt_secret_key_here_generate_with_openssl_rand_hex_32/$JWT_SECRET_KEY/" .env
        else
            # Linux
            sed -i "s/your_secret_key_here_generate_with_openssl_rand_hex_32/$SECRET_KEY/" .env
            sed -i "s/your_jwt_secret_key_here_generate_with_openssl_rand_hex_32/$JWT_SECRET_KEY/" .env
        fi
        
        print_status "Generated and configured secret keys"
    else
        print_warning "OpenSSL not found, please manually generate secret keys"
    fi
}

# Function to start database services
start_databases() {
    print_info "Starting database services..."
    
    # Check if docker-compose.dev.yml exists
    if [ ! -f "docker-compose.dev.yml" ]; then
        print_error "docker-compose.dev.yml not found"
        return 1
    fi
    
    # Start PostgreSQL and Redis
    docker-compose -f docker-compose.dev.yml up -d postgres redis
    
    # Wait for services to be ready
    print_info "Waiting for databases to be ready..."
    sleep 10
    
    # Check PostgreSQL
    if docker-compose -f docker-compose.dev.yml exec -T postgres pg_isready -U koursia_user > /dev/null 2>&1; then
        print_status "PostgreSQL is ready"
    else
        print_error "PostgreSQL failed to start"
        return 1
    fi
    
    # Check Redis
    if docker-compose -f docker-compose.dev.yml exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_status "Redis is ready"
    else
        print_error "Redis failed to start"
        return 1
    fi
}

# Function to setup backend
setup_backend() {
    print_info "Setting up backend..."
    
    if [ ! -d "$BACKEND_DIR" ]; then
        print_error "Backend directory not found: $BACKEND_DIR"
        return 1
    fi
    
    cd "$BACKEND_DIR"
    
    # Create virtual environment
    if [ ! -d "$VENV_NAME" ]; then
        print_info "Creating Python virtual environment..."
        python3 -m venv "$VENV_NAME"
        print_status "Virtual environment created"
    else
        print_warning "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source "$VENV_NAME/bin/activate" 2>/dev/null || source "$VENV_NAME/Scripts/activate" 2>/dev/null
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install dependencies
    if [ -f "requirements.txt" ]; then
        print_info "Installing Python dependencies..."
        pip install -r requirements.txt
        print_status "Python dependencies installed"
    else
        print_error "requirements.txt not found"
        cd ..
        return 1
    fi
    
    # Install development dependencies
    if [ -f "requirements-dev.txt" ]; then
        print_info "Installing development dependencies..."
        pip install -r requirements-dev.txt
        print_status "Development dependencies installed"
    fi
    
    # Run database migrations
    print_info "Running database migrations..."
    if command_exists alembic; then
        # Check if alembic is initialized
        if [ ! -d "alembic" ]; then
            print_info "Initializing Alembic..."
            alembic init alembic
        fi
        
        # Create initial migration if needed
        if [ ! "$(ls -A alembic/versions/ 2>/dev/null)" ]; then
            print_info "Creating initial migration..."
            alembic revision --autogenerate -m "Initial migration"
        fi
        
        # Run migrations
        alembic upgrade head
        print_status "Database migrations completed"
    else
        print_warning "Alembic not found, skipping migrations"
    fi
    
    # Initialize database with sample data
    if [ -f "scripts/init_db.py" ]; then
        print_info "Initializing database with sample data..."
        python scripts/init_db.py
        print_status "Database initialized"
    fi
    
    cd ..
}

# Function to setup frontend
setup_frontend() {
    print_info "Setting up frontend..."
    
    if [ ! -d "$FRONTEND_DIR" ]; then
        print_error "Frontend directory not found: $FRONTEND_DIR"
        return 1
    fi
    
    cd "$FRONTEND_DIR"
    
    # Install dependencies
    if [ -f "package.json" ]; then
        print_info "Installing Node.js dependencies..."
        npm install
        print_status "Node.js dependencies installed"
    else
        print_error "package.json not found"
        cd ..
        return 1
    fi
    
    cd ..
}

# Function to run tests
run_tests() {
    print_info "Running tests..."
    
    # Backend tests
    if [ -d "$BACKEND_DIR" ]; then
        cd "$BACKEND_DIR"
        source "$VENV_NAME/bin/activate" 2>/dev/null || source "$VENV_NAME/Scripts/activate" 2>/dev/null
        
        if command_exists pytest; then
            print_info "Running backend tests..."
            pytest --tb=short
            print_status "Backend tests completed"
        else
            print_warning "pytest not found, skipping backend tests"
        fi
        
        cd ..
    fi
    
    # Frontend tests
    if [ -d "$FRONTEND_DIR" ]; then
        cd "$FRONTEND_DIR"
        
        print_info "Running frontend tests..."
        npm test -- --coverage --watchAll=false
        print_status "Frontend tests completed"
        
        cd ..
    fi
}

# Function to start services
start_services() {
    print_info "Starting development services..."
    
    # Create log directory
    mkdir -p logs
    
    print_info "Services will be started in the background..."
    print_info "Backend will be available at: http://localhost:8000"
    print_info "Frontend will be available at: http://localhost:3000"
    print_info "API documentation will be available at: http://localhost:8000/docs"
    
    # Start backend
    if [ -d "$BACKEND_DIR" ]; then
        cd "$BACKEND_DIR"
        source "$VENV_NAME/bin/activate" 2>/dev/null || source "$VENV_NAME/Scripts/activate" 2>/dev/null
        
        print_info "Starting backend server..."
        nohup uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload > ../logs/backend.log 2>&1 &
        BACKEND_PID=$!
        echo $BACKEND_PID > ../logs/backend.pid
        
        cd ..
    fi
    
    # Wait a moment for backend to start
    sleep 3
    
    # Start frontend
    if [ -d "$FRONTEND_DIR" ]; then
        cd "$FRONTEND_DIR"
        
        print_info "Starting frontend server..."
        nohup npm start > ../logs/frontend.log 2>&1 &
        FRONTEND_PID=$!
        echo $FRONTEND_PID > ../logs/frontend.pid
        
        cd ..
    fi
    
    print_status "Services started successfully!"
}

# Function to verify setup
verify_setup() {
    print_info "Verifying setup..."
    
    # Wait for services to start
    sleep 10
    
    # Check backend health
    if curl -f -s http://localhost:8000/health > /dev/null; then
        print_status "Backend health check passed"
    else
        print_warning "Backend health check failed"
    fi
    
    # Check frontend
    if curl -f -s http://localhost:3000 > /dev/null; then
        print_status "Frontend accessibility check passed"
    else
        print_warning "Frontend accessibility check failed"
    fi
    
    # Check database connection
    if curl -f -s http://localhost:8000/api/v1/health/db > /dev/null; then
        print_status "Database connection check passed"
    else
        print_warning "Database connection check failed"
    fi
    
    # Check Redis connection
    if curl -f -s http://localhost:8000/api/v1/health/redis > /dev/null; then
        print_status "Redis connection check passed"
    else
        print_warning "Redis connection check failed"
    fi
}

# Function to display final instructions
show_final_instructions() {
    echo
    echo -e "${GREEN}🎉 Setup completed successfully!${NC}"
    echo "=" * 60
    echo
    echo -e "${BLUE}📋 Next Steps:${NC}"
    echo "1. Open your browser and visit:"
    echo "   • Frontend: http://localhost:3000"
    echo "   • Backend API: http://localhost:8000"
    echo "   • API Documentation: http://localhost:8000/docs"
    echo
    echo "2. Default login credentials:"
    echo "   • Email: <EMAIL>"
    echo "   • Password: admin123"
    echo
    echo "3. Log files are available in the 'logs' directory"
    echo
    echo "4. To stop services:"
    echo "   • Kill processes: kill \$(cat logs/backend.pid logs/frontend.pid)"
    echo "   • Stop databases: docker-compose -f docker-compose.dev.yml down"
    echo
    echo "5. To configure API keys for full functionality:"
    echo "   • Edit the .env file with your actual API credentials"
    echo "   • Restart the backend service"
    echo
    echo -e "${YELLOW}⚠️  Security Reminder:${NC}"
    echo "• Never commit .env files with actual credentials"
    echo "• Use placeholder values in version control"
    echo "• Rotate credentials regularly"
    echo
    echo -e "${GREEN}Happy coding! 🚀${NC}"
}

# Main execution
main() {
    check_prerequisites
    setup_environment
    start_databases
    setup_backend
    setup_frontend
    
    # Ask if user wants to run tests
    read -p "Do you want to run tests? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_tests
    fi
    
    start_services
    verify_setup
    show_final_instructions
}

# Handle script interruption
trap 'print_error "Setup interrupted"; exit 1' INT

# Run main function
main "$@"
