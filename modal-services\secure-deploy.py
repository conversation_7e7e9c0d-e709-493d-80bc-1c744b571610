"""
Secure Modal deployment script for Koursia AI services
This script uses placeholder values and secure secrets management
"""

import modal
import subprocess
import sys
import os
from pathlib import Path
import json

def setup_modal_authentication():
    """Setup Modal authentication securely"""
    print("🔐 Setting up Modal authentication...")
    
    # SECURITY NOTE: Never hardcode credentials
    # Use environment variables or secure input methods
    
    token_id = os.getenv('MODAL_TOKEN_ID') or input("Enter Modal Token ID: ")
    token_secret = os.getenv('MODAL_TOKEN_SECRET') or input("Enter Modal Token Secret: ")
    profile_name = os.getenv('MODAL_PROFILE') or input("Enter Modal Profile Name: ")
    
    try:
        # Set Modal token
        subprocess.run([
            "modal", "token", "set",
            "--token-id", token_id,
            "--token-secret", token_secret,
            "--profile", profile_name
        ], check=True, capture_output=True)
        
        # Activate profile
        subprocess.run([
            "modal", "profile", "activate", profile_name
        ], check=True, capture_output=True)
        
        print("✅ Modal authentication configured successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Modal authentication failed: {e}")
        return False

def create_modal_secrets():
    """Create Modal secrets using secure input methods"""
    print("🔑 Setting up Modal secrets...")
    
    secrets_config = {
        "aws-credentials": {
            "AWS_ACCESS_KEY_ID": "your_aws_access_key_here",
            "AWS_SECRET_ACCESS_KEY": "your_aws_secret_key_here",
            "AWS_S3_BUCKET": "your_s3_bucket_name_here",
            "AWS_REGION": "us-east-1"
        },
        "openai-api-key": {
            "OPENAI_API_KEY": "your_openai_api_key_here"
        },
        "gemini-api-key": {
            "GEMINI_API_KEY": "your_gemini_api_key_here"
        },
        "pexels-api-key": {
            "PEXELS_API_KEY": "your_pexels_api_key_here"
        },
        "pixabay-api-key": {
            "PIXABAY_API_KEY": "your_pixabay_api_key_here"
        },
        "chatterbox-api-key": {
            "CHATTERBOX_API_KEY": "your_chatterbox_api_key_here"
        },
        "kokoro-api-key": {
            "KOKORO_API_KEY": "your_kokoro_api_key_here"
        },
        "ecomimic-api-key": {
            "ECOMIMIC_API_KEY": "your_ecomimic_api_key_here"
        }
    }
    
    print("📝 Please update the secrets manually using the following commands:")
    print("   (Replace placeholder values with your actual credentials)")
    print()
    
    for secret_name, env_vars in secrets_config.items():
        env_string = " ".join([f"{k}={v}" for k, v in env_vars.items()])
        print(f"modal secret create {secret_name} {env_string}")
    
    print()
    print("⚠️  SECURITY REMINDER: Replace all placeholder values with actual credentials")
    print("   Never commit actual credentials to version control")
    
    return True

def verify_modal_setup():
    """Verify Modal setup and permissions"""
    print("🔍 Verifying Modal setup...")
    
    try:
        # Check authentication
        result = subprocess.run(
            ["modal", "token", "current"],
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ Modal authentication verified")
        
        # Check GPU quota
        result = subprocess.run(
            ["modal", "quota"],
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ Modal quota checked")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Modal verification failed: {e}")
        return False

def deploy_ai_services():
    """Deploy AI services to Modal"""
    print("🚀 Deploying AI services to Modal...")
    
    services = [
        ("whisper_service.py", "Whisper Speech Recognition"),
        ("tts_service.py", "Text-to-Speech"),
        ("ecomimic_service.py", "Avatar Generation")
    ]
    
    deployed_services = []
    
    for service_file, service_name in services:
        if Path(service_file).exists():
            try:
                print(f"📦 Deploying {service_name}...")
                subprocess.run([
                    "modal", "deploy", service_file
                ], check=True, capture_output=True)
                
                print(f"✅ {service_name} deployed successfully")
                deployed_services.append(service_name)
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to deploy {service_name}: {e}")
        else:
            print(f"⚠️  Service file {service_file} not found")
    
    return deployed_services

def get_service_endpoints():
    """Get deployed service endpoints"""
    print("🔗 Retrieving service endpoints...")
    
    try:
        result = subprocess.run([
            "modal", "app", "list", "--json"
        ], capture_output=True, text=True, check=True)
        
        apps = json.loads(result.stdout)
        
        koursia_app = None
        for app in apps:
            if app.get("name") == "koursia-ai-services":
                koursia_app = app
                break
        
        if koursia_app:
            endpoints = {
                "whisper": f"https://{koursia_app.get('whisper_webhook', 'not-deployed')}",
                "tts": f"https://{koursia_app.get('tts_webhook', 'not-deployed')}",
                "ecomimic": f"https://{koursia_app.get('ecomimic_webhook', 'not-deployed')}"
            }
            
            print("🌐 Service endpoints:")
            for service, endpoint in endpoints.items():
                print(f"   {service.capitalize()}: {endpoint}")
            
            return endpoints
        else:
            print("⚠️  Koursia app not found in deployed apps")
            return {}
            
    except Exception as e:
        print(f"❌ Failed to get service endpoints: {e}")
        return {}

def test_deployed_services():
    """Test deployed services"""
    print("🧪 Testing deployed services...")
    
    health_tests = [
        ("whisper_service.py::whisper_health", "Whisper"),
        ("tts_service.py::tts_health", "TTS"),
        ("ecomimic_service.py::ecomimic_health", "Ecomimic")
    ]
    
    test_results = {}
    
    for test_endpoint, service_name in health_tests:
        try:
            result = subprocess.run([
                "modal", "run", test_endpoint
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ {service_name} health check passed")
                test_results[service_name] = "PASS"
            else:
                print(f"❌ {service_name} health check failed")
                test_results[service_name] = "FAIL"
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {service_name} health check timed out")
            test_results[service_name] = "TIMEOUT"
        except Exception as e:
            print(f"❌ {service_name} test error: {e}")
            test_results[service_name] = "ERROR"
    
    return test_results

def generate_deployment_report(endpoints, test_results):
    """Generate deployment report"""
    print("📊 Generating deployment report...")
    
    report = {
        "deployment_timestamp": "2024-01-01T00:00:00Z",  # Would use actual timestamp
        "services": {
            "whisper": {
                "endpoint": endpoints.get("whisper", "not-deployed"),
                "status": test_results.get("Whisper", "unknown"),
                "gpu": "A100 80GB",
                "scaling": "auto"
            },
            "tts": {
                "endpoint": endpoints.get("tts", "not-deployed"),
                "status": test_results.get("TTS", "unknown"),
                "gpu": "A100 80GB",
                "scaling": "auto"
            },
            "ecomimic": {
                "endpoint": endpoints.get("ecomimic", "not-deployed"),
                "status": test_results.get("Ecomimic", "unknown"),
                "gpu": "A100 80GB",
                "scaling": "auto"
            }
        },
        "configuration": {
            "gpu_type": "A100 80GB",
            "auto_scaling": True,
            "keep_warm": 1,
            "max_instances": 10,
            "timeout": 1800
        }
    }
    
    # Save report
    with open("deployment_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print("✅ Deployment report saved to deployment_report.json")
    return report

def main():
    """Main deployment function"""
    print("🚀 Starting secure Modal deployment for Koursia AI services")
    print("=" * 60)
    
    # Step 1: Setup authentication
    if not setup_modal_authentication():
        print("❌ Authentication setup failed. Exiting.")
        sys.exit(1)
    
    # Step 2: Create secrets (manual process for security)
    create_modal_secrets()
    
    input("\n⏸️  Press Enter after you have manually created all secrets...")
    
    # Step 3: Verify setup
    if not verify_modal_setup():
        print("❌ Modal setup verification failed. Exiting.")
        sys.exit(1)
    
    # Step 4: Deploy services
    deployed_services = deploy_ai_services()
    
    if not deployed_services:
        print("❌ No services were deployed successfully. Exiting.")
        sys.exit(1)
    
    # Step 5: Get endpoints
    endpoints = get_service_endpoints()
    
    # Step 6: Test services
    test_results = test_deployed_services()
    
    # Step 7: Generate report
    report = generate_deployment_report(endpoints, test_results)
    
    print("\n🎉 Deployment completed!")
    print("=" * 60)
    print(f"✅ Successfully deployed {len(deployed_services)} services")
    print("📋 Next steps:")
    print("   1. Update backend configuration with service endpoints")
    print("   2. Test integration with main Koursia platform")
    print("   3. Monitor service performance and costs")
    print("   4. Set up alerting and monitoring")

if __name__ == "__main__":
    main()
