import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { ThemeProvider, createTheme } from '@mui/material/styles'

import PricingPage from '../PricingPage'
import authSlice from '../../store/slices/authSlice'

const theme = createTheme()

const mockStore = configureStore({
  reducer: {
    auth: authSlice,
  },
  preloadedState: {
    auth: {
      user: null,
      token: null,
      isLoading: false,
      error: null,
    },
  },
})

const mockNavigate = jest.fn()

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}))

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  )
}

describe('PricingPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders all pricing plans', () => {
    renderWithProviders(<PricingPage />)

    expect(screen.getByText('FREE')).toBeInTheDocument()
    expect(screen.getByText('PRO')).toBeInTheDocument()
    expect(screen.getByText('CREATOR')).toBeInTheDocument()
    expect(screen.getByText('ENTERPRISE')).toBeInTheDocument()
  })

  it('shows correct pricing for monthly billing', () => {
    renderWithProviders(<PricingPage />)

    expect(screen.getByText('$0')).toBeInTheDocument() // FREE
    expect(screen.getByText('$49')).toBeInTheDocument() // PRO
    expect(screen.getByText('$99')).toBeInTheDocument() // CREATOR
    expect(screen.getByText('$289')).toBeInTheDocument() // ENTERPRISE
  })

  it('shows correct pricing for yearly billing', async () => {
    const user = userEvent.setup()
    renderWithProviders(<PricingPage />)

    // Toggle to yearly billing
    const yearlyToggle = screen.getByRole('checkbox')
    await user.click(yearlyToggle)

    await waitFor(() => {
      expect(screen.getByText('$0')).toBeInTheDocument() // FREE (unchanged)
      expect(screen.getByText('$39')).toBeInTheDocument() // PRO yearly
      expect(screen.getByText('$79')).toBeInTheDocument() // CREATOR yearly
      expect(screen.getByText('$231')).toBeInTheDocument() // ENTERPRISE yearly
    })
  })

  it('highlights PRO plan as recommended', () => {
    renderWithProviders(<PricingPage />)

    const proCard = screen.getByText('PRO').closest('.MuiCard-root')
    expect(proCard).toHaveStyle('border: 2px solid #667eea')
    expect(screen.getByText('RECOMMENDED')).toBeInTheDocument()
  })

  it('shows trial information for paid plans', () => {
    renderWithProviders(<PricingPage />)

    expect(screen.getByText('3-day free trial')).toBeInTheDocument() // PRO
    expect(screen.getAllByText('3-day free trial')).toHaveLength(2) // PRO and CREATOR
    expect(screen.getByText('7-day free trial')).toBeInTheDocument() // ENTERPRISE
  })

  it('displays plan features correctly', () => {
    renderWithProviders(<PricingPage />)

    // FREE plan features
    expect(screen.getByText('Smart course creation only')).toBeInTheDocument()
    expect(screen.getByText('5-minute course maximum')).toBeInTheDocument()
    expect(screen.getByText('1 course per month')).toBeInTheDocument()

    // PRO plan features
    expect(screen.getByText('30-minute avatar courses')).toBeInTheDocument()
    expect(screen.getByText('10 smart courses per month')).toBeInTheDocument()
    expect(screen.getByText('Premium templates')).toBeInTheDocument()

    // CREATOR plan features
    expect(screen.getByText('3 avatar courses per month')).toBeInTheDocument()
    expect(screen.getByText('Unlimited smart courses')).toBeInTheDocument()
    expect(screen.getByText('Custom branding')).toBeInTheDocument()

    // ENTERPRISE plan features
    expect(screen.getByText('Unlimited avatar courses')).toBeInTheDocument()
    expect(screen.getByText('White-label solution')).toBeInTheDocument()
    expect(screen.getByText('Dedicated support')).toBeInTheDocument()
  })

  it('shows limitations for FREE plan', () => {
    renderWithProviders(<PricingPage />)

    expect(screen.getByText('No avatar instructors')).toBeInTheDocument()
    expect(screen.getByText('Limited customization')).toBeInTheDocument()
    expect(screen.getByText('Watermarked videos')).toBeInTheDocument()
  })

  it('handles FREE plan selection for unauthenticated user', async () => {
    const user = userEvent.setup()
    renderWithProviders(<PricingPage />)

    const freeButton = screen.getByRole('button', { name: /get started free/i })
    await user.click(freeButton)

    expect(mockNavigate).toHaveBeenCalledWith('/register')
  })

  it('handles PRO plan selection', async () => {
    const user = userEvent.setup()
    renderWithProviders(<PricingPage />)

    const proButton = screen.getByRole('button', { name: /start pro trial/i })
    await user.click(proButton)

    expect(mockNavigate).toHaveBeenCalledWith('/checkout', {
      state: { planId: 'pro', isYearly: false }
    })
  })

  it('handles ENTERPRISE plan selection', async () => {
    const user = userEvent.setup()
    
    // Mock window.open
    const mockOpen = jest.fn()
    Object.defineProperty(window, 'open', {
      value: mockOpen,
      writable: true,
    })

    renderWithProviders(<PricingPage />)

    const enterpriseButton = screen.getByRole('button', { name: /contact sales/i })
    await user.click(enterpriseButton)

    expect(mockOpen).toHaveBeenCalledWith(
      'mailto:<EMAIL>?subject=Enterprise Plan Inquiry',
      '_blank'
    )
  })

  it('shows billing toggle with savings indicator', () => {
    renderWithProviders(<PricingPage />)

    expect(screen.getByText('Monthly')).toBeInTheDocument()
    expect(screen.getByText('Yearly')).toBeInTheDocument()
    expect(screen.getByText('Save 20%')).toBeInTheDocument()
  })

  it('displays testimonials section', () => {
    renderWithProviders(<PricingPage />)

    expect(screen.getByText('What Our Users Say')).toBeInTheDocument()
    expect(screen.getByText('Sarah Johnson')).toBeInTheDocument()
    expect(screen.getByText('Michael Chen')).toBeInTheDocument()
    expect(screen.getByText('Emily Rodriguez')).toBeInTheDocument()
  })

  it('shows FAQ section', () => {
    renderWithProviders(<PricingPage />)

    expect(screen.getByText('Frequently Asked Questions')).toBeInTheDocument()
    expect(screen.getByText('Have questions? We\'re here to help.')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /view faq/i })).toBeInTheDocument()
  })

  it('displays feature comparison section', () => {
    renderWithProviders(<PricingPage />)

    expect(screen.getByText('Compare Features')).toBeInTheDocument()
    expect(screen.getByText('Detailed Feature Comparison')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /view full comparison/i })).toBeInTheDocument()
  })

  it('has responsive design elements', () => {
    renderWithProviders(<PricingPage />)

    // Check for responsive grid layout
    const pricingCards = screen.getAllByRole('button', { name: /start|get started|contact/i })
    expect(pricingCards).toHaveLength(4) // One for each plan

    // Check for responsive typography
    expect(screen.getByText('Choose Your Plan')).toBeInTheDocument()
  })

  it('shows correct button text for each plan', () => {
    renderWithProviders(<PricingPage />)

    expect(screen.getByRole('button', { name: /get started free/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /start pro trial/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /start creator trial/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /contact sales/i })).toBeInTheDocument()
  })

  it('applies correct styling to recommended plan', () => {
    renderWithProviders(<PricingPage />)

    const recommendedChip = screen.getByText('RECOMMENDED')
    expect(recommendedChip).toBeInTheDocument()
    
    // Check that the PRO plan card has special styling
    const proCard = screen.getByText('PRO').closest('.MuiCard-root')
    expect(proCard).toHaveClass('MuiCard-root')
  })

  it('handles yearly billing toggle correctly', async () => {
    const user = userEvent.setup()
    renderWithProviders(<PricingPage />)

    const toggle = screen.getByRole('checkbox')
    
    // Initially should be monthly (unchecked)
    expect(toggle).not.toBeChecked()
    
    // Click to switch to yearly
    await user.click(toggle)
    expect(toggle).toBeChecked()
    
    // Click again to switch back to monthly
    await user.click(toggle)
    expect(toggle).not.toBeChecked()
  })

  it('shows plan descriptions', () => {
    renderWithProviders(<PricingPage />)

    expect(screen.getByText('Perfect for trying out our AI course creation')).toBeInTheDocument()
    expect(screen.getByText('Ideal for educators and content creators')).toBeInTheDocument()
    expect(screen.getByText('For professional course creators and trainers')).toBeInTheDocument()
    expect(screen.getByText('Complete solution for organizations')).toBeInTheDocument()
  })
})
