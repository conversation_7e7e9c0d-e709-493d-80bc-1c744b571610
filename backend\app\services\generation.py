"""
Course generation service
"""

import asyncio
import json
from typing import Dict, Any, List
from sqlalchemy.orm import Session
from datetime import datetime

from app.core.database import SessionLocal
from app.models.course import Course, Module, Lesson
from app.models.media import GenerationJob
from app.schemas.generation import CourseGenerationRequest, AvatarGenerationRequest
from app.services.ai.gemini_service import GeminiService
from app.services.ai.tts_service import TTSService
from app.services.media.video_service import VideoService
from app.services.media.slide_service import SlideService


class CourseGenerationService:
    """Service for generating courses using AI"""
    
    @staticmethod
    async def generate_course(
        job_id: str,
        request: CourseGenerationRequest,
        user_id: str
    ) -> None:
        """Generate a complete course"""
        db = SessionLocal()
        try:
            # Get job
            job = db.query(GenerationJob).filter(GenerationJob.id == job_id).first()
            if not job:
                return
            
            # Update job status
            job.status = "processing"
            job.started_at = datetime.utcnow()
            job.progress_percentage = 0.0
            db.commit()
            
            # Step 1: Generate course structure (20%)
            await CourseGenerationService._update_progress(db, job, 10, "Generating course structure...")
            
            gemini_service = GeminiService()
            course_structure = await gemini_service.generate_course_structure(request)
            
            await CourseGenerationService._update_progress(db, job, 20, "Course structure generated")
            
            # Step 2: Create course in database (25%)
            course = Course(
                title=request.title,
                description=request.description,
                difficulty=request.difficulty,
                duration_minutes=request.duration_minutes,
                language=request.language,
                target_audience=request.target_audience,
                learning_objectives=request.learning_objectives,
                voice_settings=request.voice_settings.dict(),
                slide_template=request.slide_template,
                creator_id=user_id,
                status="generating"
            )
            
            db.add(course)
            db.commit()
            db.refresh(course)
            
            # Update job with course ID
            job.course_id = course.id
            db.commit()
            
            await CourseGenerationService._update_progress(db, job, 25, "Course created in database")
            
            # Step 3: Generate detailed content for each module (50%)
            modules_data = []
            for i, module_data in enumerate(course_structure.get("modules", [])):
                progress = 25 + (i + 1) * (25 / len(course_structure.get("modules", [])))
                await CourseGenerationService._update_progress(
                    db, job, progress, f"Generating content for module {i + 1}..."
                )
                
                # Generate detailed module content
                detailed_content = await gemini_service.generate_module_content(
                    module_data, request
                )
                modules_data.append(detailed_content)
            
            await CourseGenerationService._update_progress(db, job, 50, "Module content generated")
            
            # Step 4: Create modules and lessons in database (60%)
            for module_data in modules_data:
                module = Module(
                    title=module_data["title"],
                    description=module_data["description"],
                    order_index=module_data["order_index"],
                    objectives=module_data.get("objectives", []),
                    course_id=course.id
                )
                
                db.add(module)
                db.commit()
                db.refresh(module)
                
                # Create lessons
                for lesson_data in module_data.get("lessons", []):
                    lesson = Lesson(
                        title=lesson_data["title"],
                        description=lesson_data["description"],
                        content=lesson_data["script"],
                        order_index=lesson_data["order_index"],
                        module_id=module.id
                    )
                    
                    db.add(lesson)
                
                db.commit()
            
            await CourseGenerationService._update_progress(db, job, 60, "Database structure created")
            
            # Step 5: Generate audio (80%)
            await CourseGenerationService._update_progress(db, job, 65, "Generating audio...")
            
            tts_service = TTSService()
            audio_files = await tts_service.generate_course_audio(course.id, request.voice_settings)
            
            await CourseGenerationService._update_progress(db, job, 80, "Audio generated")
            
            # Step 6: Generate slides (90%)
            await CourseGenerationService._update_progress(db, job, 85, "Generating slides...")
            
            slide_service = SlideService()
            slide_files = await slide_service.generate_course_slides(course.id, request.slide_template)
            
            await CourseGenerationService._update_progress(db, job, 90, "Slides generated")
            
            # Step 7: Assemble videos (100%)
            await CourseGenerationService._update_progress(db, job, 95, "Assembling videos...")
            
            video_service = VideoService()
            video_files = await video_service.assemble_course_videos(course.id, audio_files, slide_files)
            
            # Update course status
            course.status = "ready"
            db.commit()
            
            # Complete job
            job.status = "completed"
            job.progress_percentage = 100.0
            job.completed_at = datetime.utcnow()
            job.output_data = {
                "course_id": str(course.id),
                "audio_files": audio_files,
                "slide_files": slide_files,
                "video_files": video_files
            }
            db.commit()
            
        except Exception as e:
            # Handle error
            job.status = "failed"
            job.error_message = str(e)
            job.completed_at = datetime.utcnow()
            db.commit()
            
        finally:
            db.close()
    
    @staticmethod
    async def generate_avatar_course(
        job_id: str,
        request: AvatarGenerationRequest,
        user_id: str
    ) -> None:
        """Generate a course with avatar"""
        # Similar to generate_course but with avatar integration
        # This would include additional steps for avatar generation
        pass
    
    @staticmethod
    async def regenerate_course(
        job_id: str,
        course_id: str,
        user_id: str
    ) -> None:
        """Regenerate content for an existing course"""
        # Implementation for regenerating course content
        pass
    
    @staticmethod
    async def _update_progress(
        db: Session,
        job: GenerationJob,
        progress: float,
        message: str
    ) -> None:
        """Update job progress"""
        job.progress_percentage = progress
        # In a real implementation, you might also emit WebSocket events here
        db.commit()
        
        # Small delay to simulate processing time
        await asyncio.sleep(0.1)
