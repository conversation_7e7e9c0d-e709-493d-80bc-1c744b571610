import React, { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Divider,
  IconButton,
  Tabs,
  Tab,
  InputAdornment,
  Link,
} from '@mui/material'
import {
  Close,
  Visibility,
  VisibilityOff,
  Email,
  Person,
  Lock,
} from '@mui/icons-material'
import { useForm } from 'react-hook-form'
import { toast } from 'react-toastify'

import { useAppDispatch, useAppSelector } from '../../hooks/redux'
import { loginStart, loginSuccess, loginFailure, selectAuthLoading, selectAuthError } from '../../store/slices/authSlice'

interface AuthModalProps {
  open: boolean
  onClose: () => void
  initialMode?: 'login' | 'register'
}

interface LoginFormData {
  email_or_username: string
  password: string
}

interface RegisterFormData {
  email: string
  username: string
  full_name: string
  password: string
  confirmPassword: string
}

const AuthModal: React.FC<AuthModalProps> = ({ open, onClose, initialMode = 'login' }) => {
  const dispatch = useAppDispatch()
  const isLoading = useAppSelector(selectAuthLoading)
  const error = useAppSelector(selectAuthError)
  
  const [mode, setMode] = useState<'login' | 'register'>(initialMode)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const loginForm = useForm<LoginFormData>()
  const registerForm = useForm<RegisterFormData>()

  const handleModeChange = (newMode: 'login' | 'register') => {
    setMode(newMode)
    loginForm.reset()
    registerForm.reset()
  }

  const onLoginSubmit = async (data: LoginFormData) => {
    dispatch(loginStart())

    try {
      const response = await fetch('/api/v1/auth/login/json', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        const result = await response.json()
        
        // Get user info
        const userResponse = await fetch('/api/v1/auth/me', {
          headers: {
            'Authorization': `Bearer ${result.access_token}`,
          },
        })

        if (userResponse.ok) {
          const user = await userResponse.json()
          dispatch(loginSuccess({ user, token: result.access_token }))
          toast.success('Welcome back!')
          onClose()
        } else {
          throw new Error('Failed to get user information')
        }
      } else {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Login failed')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed'
      dispatch(loginFailure(errorMessage))
      toast.error(errorMessage)
    }
  }

  const onRegisterSubmit = async (data: RegisterFormData) => {
    dispatch(loginStart())

    try {
      const { confirmPassword, ...registerData } = data
      
      const response = await fetch('/api/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registerData),
      })

      if (response.ok) {
        const user = await response.json()
        
        // Auto-login after registration
        const loginResponse = await fetch('/api/v1/auth/login/json', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email_or_username: data.email,
            password: data.password,
          }),
        })

        if (loginResponse.ok) {
          const loginResult = await loginResponse.json()
          dispatch(loginSuccess({ user, token: loginResult.access_token }))
          toast.success('Welcome to Koursia! Your account has been created.')
          onClose()
        } else {
          toast.success('Registration successful! Please log in.')
          setMode('login')
        }
      } else {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Registration failed')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Registration failed'
      dispatch(loginFailure(errorMessage))
      toast.error(errorMessage)
    }
  }

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="sm" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          overflow: 'hidden',
        }
      }}
    >
      <DialogTitle sx={{ p: 0 }}>
        <Box sx={{ position: 'relative' }}>
          <IconButton
            onClick={onClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              zIndex: 1,
            }}
          >
            <Close />
          </IconButton>
          
          <Box
            sx={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              p: 3,
              textAlign: 'center',
            }}
          >
            <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
              Koursia
            </Typography>
            <Typography variant="subtitle1">
              AI-Powered Course Creation Platform
            </Typography>
          </Box>
          
          <Tabs
            value={mode}
            onChange={(_, newValue) => handleModeChange(newValue)}
            variant="fullWidth"
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontSize: '1rem',
                fontWeight: 500,
              },
            }}
          >
            <Tab label="Sign In" value="login" />
            <Tab label="Create Account" value="register" />
          </Tabs>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {mode === 'login' ? (
          <Box component="form" onSubmit={loginForm.handleSubmit(onLoginSubmit)}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="email_or_username"
              label="Email or Username"
              autoComplete="email"
              autoFocus
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Email color="action" />
                  </InputAdornment>
                ),
              }}
              error={!!loginForm.formState.errors.email_or_username}
              helperText={loginForm.formState.errors.email_or_username?.message}
              {...loginForm.register('email_or_username', {
                required: 'Email or username is required',
              })}
            />
            
            <TextField
              margin="normal"
              required
              fullWidth
              label="Password"
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="current-password"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              error={!!loginForm.formState.errors.password}
              helperText={loginForm.formState.errors.password?.message}
              {...loginForm.register('password', {
                required: 'Password is required',
              })}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2, py: 1.5 }}
              disabled={isLoading}
            >
              {isLoading ? <CircularProgress size={24} /> : 'Sign In'}
            </Button>

            <Box textAlign="center">
              <Link href="#" variant="body2" sx={{ textDecoration: 'none' }}>
                Forgot your password?
              </Link>
            </Box>
          </Box>
        ) : (
          <Box component="form" onSubmit={registerForm.handleSubmit(onRegisterSubmit)}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="full_name"
              label="Full Name"
              autoComplete="name"
              autoFocus
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Person color="action" />
                  </InputAdornment>
                ),
              }}
              error={!!registerForm.formState.errors.full_name}
              helperText={registerForm.formState.errors.full_name?.message}
              {...registerForm.register('full_name', {
                required: 'Full name is required',
                minLength: {
                  value: 2,
                  message: 'Full name must be at least 2 characters',
                },
              })}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="Username"
              autoComplete="username"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Person color="action" />
                  </InputAdornment>
                ),
              }}
              error={!!registerForm.formState.errors.username}
              helperText={registerForm.formState.errors.username?.message}
              {...registerForm.register('username', {
                required: 'Username is required',
                minLength: {
                  value: 3,
                  message: 'Username must be at least 3 characters',
                },
                pattern: {
                  value: /^[a-zA-Z0-9_-]+$/,
                  message: 'Username can only contain letters, numbers, underscores, and hyphens',
                },
              })}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="Email Address"
              autoComplete="email"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Email color="action" />
                  </InputAdornment>
                ),
              }}
              error={!!registerForm.formState.errors.email}
              helperText={registerForm.formState.errors.email?.message}
              {...registerForm.register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address',
                },
              })}
            />
            
            <TextField
              margin="normal"
              required
              fullWidth
              label="Password"
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="new-password"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              error={!!registerForm.formState.errors.password}
              helperText={registerForm.formState.errors.password?.message}
              {...registerForm.register('password', {
                required: 'Password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters',
                },
              })}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              label="Confirm Password"
              type={showConfirmPassword ? 'text' : 'password'}
              id="confirmPassword"
              autoComplete="new-password"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      edge="end"
                    >
                      {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              error={!!registerForm.formState.errors.confirmPassword}
              helperText={registerForm.formState.errors.confirmPassword?.message}
              {...registerForm.register('confirmPassword', {
                required: 'Please confirm your password',
                validate: (value) =>
                  value === registerForm.watch('password') || 'Passwords do not match',
              })}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2, py: 1.5 }}
              disabled={isLoading}
            >
              {isLoading ? <CircularProgress size={24} /> : 'Create Account'}
            </Button>

            <Typography variant="body2" color="text.secondary" textAlign="center">
              By creating an account, you agree to our{' '}
              <Link href="#" sx={{ textDecoration: 'none' }}>
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link href="#" sx={{ textDecoration: 'none' }}>
                Privacy Policy
              </Link>
            </Typography>
          </Box>
        )}
      </DialogContent>
    </Dialog>
  )
}

export default AuthModal
