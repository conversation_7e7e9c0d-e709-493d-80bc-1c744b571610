"""
Gemini AI service for content generation
"""

import google.generativeai as genai
from typing import Dict, Any, List
import json
import asyncio

from app.core.config import settings
from app.schemas.generation import CourseGenerationRequest


class GeminiService:
    """Service for interacting with Google Gemini API"""
    
    def __init__(self):
        if settings.GEMINI_API_KEY:
            genai.configure(api_key=settings.GEMINI_API_KEY)
            self.model = genai.GenerativeModel('gemini-pro')
        else:
            raise ValueError("GEMINI_API_KEY not configured")
    
    async def generate_course_structure(self, request: CourseGenerationRequest) -> Dict[str, Any]:
        """Generate high-level course structure"""
        
        prompt = f"""
        Create a detailed course structure for the following course:
        
        Title: {request.title}
        Description: {request.description}
        Target Audience: {request.target_audience}
        Difficulty: {request.difficulty}
        Duration: {request.duration_minutes} minutes
        Language: {request.language}
        Learning Objectives: {', '.join(request.learning_objectives)}
        Topics to Cover: {', '.join(request.topics)}
        
        Please create a JSON structure with the following format:
        {{
            "course_overview": {{
                "title": "Course Title",
                "description": "Detailed course description",
                "estimated_duration": {request.duration_minutes},
                "difficulty_level": "{request.difficulty}",
                "prerequisites": ["prerequisite1", "prerequisite2"]
            }},
            "modules": [
                {{
                    "title": "Module Title",
                    "description": "Module description",
                    "order_index": 1,
                    "estimated_duration": 30,
                    "learning_objectives": ["objective1", "objective2"],
                    "lesson_count": 3
                }}
            ]
        }}
        
        Create {max(3, min(8, request.duration_minutes // 30))} modules that logically progress through the topics.
        Each module should be 20-40 minutes long.
        Ensure the content is appropriate for {request.difficulty} level learners.
        """
        
        try:
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            
            # Extract JSON from response
            content = response.text
            
            # Find JSON in the response
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                return json.loads(json_str)
            else:
                raise ValueError("No valid JSON found in response")
                
        except Exception as e:
            # Fallback structure if API fails
            return self._create_fallback_structure(request)
    
    async def generate_module_content(
        self, 
        module_data: Dict[str, Any], 
        request: CourseGenerationRequest
    ) -> Dict[str, Any]:
        """Generate detailed content for a specific module"""
        
        prompt = f"""
        Create detailed lesson content for this module:
        
        Module Title: {module_data['title']}
        Module Description: {module_data['description']}
        Target Duration: {module_data.get('estimated_duration', 30)} minutes
        Learning Objectives: {', '.join(module_data.get('learning_objectives', []))}
        Course Context: {request.title} - {request.description}
        Difficulty Level: {request.difficulty}
        Target Audience: {request.target_audience}
        
        Create {module_data.get('lesson_count', 3)} lessons for this module.
        
        Please provide a JSON structure with the following format:
        {{
            "title": "{module_data['title']}",
            "description": "{module_data['description']}",
            "order_index": {module_data['order_index']},
            "objectives": {module_data.get('learning_objectives', [])},
            "lessons": [
                {{
                    "title": "Lesson Title",
                    "description": "Lesson description",
                    "order_index": 1,
                    "estimated_duration": 10,
                    "script": "Detailed narration script for this lesson. This should be conversational and engaging, suitable for voice synthesis. Include natural pauses and emphasis where appropriate.",
                    "key_points": ["point1", "point2", "point3"],
                    "slide_content": [
                        {{
                            "slide_number": 1,
                            "title": "Slide Title",
                            "content": "Slide content in markdown format",
                            "speaker_notes": "Additional notes for the narrator"
                        }}
                    ]
                }}
            ]
        }}
        
        Each lesson script should be detailed enough for text-to-speech conversion.
        Make the content engaging and educational.
        Include practical examples and clear explanations.
        """
        
        try:
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            content = response.text
            
            # Extract JSON from response
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                return json.loads(json_str)
            else:
                raise ValueError("No valid JSON found in response")
                
        except Exception as e:
            # Fallback content if API fails
            return self._create_fallback_module_content(module_data)
    
    async def generate_quiz_questions(
        self, 
        lesson_content: str, 
        difficulty: str
    ) -> List[Dict[str, Any]]:
        """Generate quiz questions for a lesson"""
        
        prompt = f"""
        Based on this lesson content, create 3-5 multiple choice quiz questions:
        
        Lesson Content: {lesson_content}
        Difficulty Level: {difficulty}
        
        Format as JSON:
        [
            {{
                "question": "Question text",
                "options": ["Option A", "Option B", "Option C", "Option D"],
                "correct_answer": 0,
                "explanation": "Why this answer is correct"
            }}
        ]
        """
        
        try:
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            content = response.text
            
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                return json.loads(json_str)
            else:
                return []
                
        except Exception as e:
            return []
    
    def _create_fallback_structure(self, request: CourseGenerationRequest) -> Dict[str, Any]:
        """Create a basic fallback structure if API fails"""
        num_modules = max(3, min(8, request.duration_minutes // 30))
        
        return {
            "course_overview": {
                "title": request.title,
                "description": request.description,
                "estimated_duration": request.duration_minutes,
                "difficulty_level": request.difficulty,
                "prerequisites": []
            },
            "modules": [
                {
                    "title": f"Module {i + 1}: {topic}",
                    "description": f"Learn about {topic}",
                    "order_index": i + 1,
                    "estimated_duration": request.duration_minutes // num_modules,
                    "learning_objectives": [f"Understand {topic}"],
                    "lesson_count": 3
                }
                for i, topic in enumerate(request.topics[:num_modules])
            ]
        }
    
    def _create_fallback_module_content(self, module_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback module content if API fails"""
        return {
            "title": module_data["title"],
            "description": module_data["description"],
            "order_index": module_data["order_index"],
            "objectives": module_data.get("learning_objectives", []),
            "lessons": [
                {
                    "title": f"Lesson {i + 1}",
                    "description": f"Introduction to lesson {i + 1}",
                    "order_index": i + 1,
                    "estimated_duration": 10,
                    "script": f"Welcome to lesson {i + 1}. In this lesson, we will explore the key concepts and practical applications.",
                    "key_points": ["Key point 1", "Key point 2"],
                    "slide_content": [
                        {
                            "slide_number": 1,
                            "title": f"Lesson {i + 1} Overview",
                            "content": "# Lesson Overview\n\n- Introduction\n- Key concepts\n- Summary",
                            "speaker_notes": "Introduce the lesson objectives"
                        }
                    ]
                }
                for i in range(module_data.get("lesson_count", 3))
            ]
        }
