name: Build and run example

on:
  workflow_dispatch:
  schedule:
    - cron: "23 * * * *"

env:
  TERM: linux
  TERMINFO: /etc/terminfo
  MODAL_TOKEN_ID: ${{ secrets.MODAL_MODAL_LABS_TOKEN_ID }}
  MODAL_TOKEN_SECRET: ${{ secrets.MODAL_MODAL_LABS_TOKEN_SECRET }}
  MODAL_ENVIRONMENT: examples

jobs:
  build-and-run:
    name: Build a random example from scratch and run it
    runs-on: ubuntu-24.04
    timeout-minutes: 60
    steps:
      - name: Checkout Repository
        uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3
        with:
          fetch-depth: 1
      - uses: ./.github/actions/setup

      - name: Run a random example with MODAL_IGNORE_CACHE set
        run: |
          MODAL_IGNORE_CACHE=1 python3 -m internal.run_example
