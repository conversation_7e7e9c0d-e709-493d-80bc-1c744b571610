"""
Modal Whisper service for speech recognition and transcription
"""

import modal
import whisper
import tempfile
import json
import asyncio
from pathlib import Path
from typing import Dict, Any, List, Optional
import aiofiles
import aiohttp
from datetime import datetime

from modal_config import (
    app,
    whisper_image,
    gpu_function,
    webhook_config,
    models_volume,
    temp_volume,
    MODEL_CONFIGS,
)

# Load Whisper model on container startup
@app.function(
    image=whisper_image,
    gpu=modal.gpu.A100(count=1, memory=80),
    volumes={
        "/models": models_volume,
        "/tmp": temp_volume,
    },
    timeout=1800,
    keep_warm=1,
)
class WhisperService:
    """Whisper speech recognition service"""
    
    def __init__(self):
        self.models = {}
        self.model_config = MODEL_CONFIGS["whisper"]
    
    @modal.enter()
    def load_models(self):
        """Load Whisper models on container startup"""
        print("Loading Whisper models...")
        
        for model_name in self.model_config["models"]:
            try:
                print(f"Loading {model_name} model...")
                self.models[model_name] = whisper.load_model(
                    model_name,
                    device="cuda",
                    download_root="/models/whisper"
                )
                print(f"✅ {model_name} model loaded successfully")
            except Exception as e:
                print(f"❌ Failed to load {model_name} model: {e}")
        
        print(f"Loaded {len(self.models)} Whisper models")
    
    @modal.method()
    def transcribe_audio(
        self,
        audio_url: str,
        model_name: str = "base",
        language: Optional[str] = None,
        task: str = "transcribe",
        options: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Transcribe audio file using Whisper
        
        Args:
            audio_url: URL or path to audio file
            model_name: Whisper model to use (base, small, medium, large)
            language: Source language (auto-detect if None)
            task: transcribe or translate
            options: Additional Whisper options
        
        Returns:
            Transcription result with text, segments, and metadata
        """
        try:
            # Validate model
            if model_name not in self.models:
                model_name = self.model_config["default_model"]
            
            model = self.models[model_name]
            
            # Download audio file if URL provided
            if audio_url.startswith(('http://', 'https://')):
                audio_path = self._download_audio(audio_url)
            else:
                audio_path = audio_url
            
            # Prepare transcription options
            transcribe_options = {
                "task": task,
                "language": language,
                "verbose": False,
                "word_timestamps": True,
                "condition_on_previous_text": False,
            }
            
            if options:
                transcribe_options.update(options)
            
            # Perform transcription
            print(f"Transcribing audio with {model_name} model...")
            result = model.transcribe(audio_path, **transcribe_options)
            
            # Process result
            processed_result = {
                "text": result["text"].strip(),
                "language": result.get("language", "unknown"),
                "segments": [
                    {
                        "id": seg["id"],
                        "start": seg["start"],
                        "end": seg["end"],
                        "text": seg["text"].strip(),
                        "words": seg.get("words", []),
                    }
                    for seg in result.get("segments", [])
                ],
                "metadata": {
                    "model": model_name,
                    "task": task,
                    "duration": result.get("segments", [{}])[-1].get("end", 0) if result.get("segments") else 0,
                    "timestamp": datetime.utcnow().isoformat(),
                },
            }
            
            # Clean up temporary file
            if audio_url.startswith(('http://', 'https://')):
                Path(audio_path).unlink(missing_ok=True)
            
            print(f"✅ Transcription completed: {len(processed_result['text'])} characters")
            return processed_result
            
        except Exception as e:
            print(f"❌ Transcription failed: {e}")
            return {
                "error": str(e),
                "text": "",
                "segments": [],
                "metadata": {
                    "model": model_name,
                    "task": task,
                    "timestamp": datetime.utcnow().isoformat(),
                    "error": True,
                },
            }
    
    @modal.method()
    def generate_subtitles(
        self,
        audio_url: str,
        format: str = "srt",
        model_name: str = "base",
        language: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Generate subtitle file from audio
        
        Args:
            audio_url: URL or path to audio file
            format: Subtitle format (srt, vtt, ass)
            model_name: Whisper model to use
            language: Source language
        
        Returns:
            Subtitle content and metadata
        """
        try:
            # Get transcription
            transcription = self.transcribe_audio(
                audio_url=audio_url,
                model_name=model_name,
                language=language,
            )
            
            if "error" in transcription:
                return transcription
            
            # Generate subtitle content
            if format.lower() == "srt":
                subtitle_content = self._generate_srt(transcription["segments"])
            elif format.lower() == "vtt":
                subtitle_content = self._generate_vtt(transcription["segments"])
            elif format.lower() == "ass":
                subtitle_content = self._generate_ass(transcription["segments"])
            else:
                raise ValueError(f"Unsupported subtitle format: {format}")
            
            return {
                "content": subtitle_content,
                "format": format.lower(),
                "metadata": transcription["metadata"],
            }
            
        except Exception as e:
            print(f"❌ Subtitle generation failed: {e}")
            return {
                "error": str(e),
                "content": "",
                "format": format,
                "metadata": {
                    "timestamp": datetime.utcnow().isoformat(),
                    "error": True,
                },
            }
    
    @modal.method()
    def batch_transcribe(
        self,
        audio_urls: List[str],
        model_name: str = "base",
        language: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Batch transcribe multiple audio files
        
        Args:
            audio_urls: List of audio URLs or paths
            model_name: Whisper model to use
            language: Source language
        
        Returns:
            List of transcription results
        """
        results = []
        
        for i, audio_url in enumerate(audio_urls):
            print(f"Processing audio {i+1}/{len(audio_urls)}")
            result = self.transcribe_audio(
                audio_url=audio_url,
                model_name=model_name,
                language=language,
            )
            results.append(result)
        
        return results
    
    def _download_audio(self, url: str) -> str:
        """Download audio file from URL"""
        import requests
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
            for chunk in response.iter_content(chunk_size=8192):
                tmp_file.write(chunk)
            return tmp_file.name
    
    def _generate_srt(self, segments: List[Dict[str, Any]]) -> str:
        """Generate SRT subtitle format"""
        srt_content = []
        
        for i, segment in enumerate(segments, 1):
            start_time = self._format_timestamp(segment["start"])
            end_time = self._format_timestamp(segment["end"])
            text = segment["text"].strip()
            
            srt_content.append(f"{i}")
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(text)
            srt_content.append("")
        
        return "\n".join(srt_content)
    
    def _generate_vtt(self, segments: List[Dict[str, Any]]) -> str:
        """Generate WebVTT subtitle format"""
        vtt_content = ["WEBVTT", ""]
        
        for segment in segments:
            start_time = self._format_timestamp(segment["start"], vtt=True)
            end_time = self._format_timestamp(segment["end"], vtt=True)
            text = segment["text"].strip()
            
            vtt_content.append(f"{start_time} --> {end_time}")
            vtt_content.append(text)
            vtt_content.append("")
        
        return "\n".join(vtt_content)
    
    def _generate_ass(self, segments: List[Dict[str, Any]]) -> str:
        """Generate ASS subtitle format"""
        ass_header = """[Script Info]
Title: Koursia Generated Subtitles
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,20,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
        
        ass_content = [ass_header]
        
        for segment in segments:
            start_time = self._format_timestamp_ass(segment["start"])
            end_time = self._format_timestamp_ass(segment["end"])
            text = segment["text"].strip()
            
            ass_content.append(f"Dialogue: 0,{start_time},{end_time},Default,,0,0,0,,{text}")
        
        return "\n".join(ass_content)
    
    def _format_timestamp(self, seconds: float, vtt: bool = False) -> str:
        """Format timestamp for SRT/VTT"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        if vtt:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}.{milliseconds:03d}"
        else:
            return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
    
    def _format_timestamp_ass(self, seconds: float) -> str:
        """Format timestamp for ASS"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        return f"{hours:01d}:{minutes:02d}:{secs:05.2f}"

# Create webhook endpoint
@app.function(
    image=whisper_image,
    gpu=modal.gpu.A100(count=1, memory=80),
    timeout=1800,
    keep_warm=1,
)
@modal.web_endpoint(**webhook_config)
async def whisper_webhook(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """Webhook endpoint for Whisper transcription"""
    
    service = WhisperService()
    
    action = request_data.get("action", "transcribe")
    
    if action == "transcribe":
        return service.transcribe_audio(
            audio_url=request_data["audio_url"],
            model_name=request_data.get("model", "base"),
            language=request_data.get("language"),
            task=request_data.get("task", "transcribe"),
            options=request_data.get("options"),
        )
    
    elif action == "subtitles":
        return service.generate_subtitles(
            audio_url=request_data["audio_url"],
            format=request_data.get("format", "srt"),
            model_name=request_data.get("model", "base"),
            language=request_data.get("language"),
        )
    
    elif action == "batch":
        return {
            "results": service.batch_transcribe(
                audio_urls=request_data["audio_urls"],
                model_name=request_data.get("model", "base"),
                language=request_data.get("language"),
            )
        }
    
    else:
        return {"error": f"Unknown action: {action}"}

# Health check endpoint
@app.function(image=whisper_image)
@modal.web_endpoint(method="GET")
def whisper_health() -> Dict[str, Any]:
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "whisper",
        "timestamp": datetime.utcnow().isoformat(),
        "models": MODEL_CONFIGS["whisper"]["models"],
    }
