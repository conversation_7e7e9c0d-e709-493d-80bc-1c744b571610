# Koursia Platform Development Setup Guide

This guide provides step-by-step instructions to set up the complete Koursia platform for local development.

## 📋 Prerequisites

### System Requirements
- **Node.js**: 18.x or higher
- **Python**: 3.9 or higher
- **Docker**: Latest version
- **Docker Compose**: Latest version
- **Git**: Latest version

### Development Tools (Recommended)
- **VS Code** with extensions:
  - Python
  - TypeScript and JavaScript
  - Docker
  - PostgreSQL
  - Thunder Client (for API testing)

## 🔧 Step 1: Environment Configuration

### 1.1 Create Environment File
```bash
# Copy the template to create your .env file
cp .env.template .env
```

### 1.2 Generate Secret Keys
```bash
# Generate SECRET_KEY
openssl rand -hex 32

# Generate JWT_SECRET_KEY
openssl rand -hex 32
```

### 1.3 Configure Basic Environment Variables
Edit your `.env` file and update these essential variables:

```bash
# Application Secrets (replace with generated values)
SECRET_KEY=your_generated_secret_key_here
JWT_SECRET_KEY=your_generated_jwt_secret_key_here

# Database Configuration (can keep defaults for development)
POSTGRES_DB=koursia
POSTGRES_USER=koursia_user
POSTGRES_PASSWORD=koursia_dev_password

# Redis Configuration (can keep defaults)
REDIS_URL=redis://localhost:6379/0

# Application URLs (keep defaults for local development)
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8000
```

### 1.4 API Keys Configuration (Optional for Basic Setup)
For full functionality, you'll need to add your API keys:

```bash
# AWS S3 (for file storage)
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET=your_s3_bucket_name

# AI Services (for course generation)
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key

# Stock Media (for course images)
PEXELS_API_KEY=your_pexels_api_key
PIXABAY_API_KEY=your_pixabay_api_key

# TTS Services (for voice generation)
CHATTERBOX_API_KEY=your_chatterbox_api_key
KOKORO_API_KEY=your_kokoro_api_key

# Avatar Generation
ECOMIMIC_API_KEY=your_ecomimic_api_key
```

**Security Note**: Never commit your `.env` file with actual credentials to version control.

## 🐳 Step 2: Database Setup with Docker

### 2.1 Start Database Services
```bash
# Start PostgreSQL and Redis using Docker Compose
docker-compose up -d postgres redis

# Verify services are running
docker-compose ps
```

### 2.2 Verify Database Connection
```bash
# Test PostgreSQL connection
docker exec -it koursia_postgres psql -U koursia_user -d koursia -c "SELECT version();"

# Test Redis connection
docker exec -it koursia_redis redis-cli ping
```

## 🐍 Step 3: Backend Setup (FastAPI)

### 3.1 Navigate to Backend Directory
```bash
cd backend
```

### 3.2 Create Python Virtual Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 3.3 Install Backend Dependencies
```bash
# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt

# Install development dependencies
pip install -r requirements-dev.txt
```

### 3.4 Run Database Migrations
```bash
# Create initial migration (if not exists)
alembic revision --autogenerate -m "Initial migration"

# Run migrations
alembic upgrade head
```

### 3.5 Seed Initial Data
```bash
# Create initial data (admin user, plans, etc.)
python scripts/init_db.py
```

### 3.6 Start Backend Server
```bash
# Start FastAPI development server
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

The backend will be available at: http://localhost:8000

## ⚛️ Step 4: Frontend Setup (React)

### 4.1 Navigate to Frontend Directory
```bash
# Open a new terminal and navigate to frontend
cd frontend
```

### 4.2 Install Frontend Dependencies
```bash
# Install Node.js dependencies
npm install

# Or using yarn
yarn install
```

### 4.3 Start Frontend Development Server
```bash
# Start React development server
npm start

# Or using yarn
yarn start
```

The frontend will be available at: http://localhost:3000

## 🔍 Step 5: Verification and Testing

### 5.1 Health Check Endpoints
Test that all services are running:

```bash
# Backend health check
curl http://localhost:8000/health

# Database connection check
curl http://localhost:8000/api/v1/health/db

# Redis connection check
curl http://localhost:8000/api/v1/health/redis
```

### 5.2 Frontend Verification
1. Open http://localhost:3000 in your browser
2. Verify the landing page loads correctly
3. Test navigation between pages
4. Check browser console for errors

### 5.3 API Testing
```bash
# Test user registration
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "full_name": "Test User",
    "password": "testpassword123"
  }'

# Test user login
curl -X POST http://localhost:8000/api/v1/auth/login/json \
  -H "Content-Type: application/json" \
  -d '{
    "email_or_username": "<EMAIL>",
    "password": "testpassword123"
  }'
```

## 🧪 Step 6: Run Tests

### 6.1 Backend Tests
```bash
cd backend

# Run all tests
pytest

# Run tests with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/api/test_auth.py -v
```

### 6.2 Frontend Tests
```bash
cd frontend

# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage --watchAll=false

# Run specific test file
npm test -- --testPathPattern=HomePage.test.tsx
```

## 🔧 Step 7: Optional Modal AI Services Setup

### 7.1 Install Modal CLI
```bash
pip install modal
```

### 7.2 Configure Modal Authentication
```bash
# Set up Modal authentication (use your actual credentials)
modal token set \
  --token-id your_modal_token_id \
  --token-secret your_modal_token_secret \
  --profile koursia-dev

modal profile activate koursia-dev
```

### 7.3 Deploy Modal Services (Optional)
```bash
cd modal-services

# Deploy Whisper service
modal deploy whisper_service.py

# Deploy TTS service
modal deploy tts_service.py

# Deploy Ecomimic service
modal deploy ecomimic_service.py
```

### 7.4 Update Environment with Modal URLs
After deployment, update your `.env` file:
```bash
MODAL_WHISPER_URL=https://your-whisper-webhook-url
MODAL_TTS_URL=https://your-tts-webhook-url
MODAL_ECOMIMIC_URL=https://your-ecomimic-webhook-url
USE_MODAL_SERVICES=true
```

## 🚀 Step 8: Complete System Test

### 8.1 Run Integration Tests
```bash
# Run the comprehensive test script
python scripts/test-modal-integration.py
```

### 8.2 Manual Testing Checklist
- [ ] Frontend loads at http://localhost:3000
- [ ] Backend API responds at http://localhost:8000
- [ ] User registration works
- [ ] User login works
- [ ] Database queries execute successfully
- [ ] Redis caching works
- [ ] File upload functionality (if configured)
- [ ] AI services respond (if configured)

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### 1. Database Connection Issues
```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Check PostgreSQL logs
docker-compose logs postgres

# Restart PostgreSQL
docker-compose restart postgres
```

#### 2. Redis Connection Issues
```bash
# Check if Redis is running
docker-compose ps redis

# Test Redis connection
docker exec -it koursia_redis redis-cli ping

# Restart Redis
docker-compose restart redis
```

#### 3. Backend Import Errors
```bash
# Ensure virtual environment is activated
source venv/bin/activate  # macOS/Linux
venv\Scripts\activate     # Windows

# Reinstall dependencies
pip install -r requirements.txt
```

#### 4. Frontend Build Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### 5. Port Already in Use
```bash
# Find process using port 8000
lsof -i :8000  # macOS/Linux
netstat -ano | findstr :8000  # Windows

# Kill process
kill -9 <PID>  # macOS/Linux
taskkill /PID <PID> /F  # Windows
```

#### 6. Database Migration Issues
```bash
# Reset database (WARNING: This will delete all data)
docker-compose down -v
docker-compose up -d postgres redis

# Run migrations again
cd backend
alembic upgrade head
python scripts/init_db.py
```

### Performance Optimization

#### 1. Database Performance
```bash
# Monitor database performance
docker exec -it koursia_postgres psql -U koursia_user -d koursia -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"
```

#### 2. Redis Performance
```bash
# Monitor Redis performance
docker exec -it koursia_redis redis-cli info stats
```

## 📚 Additional Resources

### Documentation
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Redis Documentation](https://redis.io/documentation)
- [Docker Documentation](https://docs.docker.com/)

### Development Tools
- [Postman Collection](./docs/postman_collection.json) - API testing
- [VS Code Settings](./.vscode/settings.json) - Recommended IDE settings
- [Git Hooks](./scripts/git-hooks/) - Pre-commit hooks

### Support
- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Check the `/docs` folder for detailed guides
- **Community**: Join our Discord server for support

## 🎉 Success!

If all steps completed successfully, you should have:
- ✅ Frontend running on http://localhost:3000
- ✅ Backend API running on http://localhost:8000
- ✅ PostgreSQL database running and migrated
- ✅ Redis cache running
- ✅ All tests passing
- ✅ Complete development environment ready

You're now ready to start developing with the Koursia platform!
