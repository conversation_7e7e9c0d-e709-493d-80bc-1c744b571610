"""
Course endpoint tests
"""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session

from app.models.course import Course
from app.models.user import User


class TestCourseEndpoints:
    """Test course management endpoints"""
    
    def test_get_courses(self, client: TestClient, test_course: Course):
        """Test getting list of courses"""
        response = client.get("/api/v1/courses/")
        
        assert response.status_code == 200
        data = response.json()
        assert "courses" in data
        assert "total" in data
        assert "page" in data
        assert "per_page" in data
        assert len(data["courses"]) >= 1
        
        # Check course data
        course_data = data["courses"][0]
        assert course_data["title"] == test_course.title
        assert course_data["slug"] == test_course.slug
    
    def test_get_courses_with_filters(self, client: TestClient, test_course: Course):
        """Test getting courses with filters"""
        response = client.get("/api/v1/courses/?search=Test&difficulty=beginner")
        
        assert response.status_code == 200
        data = response.json()
        assert "courses" in data
    
    def test_get_courses_pagination(self, client: TestClient, test_course: Course):
        """Test course pagination"""
        response = client.get("/api/v1/courses/?skip=0&limit=5")
        
        assert response.status_code == 200
        data = response.json()
        assert data["per_page"] == 5
        assert data["page"] == 1
    
    def test_create_course_success(self, client: TestClient, auth_headers: dict, sample_course_data: dict):
        """Test successful course creation"""
        response = client.post("/api/v1/courses/", json=sample_course_data, headers=auth_headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == sample_course_data["title"]
        assert data["slug"] == sample_course_data["slug"]
        assert data["status"] == "draft"
        assert "id" in data
        assert "created_at" in data
    
    def test_create_course_unauthorized(self, client: TestClient, sample_course_data: dict):
        """Test course creation without authentication"""
        response = client.post("/api/v1/courses/", json=sample_course_data)
        
        assert response.status_code == 401
    
    def test_create_course_duplicate_slug(self, client: TestClient, auth_headers: dict, test_course: Course):
        """Test course creation with duplicate slug"""
        course_data = {
            "title": "Another Course",
            "description": "Another course",
            "slug": test_course.slug,  # Duplicate slug
            "difficulty": "beginner"
        }
        
        response = client.post("/api/v1/courses/", json=course_data, headers=auth_headers)
        
        assert response.status_code == 400
        assert "Course slug already exists" in response.json()["detail"]
    
    def test_get_course_by_id(self, client: TestClient, test_course: Course):
        """Test getting course by ID"""
        response = client.get(f"/api/v1/courses/{test_course.id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(test_course.id)
        assert data["title"] == test_course.title
    
    def test_get_course_not_found(self, client: TestClient):
        """Test getting nonexistent course"""
        fake_id = "00000000-0000-0000-0000-000000000000"
        response = client.get(f"/api/v1/courses/{fake_id}")
        
        assert response.status_code == 404
    
    def test_update_course_success(self, client: TestClient, auth_headers: dict, test_course: Course):
        """Test successful course update"""
        update_data = {
            "title": "Updated Course Title",
            "description": "Updated description"
        }
        
        response = client.put(f"/api/v1/courses/{test_course.id}", json=update_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == update_data["title"]
        assert data["description"] == update_data["description"]
    
    def test_update_course_unauthorized(self, client: TestClient, test_course: Course):
        """Test course update without authentication"""
        update_data = {"title": "Updated Title"}
        
        response = client.put(f"/api/v1/courses/{test_course.id}", json=update_data)
        
        assert response.status_code == 401
    
    def test_update_course_not_owner(self, client: TestClient, test_course: Course, db_session: Session):
        """Test course update by non-owner"""
        # Create another user
        other_user = User(
            email="<EMAIL>",
            username="otheruser",
            full_name="Other User",
            hashed_password="hashed_password"
        )
        db_session.add(other_user)
        db_session.commit()
        
        # Create auth headers for other user
        from app.core.security import create_access_token
        token = create_access_token(data={"sub": str(other_user.id)})
        other_headers = {"Authorization": f"Bearer {token}"}
        
        update_data = {"title": "Updated Title"}
        response = client.put(f"/api/v1/courses/{test_course.id}", json=update_data, headers=other_headers)
        
        assert response.status_code == 403
    
    def test_delete_course_success(self, client: TestClient, auth_headers: dict, test_course: Course, db_session: Session):
        """Test successful course deletion (soft delete)"""
        response = client.delete(f"/api/v1/courses/{test_course.id}", headers=auth_headers)
        
        assert response.status_code == 200
        
        # Verify course is soft deleted
        db_session.refresh(test_course)
        assert test_course.is_active == False
    
    def test_delete_course_unauthorized(self, client: TestClient, test_course: Course):
        """Test course deletion without authentication"""
        response = client.delete(f"/api/v1/courses/{test_course.id}")
        
        assert response.status_code == 401
    
    def test_create_module_success(self, client: TestClient, auth_headers: dict, test_course: Course):
        """Test successful module creation"""
        module_data = {
            "title": "Test Module",
            "description": "A test module",
            "order_index": 1,
            "objectives": ["Learn something", "Practice skills"]
        }
        
        response = client.post(f"/api/v1/courses/{test_course.id}/modules", json=module_data, headers=auth_headers)
        
        assert response.status_code == 201
        data = response.json()
        assert data["title"] == module_data["title"]
        assert data["course_id"] == str(test_course.id)
    
    def test_get_course_modules(self, client: TestClient, test_course: Course):
        """Test getting modules for a course"""
        response = client.get(f"/api/v1/courses/{test_course.id}/modules")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_categories(self, client: TestClient, test_category):
        """Test getting course categories"""
        response = client.get("/api/v1/courses/categories/")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        
        category_data = data[0]
        assert category_data["name"] == test_category.name
        assert category_data["slug"] == test_category.slug
