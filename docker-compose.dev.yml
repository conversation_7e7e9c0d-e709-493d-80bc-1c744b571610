version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: koursia_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-koursia}
      POSTGRES_USER: ${POSTGRES_USER:-koursia_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-koursia_dev_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-koursia_user} -d ${POSTGRES_DB:-koursia}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - koursia_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: koursia_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - koursia_network

  # PostgreSQL Admin (pgAdmin) - Optional
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: koursia_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - koursia_network
    profiles:
      - tools

  # Redis Commander - Optional
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: koursia_redis_commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - koursia_network
    profiles:
      - tools

  # Nginx (for production-like setup) - Optional
  nginx:
    image: nginx:alpine
    container_name: koursia_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./frontend/build:/usr/share/nginx/html:ro
    depends_on:
      - postgres
      - redis
    networks:
      - koursia_network
    profiles:
      - production

  # Celery Worker (for background tasks) - Optional
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: koursia_celery_worker
    restart: unless-stopped
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-koursia_user}:${POSTGRES_PASSWORD:-koursia_dev_password}@postgres:5432/${POSTGRES_DB:-koursia}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-}@redis:6379/1
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - koursia_network
    profiles:
      - workers

  # Celery Beat (for scheduled tasks) - Optional
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: koursia_celery_beat
    restart: unless-stopped
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-koursia_user}:${POSTGRES_PASSWORD:-koursia_dev_password}@postgres:5432/${POSTGRES_DB:-koursia}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-}@redis:6379/1
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - redis
    networks:
      - koursia_network
    profiles:
      - workers

  # Flower (Celery monitoring) - Optional
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: koursia_flower
    restart: unless-stopped
    command: celery -A app.core.celery flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://:${REDIS_PASSWORD:-}@redis:6379/1
    depends_on:
      - redis
    networks:
      - koursia_network
    profiles:
      - monitoring

  # Prometheus (metrics collection) - Optional
  prometheus:
    image: prom/prometheus:latest
    container_name: koursia_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - koursia_network
    profiles:
      - monitoring

  # Grafana (metrics visualization) - Optional
  grafana:
    image: grafana/grafana:latest
    container_name: koursia_grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - koursia_network
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  koursia_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
