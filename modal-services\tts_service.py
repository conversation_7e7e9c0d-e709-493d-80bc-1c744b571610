"""
Modal TTS service for text-to-speech generation
"""

import modal
import tempfile
import json
import asyncio
from pathlib import Path
from typing import Dict, Any, List, Optional
import aiofiles
import aiohttp
from datetime import datetime
import base64
import io

from modal_config import (
    app,
    tts_image,
    gpu_function,
    webhook_config,
    models_volume,
    temp_volume,
    MODEL_CONFIGS,
    ENVIRONMENT_VARS,
)

@app.function(
    image=tts_image,
    gpu=modal.gpu.A100(count=1, memory=80),
    volumes={
        "/models": models_volume,
        "/tmp": temp_volume,
    },
    secrets=list(ENVIRONMENT_VARS.values()),
    timeout=1800,
    keep_warm=1,
)
class TTSService:
    """Text-to-Speech service with multiple providers"""
    
    def __init__(self):
        self.tts_config = MODEL_CONFIGS["tts"]
        self.local_tts = None
    
    @modal.enter()
    def setup_tts(self):
        """Setup TTS models and providers"""
        print("Setting up TTS service...")
        
        try:
            # Initialize local TTS as fallback
            from TTS.api import TTS
            self.local_tts = TTS(model_name="tts_models/en/ljspeech/tacotron2-DDC")
            print("✅ Local TTS model loaded")
        except Exception as e:
            print(f"⚠️ Local TTS setup failed: {e}")
        
        print("TTS service ready")
    
    @modal.method()
    async def generate_speech(
        self,
        text: str,
        voice_settings: Dict[str, Any],
        output_format: str = "mp3",
    ) -> Dict[str, Any]:
        """
        Generate speech from text using specified provider
        
        Args:
            text: Text to convert to speech
            voice_settings: Voice configuration
            output_format: Output audio format (mp3, wav, ogg)
        
        Returns:
            Audio data and metadata
        """
        try:
            provider = voice_settings.get("provider", "chatterbox")
            
            if provider == "chatterbox":
                return await self._generate_chatterbox_speech(text, voice_settings, output_format)
            elif provider == "kokoro":
                return await self._generate_kokoro_speech(text, voice_settings, output_format)
            elif provider == "local":
                return await self._generate_local_speech(text, voice_settings, output_format)
            else:
                # Fallback to local TTS
                print(f"Unknown provider {provider}, falling back to local TTS")
                return await self._generate_local_speech(text, voice_settings, output_format)
                
        except Exception as e:
            print(f"❌ TTS generation failed: {e}")
            return {
                "error": str(e),
                "audio_data": None,
                "metadata": {
                    "provider": voice_settings.get("provider", "unknown"),
                    "timestamp": datetime.utcnow().isoformat(),
                    "error": True,
                },
            }
    
    async def _generate_chatterbox_speech(
        self,
        text: str,
        voice_settings: Dict[str, Any],
        output_format: str,
    ) -> Dict[str, Any]:
        """Generate speech using Chatterbox TTS API"""
        import os
        
        api_key = os.getenv("CHATTERBOX_API_KEY")
        if not api_key:
            raise ValueError("Chatterbox API key not configured")
        
        # Prepare request data
        request_data = {
            "text": text,
            "voice_id": voice_settings.get("voice_id", "en-US-AriaNeural"),
            "speed": voice_settings.get("speed", 1.0),
            "pitch": voice_settings.get("pitch", 0.0),
            "volume": voice_settings.get("volume", 1.0),
            "output_format": output_format,
        }
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://api.chatterbox.com/v1/tts",
                json=request_data,
                headers=headers,
            ) as response:
                
                if response.status == 200:
                    audio_data = await response.read()
                    
                    return {
                        "audio_data": base64.b64encode(audio_data).decode(),
                        "format": output_format,
                        "metadata": {
                            "provider": "chatterbox",
                            "voice_id": voice_settings.get("voice_id"),
                            "duration": len(audio_data) / 16000,  # Approximate
                            "text_length": len(text),
                            "timestamp": datetime.utcnow().isoformat(),
                        },
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"Chatterbox API error: {response.status} - {error_text}")
    
    async def _generate_kokoro_speech(
        self,
        text: str,
        voice_settings: Dict[str, Any],
        output_format: str,
    ) -> Dict[str, Any]:
        """Generate speech using Kokoro TTS API"""
        import os
        
        api_key = os.getenv("KOKORO_API_KEY")
        if not api_key:
            raise ValueError("Kokoro API key not configured")
        
        # Prepare request data
        request_data = {
            "text": text,
            "voice": voice_settings.get("voice_id", "default"),
            "speed": voice_settings.get("speed", 1.0),
            "format": output_format,
        }
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "https://api.kokoro.ai/v1/synthesize",
                json=request_data,
                headers=headers,
            ) as response:
                
                if response.status == 200:
                    audio_data = await response.read()
                    
                    return {
                        "audio_data": base64.b64encode(audio_data).decode(),
                        "format": output_format,
                        "metadata": {
                            "provider": "kokoro",
                            "voice_id": voice_settings.get("voice_id"),
                            "duration": len(audio_data) / 22050,  # Approximate
                            "text_length": len(text),
                            "timestamp": datetime.utcnow().isoformat(),
                        },
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"Kokoro API error: {response.status} - {error_text}")
    
    async def _generate_local_speech(
        self,
        text: str,
        voice_settings: Dict[str, Any],
        output_format: str,
    ) -> Dict[str, Any]:
        """Generate speech using local TTS model"""
        
        if not self.local_tts:
            raise Exception("Local TTS not available")
        
        try:
            # Generate audio with local TTS
            with tempfile.NamedTemporaryFile(suffix=f".{output_format}", delete=False) as tmp_file:
                self.local_tts.tts_to_file(
                    text=text,
                    file_path=tmp_file.name,
                )
                
                # Read generated audio
                with open(tmp_file.name, "rb") as f:
                    audio_data = f.read()
                
                # Clean up
                Path(tmp_file.name).unlink(missing_ok=True)
                
                return {
                    "audio_data": base64.b64encode(audio_data).decode(),
                    "format": output_format,
                    "metadata": {
                        "provider": "local",
                        "model": "tacotron2-DDC",
                        "duration": len(audio_data) / 22050,  # Approximate
                        "text_length": len(text),
                        "timestamp": datetime.utcnow().isoformat(),
                    },
                }
                
        except Exception as e:
            raise Exception(f"Local TTS generation failed: {e}")
    
    @modal.method()
    async def batch_generate_speech(
        self,
        texts: List[str],
        voice_settings: Dict[str, Any],
        output_format: str = "mp3",
    ) -> List[Dict[str, Any]]:
        """
        Generate speech for multiple texts
        
        Args:
            texts: List of texts to convert
            voice_settings: Voice configuration
            output_format: Output audio format
        
        Returns:
            List of audio generation results
        """
        results = []
        
        for i, text in enumerate(texts):
            print(f"Generating speech {i+1}/{len(texts)}")
            result = await self.generate_speech(text, voice_settings, output_format)
            results.append(result)
        
        return results
    
    @modal.method()
    async def get_available_voices(self, provider: str = "chatterbox") -> Dict[str, Any]:
        """
        Get available voices for a provider
        
        Args:
            provider: TTS provider name
        
        Returns:
            List of available voices
        """
        try:
            if provider == "chatterbox":
                return await self._get_chatterbox_voices()
            elif provider == "kokoro":
                return await self._get_kokoro_voices()
            elif provider == "local":
                return self._get_local_voices()
            else:
                return {"error": f"Unknown provider: {provider}"}
                
        except Exception as e:
            return {"error": str(e)}
    
    async def _get_chatterbox_voices(self) -> Dict[str, Any]:
        """Get Chatterbox available voices"""
        import os
        
        api_key = os.getenv("CHATTERBOX_API_KEY")
        if not api_key:
            return {"error": "Chatterbox API key not configured"}
        
        headers = {"Authorization": f"Bearer {api_key}"}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                "https://api.chatterbox.com/v1/voices",
                headers=headers,
            ) as response:
                
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    return {"error": f"API error: {response.status} - {error_text}"}
    
    async def _get_kokoro_voices(self) -> Dict[str, Any]:
        """Get Kokoro available voices"""
        import os
        
        api_key = os.getenv("KOKORO_API_KEY")
        if not api_key:
            return {"error": "Kokoro API key not configured"}
        
        headers = {"Authorization": f"Bearer {api_key}"}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                "https://api.kokoro.ai/v1/voices",
                headers=headers,
            ) as response:
                
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    return {"error": f"API error: {response.status} - {error_text}"}
    
    def _get_local_voices(self) -> Dict[str, Any]:
        """Get local TTS available voices"""
        return {
            "voices": [
                {
                    "id": "ljspeech",
                    "name": "LJSpeech Female",
                    "language": "en",
                    "gender": "female",
                }
            ]
        }

# Create webhook endpoint
@app.function(
    image=tts_image,
    gpu=modal.gpu.A100(count=1, memory=80),
    secrets=list(ENVIRONMENT_VARS.values()),
    timeout=1800,
    keep_warm=1,
)
@modal.web_endpoint(**webhook_config)
async def tts_webhook(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """Webhook endpoint for TTS generation"""
    
    service = TTSService()
    
    action = request_data.get("action", "generate")
    
    if action == "generate":
        return await service.generate_speech(
            text=request_data["text"],
            voice_settings=request_data.get("voice_settings", {}),
            output_format=request_data.get("output_format", "mp3"),
        )
    
    elif action == "batch":
        return {
            "results": await service.batch_generate_speech(
                texts=request_data["texts"],
                voice_settings=request_data.get("voice_settings", {}),
                output_format=request_data.get("output_format", "mp3"),
            )
        }
    
    elif action == "voices":
        return await service.get_available_voices(
            provider=request_data.get("provider", "chatterbox")
        )
    
    else:
        return {"error": f"Unknown action: {action}"}

# Health check endpoint
@app.function(image=tts_image)
@modal.web_endpoint(method="GET")
def tts_health() -> Dict[str, Any]:
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "tts",
        "timestamp": datetime.utcnow().isoformat(),
        "providers": MODEL_CONFIGS["tts"]["providers"],
    }
