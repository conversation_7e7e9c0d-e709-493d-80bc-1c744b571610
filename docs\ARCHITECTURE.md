# Koursia Platform Architecture

## Overview

Koursia is a comprehensive AI-powered course creation platform built with a microservices architecture. The platform enables users to create engaging educational content through two main workflows: Smart Course Creation and Avatar Course Creation.

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   AI Services   │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (Python)      │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 8001    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Media Processor │    │   PostgreSQL    │    │     Redis       │
│   (Python)      │    │   Database      │    │   Cache/Queue   │
│   Port: 8002    │    │   Port: 5432    │    │   Port: 6379    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Core Components

### 1. Frontend (React + TypeScript)
- **Technology**: React 18, TypeScript, Material-UI, Redux Toolkit
- **Responsibilities**:
  - User interface for course creation workflows
  - Real-time progress tracking
  - Media preview and management
  - User authentication and profile management

### 2. Backend API (FastAPI)
- **Technology**: FastAPI, SQLAlchemy, PostgreSQL, Celery
- **Responsibilities**:
  - RESTful API endpoints
  - User authentication and authorization
  - Course and media management
  - Background job orchestration
  - Integration with external services

### 3. AI Services (Python)
- **Technology**: Python, various AI libraries
- **Responsibilities**:
  - Content generation with Gemini API
  - Text-to-speech with Chatterbox/Kokoro TTS
  - Speech recognition with Whisper
  - Avatar generation with Ecomimic V2

### 4. Media Processing (Python)
- **Technology**: FFmpeg, Marp, Python
- **Responsibilities**:
  - Video assembly and editing
  - Slide generation from scripts
  - Stock media integration (Pexels, Pixabay)
  - Media format conversion

## Data Flow

### Smart Course Creation Flow
1. **User Input**: Course details, preferences, and requirements
2. **Content Generation**: Gemini API generates course structure and scripts
3. **Voice Synthesis**: Chatterbox/Kokoro TTS converts scripts to audio
4. **Media Integration**: Stock media APIs provide relevant images/videos
5. **Slide Generation**: Marp creates slides from generated content
6. **Video Assembly**: FFmpeg combines audio, slides, and media
7. **Transcription**: Whisper generates subtitles
8. **Output**: Complete course video with subtitles

### Avatar Course Creation Flow
1. **User Input**: Course details and avatar preferences
2. **Content Generation**: Gemini API generates course structure and scripts
3. **Avatar Generation**: Ecomimic V2 creates avatar video from scripts
4. **Media Synchronization**: Align avatar with background media
5. **Video Assembly**: FFmpeg combines avatar, media, and effects
6. **Transcription**: Whisper generates subtitles
7. **Output**: Avatar-narrated course video

## Database Schema

### Core Entities
- **Users**: Authentication, profiles, permissions
- **Courses**: Course metadata, status, settings
- **Modules**: Course sections with learning objectives
- **Lessons**: Individual lesson content and media
- **Media**: File storage references and metadata
- **Generation Jobs**: Background task tracking

### Relationships
- User → Courses (1:many)
- Course → Modules (1:many)
- Module → Lessons (1:many)
- Lesson → Media (many:many)
- User → Generation Jobs (1:many)

## API Design

### Authentication
- JWT-based authentication
- Role-based access control (RBAC)
- API key authentication for services

### Core Endpoints
```
/api/v1/auth/          # Authentication endpoints
/api/v1/users/         # User management
/api/v1/courses/       # Course CRUD operations
/api/v1/modules/       # Module management
/api/v1/lessons/       # Lesson management
/api/v1/media/         # Media upload and management
/api/v1/generation/    # Course generation jobs
/api/v1/ai/            # AI service integrations
```

## Security Considerations

### Authentication & Authorization
- JWT tokens with configurable expiration
- Password hashing with bcrypt
- Role-based permissions
- API rate limiting

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CORS configuration

### File Security
- File type validation
- Size limits
- Virus scanning (recommended)
- Secure file storage with AWS S3

## Scalability & Performance

### Horizontal Scaling
- Stateless API design
- Load balancer ready
- Database connection pooling
- Redis for session management

### Background Processing
- Celery for async tasks
- Redis as message broker
- Task queues for different priorities
- Progress tracking and notifications

### Caching Strategy
- Redis for API response caching
- CDN for static media files
- Database query optimization
- Frontend state management

## Monitoring & Logging

### Application Monitoring
- Health check endpoints
- Prometheus metrics
- Error tracking with Sentry
- Performance monitoring

### Logging
- Structured logging with JSON format
- Log levels: DEBUG, INFO, WARNING, ERROR
- Centralized log aggregation
- Log rotation and retention

## Deployment

### Development
- Docker Compose for local development
- Hot reload for frontend and backend
- Database migrations with Alembic
- Environment-specific configurations

### Production
- Kubernetes deployment (recommended)
- Docker containers
- Load balancing with NGINX
- SSL/TLS termination
- Database backups and monitoring

## Technology Stack Summary

| Component | Technology | Purpose |
|-----------|------------|---------|
| Frontend | React + TypeScript | User interface |
| Backend | FastAPI + Python | API server |
| Database | PostgreSQL | Data persistence |
| Cache/Queue | Redis | Caching and job queue |
| AI Content | Gemini API | Content generation |
| TTS | Chatterbox/Kokoro | Voice synthesis |
| STT | Whisper | Speech recognition |
| Avatar | Ecomimic V2 | Avatar generation |
| Video | FFmpeg | Video processing |
| Slides | Marp | Slide generation |
| Storage | AWS S3 | Media storage |
| Container | Docker | Containerization |

## Development Workflow

1. **Setup**: Clone repository, configure environment
2. **Development**: Use Docker Compose for local development
3. **Testing**: Unit tests, integration tests, E2E tests
4. **Code Quality**: ESLint, Prettier, Black, MyPy
5. **CI/CD**: GitHub Actions for automated testing and deployment
6. **Monitoring**: Application and infrastructure monitoring
