"""
Text-to-Speech service supporting multiple providers
"""

import asyncio
import aiohttp
import aiofiles
from typing import Dict, Any, List, Optional
from pathlib import Path
import uuid
import json

from app.core.config import settings
from app.core.database import SessionLocal
from app.models.course import Course, Module, Lesson
from app.schemas.generation import VoiceSettings


class TTSService:
    """Service for text-to-speech conversion"""
    
    def __init__(self):
        self.chatterbox_api_key = settings.CHATTERBOX_API_KEY
        self.kokoro_api_key = settings.KOKORO_API_KEY
        self.output_dir = Path("media/generated/audio")
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def generate_course_audio(
        self, 
        course_id: str, 
        voice_settings: VoiceSettings
    ) -> List[str]:
        """Generate audio for all lessons in a course"""
        
        db = SessionLocal()
        try:
            # Get course with modules and lessons
            course = db.query(Course).filter(Course.id == course_id).first()
            if not course:
                raise ValueError(f"Course {course_id} not found")
            
            audio_files = []
            
            # Process each module
            for module in course.modules:
                for lesson in module.lessons:
                    if lesson.content:
                        audio_file = await self.generate_lesson_audio(
                            lesson.id,
                            lesson.content,
                            voice_settings
                        )
                        
                        if audio_file:
                            audio_files.append(audio_file)
                            
                            # Update lesson with audio URL
                            lesson.audio_url = f"/media/generated/audio/{Path(audio_file).name}"
                            db.commit()
            
            return audio_files
            
        finally:
            db.close()
    
    async def generate_lesson_audio(
        self,
        lesson_id: str,
        text: str,
        voice_settings: VoiceSettings
    ) -> Optional[str]:
        """Generate audio for a single lesson"""
        
        try:
            if voice_settings.provider == "chatterbox":
                return await self._generate_chatterbox_audio(lesson_id, text, voice_settings)
            elif voice_settings.provider == "kokoro":
                return await self._generate_kokoro_audio(lesson_id, text, voice_settings)
            else:
                raise ValueError(f"Unsupported TTS provider: {voice_settings.provider}")
                
        except Exception as e:
            print(f"Error generating audio for lesson {lesson_id}: {e}")
            return None
    
    async def _generate_chatterbox_audio(
        self,
        lesson_id: str,
        text: str,
        voice_settings: VoiceSettings
    ) -> Optional[str]:
        """Generate audio using Chatterbox TTS"""
        
        if not self.chatterbox_api_key:
            raise ValueError("Chatterbox API key not configured")
        
        # Prepare request data
        request_data = {
            "text": text,
            "voice": voice_settings.voice_id,
            "speed": voice_settings.speed,
            "pitch": voice_settings.pitch,
            "volume": voice_settings.volume,
            "format": "mp3"
        }
        
        headers = {
            "Authorization": f"Bearer {self.chatterbox_api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                # Make TTS request
                async with session.post(
                    "https://api.chatterbox.com/v1/tts",  # Replace with actual Chatterbox API URL
                    json=request_data,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        # Generate unique filename
                        filename = f"lesson_{lesson_id}_{uuid.uuid4().hex[:8]}.mp3"
                        file_path = self.output_dir / filename
                        
                        # Save audio file
                        async with aiofiles.open(file_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        return str(file_path)
                    else:
                        error_text = await response.text()
                        raise Exception(f"Chatterbox API error: {response.status} - {error_text}")
                        
        except Exception as e:
            print(f"Chatterbox TTS error: {e}")
            # Fallback to local TTS or return None
            return await self._generate_fallback_audio(lesson_id, text)
    
    async def _generate_kokoro_audio(
        self,
        lesson_id: str,
        text: str,
        voice_settings: VoiceSettings
    ) -> Optional[str]:
        """Generate audio using Kokoro TTS"""
        
        if not self.kokoro_api_key:
            raise ValueError("Kokoro API key not configured")
        
        # Prepare request data for Kokoro
        request_data = {
            "text": text,
            "voice_id": voice_settings.voice_id,
            "speed": voice_settings.speed,
            "pitch": voice_settings.pitch,
            "output_format": "mp3"
        }
        
        headers = {
            "Authorization": f"Bearer {self.kokoro_api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    "https://api.kokoro.com/v1/synthesize",  # Replace with actual Kokoro API URL
                    json=request_data,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        filename = f"lesson_{lesson_id}_{uuid.uuid4().hex[:8]}.mp3"
                        file_path = self.output_dir / filename
                        
                        async with aiofiles.open(file_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        return str(file_path)
                    else:
                        error_text = await response.text()
                        raise Exception(f"Kokoro API error: {response.status} - {error_text}")
                        
        except Exception as e:
            print(f"Kokoro TTS error: {e}")
            return await self._generate_fallback_audio(lesson_id, text)
    
    async def _generate_fallback_audio(
        self,
        lesson_id: str,
        text: str
    ) -> Optional[str]:
        """Generate audio using local TTS as fallback"""
        
        try:
            # This would use a local TTS engine like pyttsx3 or espeak
            # For now, we'll create a placeholder file
            filename = f"lesson_{lesson_id}_{uuid.uuid4().hex[:8]}.mp3"
            file_path = self.output_dir / filename
            
            # Create a placeholder audio file (in real implementation, use local TTS)
            async with aiofiles.open(file_path, 'w') as f:
                await f.write(f"# Placeholder audio for lesson {lesson_id}\n# Text: {text[:100]}...")
            
            return str(file_path)
            
        except Exception as e:
            print(f"Fallback TTS error: {e}")
            return None
    
    async def get_available_voices(self, provider: str) -> List[Dict[str, Any]]:
        """Get available voices for a TTS provider"""
        
        if provider == "chatterbox":
            return await self._get_chatterbox_voices()
        elif provider == "kokoro":
            return await self._get_kokoro_voices()
        else:
            return []
    
    async def _get_chatterbox_voices(self) -> List[Dict[str, Any]]:
        """Get available Chatterbox voices"""
        
        if not self.chatterbox_api_key:
            return []
        
        headers = {
            "Authorization": f"Bearer {self.chatterbox_api_key}"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://api.chatterbox.com/v1/voices",
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        return data.get("voices", [])
                    else:
                        return []
                        
        except Exception as e:
            print(f"Error fetching Chatterbox voices: {e}")
            return []
    
    async def _get_kokoro_voices(self) -> List[Dict[str, Any]]:
        """Get available Kokoro voices"""
        
        if not self.kokoro_api_key:
            return []
        
        headers = {
            "Authorization": f"Bearer {self.kokoro_api_key}"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "https://api.kokoro.com/v1/voices",
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        return data.get("voices", [])
                    else:
                        return []
                        
        except Exception as e:
            print(f"Error fetching Kokoro voices: {e}")
            return []
