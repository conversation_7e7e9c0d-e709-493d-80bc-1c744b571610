"""
Tests for pricing API endpoints
"""

import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.models.user import User
from app.models.subscription import Plan, Subscription, PlanType, BillingCycle, SubscriptionStatus
from app.core.config import settings
from tests.utils.utils import create_random_user, create_test_plan


client = TestClient(app)


class TestPricingAPI:
    """Test pricing and subscription API endpoints"""

    def test_get_plans(self, db: Session):
        """Test getting all available plans"""
        # Create test plans
        free_plan = create_test_plan(db, plan_type=PlanType.FREE, monthly_price=0.0)
        pro_plan = create_test_plan(db, plan_type=PlanType.PRO, monthly_price=49.0)
        
        response = client.get("/api/v1/pricing/plans")
        
        assert response.status_code == 200
        plans = response.json()
        assert len(plans) >= 2
        
        # Check plan structure
        plan = plans[0]
        assert "id" in plan
        assert "name" in plan
        assert "plan_type" in plan
        assert "monthly_price" in plan
        assert "yearly_price" in plan

    def test_get_plan_by_id(self, db: Session):
        """Test getting specific plan details"""
        plan = create_test_plan(db, plan_type=PlanType.PRO)
        
        response = client.get(f"/api/v1/pricing/plans/{plan.id}")
        
        assert response.status_code == 200
        plan_data = response.json()
        assert plan_data["id"] == str(plan.id)
        assert plan_data["name"] == plan.name

    def test_get_nonexistent_plan(self, db: Session):
        """Test getting non-existent plan returns 404"""
        fake_id = "00000000-0000-0000-0000-000000000000"
        
        response = client.get(f"/api/v1/pricing/plans/{fake_id}")
        
        assert response.status_code == 404
        assert "Plan not found" in response.json()["detail"]

    def test_calculate_pricing_monthly(self, db: Session):
        """Test pricing calculation for monthly billing"""
        plan = create_test_plan(db, monthly_price=49.0, yearly_price=39.0)
        
        response = client.post("/api/v1/pricing/calculate", params={
            "plan_id": str(plan.id),
            "billing_cycle": "monthly"
        })
        
        assert response.status_code == 200
        calculation = response.json()
        assert calculation["base_price"] == 49.0
        assert calculation["billing_cycle"] == "monthly"
        assert calculation["total_amount"] == 49.0

    def test_calculate_pricing_yearly(self, db: Session):
        """Test pricing calculation for yearly billing"""
        plan = create_test_plan(db, monthly_price=49.0, yearly_price=39.0)
        
        response = client.post("/api/v1/pricing/calculate", params={
            "plan_id": str(plan.id),
            "billing_cycle": "yearly"
        })
        
        assert response.status_code == 200
        calculation = response.json()
        assert calculation["base_price"] == 39.0
        assert calculation["billing_cycle"] == "yearly"
        assert calculation["yearly_savings"] > 0

    def test_get_current_subscription_unauthenticated(self):
        """Test getting subscription without authentication"""
        response = client.get("/api/v1/pricing/subscription")
        
        assert response.status_code == 401

    def test_get_current_subscription_none(self, db: Session, normal_user_token_headers):
        """Test getting subscription when user has none"""
        response = client.get(
            "/api/v1/pricing/subscription",
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 200
        assert response.json() is None

    def test_create_subscription(self, db: Session, normal_user_token_headers):
        """Test creating a new subscription"""
        user = create_random_user(db)
        plan = create_test_plan(db, plan_type=PlanType.PRO)
        
        subscription_data = {
            "plan_id": str(plan.id),
            "billing_cycle": "monthly",
            "payment_method_id": "pm_test_123"
        }
        
        response = client.post(
            "/api/v1/pricing/subscription",
            json=subscription_data,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 200
        subscription = response.json()
        assert subscription["plan_id"] == str(plan.id)
        assert subscription["billing_cycle"] == "monthly"
        assert subscription["status"] in ["active", "trialing"]

    def test_create_subscription_duplicate(self, db: Session, normal_user_token_headers):
        """Test creating subscription when user already has one"""
        user = create_random_user(db)
        plan = create_test_plan(db, plan_type=PlanType.PRO)
        
        # Create existing subscription
        existing_subscription = Subscription(
            user_id=user.id,
            plan_id=plan.id,
            billing_cycle=BillingCycle.MONTHLY,
            amount=49.0,
            status=SubscriptionStatus.ACTIVE,
            start_date=datetime.utcnow()
        )
        db.add(existing_subscription)
        db.commit()
        
        subscription_data = {
            "plan_id": str(plan.id),
            "billing_cycle": "monthly"
        }
        
        response = client.post(
            "/api/v1/pricing/subscription",
            json=subscription_data,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 400
        assert "already has an active subscription" in response.json()["detail"]

    def test_cancel_subscription(self, db: Session, normal_user_token_headers):
        """Test canceling a subscription"""
        user = create_random_user(db)
        plan = create_test_plan(db, plan_type=PlanType.PRO)
        
        # Create subscription
        subscription = Subscription(
            user_id=user.id,
            plan_id=plan.id,
            billing_cycle=BillingCycle.MONTHLY,
            amount=49.0,
            status=SubscriptionStatus.ACTIVE,
            start_date=datetime.utcnow()
        )
        db.add(subscription)
        db.commit()
        
        response = client.post(
            "/api/v1/pricing/subscription/cancel",
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        assert "Subscription canceled successfully" in result["message"]
        assert "subscription_id" in result

    def test_cancel_subscription_immediate(self, db: Session, normal_user_token_headers):
        """Test immediate subscription cancellation"""
        user = create_random_user(db)
        plan = create_test_plan(db, plan_type=PlanType.PRO)
        
        # Create subscription
        subscription = Subscription(
            user_id=user.id,
            plan_id=plan.id,
            billing_cycle=BillingCycle.MONTHLY,
            amount=49.0,
            status=SubscriptionStatus.ACTIVE,
            start_date=datetime.utcnow()
        )
        db.add(subscription)
        db.commit()
        
        response = client.post(
            "/api/v1/pricing/subscription/cancel?immediate=true",
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 200
        result = response.json()
        assert "end_date" in result

    def test_start_trial(self, db: Session, normal_user_token_headers):
        """Test starting a trial subscription"""
        plan = create_test_plan(db, plan_type=PlanType.PRO, trial_days=3)
        
        response = client.post(
            f"/api/v1/pricing/trial/start?plan_id={plan.id}",
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 200
        subscription = response.json()
        assert subscription["status"] == "trialing"
        assert subscription["trial_start"] is not None
        assert subscription["trial_end"] is not None

    def test_start_trial_already_used(self, db: Session, normal_user_token_headers):
        """Test starting trial when user already used one"""
        user = create_random_user(db)
        plan = create_test_plan(db, plan_type=PlanType.PRO, trial_days=3)
        
        # Create previous trial
        old_trial = Subscription(
            user_id=user.id,
            plan_id=plan.id,
            billing_cycle=BillingCycle.MONTHLY,
            amount=0.0,
            status=SubscriptionStatus.CANCELED,
            start_date=datetime.utcnow() - timedelta(days=30),
            trial_start=datetime.utcnow() - timedelta(days=30),
            trial_end=datetime.utcnow() - timedelta(days=27)
        )
        db.add(old_trial)
        db.commit()
        
        response = client.post(
            f"/api/v1/pricing/trial/start?plan_id={plan.id}",
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 400
        assert "already used a trial" in response.json()["detail"]

    def test_get_usage(self, db: Session, normal_user_token_headers):
        """Test getting usage statistics"""
        response = client.get(
            "/api/v1/pricing/usage",
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 200
        usage = response.json()
        assert "smart_courses_used" in usage
        assert "avatar_courses_used" in usage
        assert "smart_courses_limit" in usage
        assert "avatar_courses_limit" in usage
        assert "current_plan" in usage

    def test_get_features(self, db: Session, normal_user_token_headers):
        """Test getting user features"""
        response = client.get(
            "/api/v1/pricing/features",
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 200
        features = response.json()
        assert "can_create_smart_courses" in features
        assert "can_create_avatar_courses" in features
        assert "has_premium_templates" in features
        assert "max_course_duration" in features

    def test_get_invoices(self, db: Session, normal_user_token_headers):
        """Test getting user invoices"""
        response = client.get(
            "/api/v1/pricing/invoices",
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 200
        invoices = response.json()
        assert isinstance(invoices, list)

    def test_reactivate_subscription(self, db: Session, normal_user_token_headers):
        """Test reactivating a canceled subscription"""
        user = create_random_user(db)
        plan = create_test_plan(db, plan_type=PlanType.PRO)
        
        # Create canceled subscription
        subscription = Subscription(
            user_id=user.id,
            plan_id=plan.id,
            billing_cycle=BillingCycle.MONTHLY,
            amount=49.0,
            status=SubscriptionStatus.CANCELED,
            start_date=datetime.utcnow() - timedelta(days=10),
            canceled_at=datetime.utcnow() - timedelta(days=5)
        )
        db.add(subscription)
        db.commit()
        
        response = client.post(
            "/api/v1/pricing/subscription/reactivate",
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 200
        reactivated = response.json()
        assert reactivated["status"] == "active"
        assert reactivated["canceled_at"] is None

    def test_pricing_calculation_with_promo_code(self, db: Session):
        """Test pricing calculation with promo code"""
        plan = create_test_plan(db, monthly_price=49.0)
        
        response = client.post("/api/v1/pricing/calculate", params={
            "plan_id": str(plan.id),
            "billing_cycle": "monthly",
            "promo_code": "SAVE20"
        })
        
        assert response.status_code == 200
        calculation = response.json()
        assert "discount_amount" in calculation
        assert "promo_code" in calculation

    def test_update_subscription(self, db: Session, normal_user_token_headers):
        """Test updating an existing subscription"""
        user = create_random_user(db)
        plan = create_test_plan(db, plan_type=PlanType.PRO)
        new_plan = create_test_plan(db, plan_type=PlanType.CREATOR)
        
        # Create subscription
        subscription = Subscription(
            user_id=user.id,
            plan_id=plan.id,
            billing_cycle=BillingCycle.MONTHLY,
            amount=49.0,
            status=SubscriptionStatus.ACTIVE,
            start_date=datetime.utcnow()
        )
        db.add(subscription)
        db.commit()
        
        update_data = {
            "plan_id": str(new_plan.id),
            "billing_cycle": "yearly"
        }
        
        response = client.put(
            "/api/v1/pricing/subscription",
            json=update_data,
            headers=normal_user_token_headers
        )
        
        assert response.status_code == 200
        updated = response.json()
        assert updated["plan_id"] == str(new_plan.id)
        assert updated["billing_cycle"] == "yearly"
