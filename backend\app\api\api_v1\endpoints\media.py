"""
Media management endpoints
"""

from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from sqlalchemy.orm import Session
import os
import uuid
from pathlib import Path

from app.core.database import get_db
from app.core.security import get_current_active_user
from app.core.config import settings
from app.models.user import User
from app.models.media import MediaAsset, MediaType
from app.schemas.media import MediaAssetResponse, MediaAssetCreate, MediaAssetUpdate

router = APIRouter()


@router.post("/upload", response_model=MediaAssetResponse, status_code=status.HTTP_201_CREATED)
async def upload_media(
    file: UploadFile = File(...),
    title: Optional[str] = None,
    description: Optional[str] = None,
    alt_text: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Upload a media file
    """
    # Validate file type
    if file.content_type:
        file_extension = file.filename.split('.')[-1].lower()
        if file_extension not in settings.ALLOWED_FILE_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type not allowed. Allowed types: {', '.join(settings.ALLOWED_FILE_TYPES)}"
            )
    
    # Validate file size
    file_size = 0
    content = await file.read()
    file_size = len(content)
    
    max_size = settings.MAX_FILE_SIZE_MB * 1024 * 1024
    if file_size > max_size:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE_MB}MB"
        )
    
    # Generate unique filename
    file_extension = file.filename.split('.')[-1].lower()
    unique_filename = f"{uuid.uuid4()}.{file_extension}"
    
    # Create upload directory
    upload_dir = Path("media/uploads")
    upload_dir.mkdir(parents=True, exist_ok=True)
    
    # Save file
    file_path = upload_dir / unique_filename
    with open(file_path, "wb") as buffer:
        buffer.write(content)
    
    # Determine media type
    media_type = MediaType.DOCUMENT
    if file_extension in ['jpg', 'jpeg', 'png', 'gif', 'webp']:
        media_type = MediaType.IMAGE
    elif file_extension in ['mp4', 'avi', 'mov', 'wmv', 'flv']:
        media_type = MediaType.VIDEO
    elif file_extension in ['mp3', 'wav', 'flac', 'aac']:
        media_type = MediaType.AUDIO
    
    # Create media asset record
    media_asset = MediaAsset(
        filename=unique_filename,
        original_filename=file.filename,
        title=title or file.filename,
        description=description,
        alt_text=alt_text,
        file_path=str(file_path),
        file_url=f"/media/uploads/{unique_filename}",
        file_size=file_size,
        mime_type=file.content_type or "application/octet-stream",
        media_type=media_type,
        uploaded_by=current_user.id
    )
    
    db.add(media_asset)
    db.commit()
    db.refresh(media_asset)
    
    return media_asset


@router.get("/", response_model=List[MediaAssetResponse])
async def get_media_assets(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    media_type: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get user's media assets
    """
    query = db.query(MediaAsset).filter(
        MediaAsset.uploaded_by == current_user.id,
        MediaAsset.is_active == True
    )
    
    if media_type:
        query = query.filter(MediaAsset.media_type == media_type)
    
    if search:
        query = query.filter(
            or_(
                MediaAsset.title.ilike(f"%{search}%"),
                MediaAsset.description.ilike(f"%{search}%"),
                MediaAsset.original_filename.ilike(f"%{search}%")
            )
        )
    
    media_assets = query.offset(skip).limit(limit).all()
    return media_assets


@router.get("/{media_id}", response_model=MediaAssetResponse)
async def get_media_asset(
    media_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get media asset by ID
    """
    media_asset = db.query(MediaAsset).filter(
        MediaAsset.id == media_id,
        MediaAsset.is_active == True
    ).first()
    
    if not media_asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Media asset not found"
        )
    
    # Check ownership or public access
    if media_asset.uploaded_by != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return media_asset


@router.put("/{media_id}", response_model=MediaAssetResponse)
async def update_media_asset(
    media_id: str,
    media_data: MediaAssetUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Update media asset metadata
    """
    media_asset = db.query(MediaAsset).filter(
        MediaAsset.id == media_id,
        MediaAsset.uploaded_by == current_user.id,
        MediaAsset.is_active == True
    ).first()
    
    if not media_asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Media asset not found"
        )
    
    # Update media asset
    update_data = media_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(media_asset, field, value)
    
    db.commit()
    db.refresh(media_asset)
    
    return media_asset


@router.delete("/{media_id}")
async def delete_media_asset(
    media_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Delete media asset
    """
    media_asset = db.query(MediaAsset).filter(
        MediaAsset.id == media_id,
        MediaAsset.uploaded_by == current_user.id,
        MediaAsset.is_active == True
    ).first()
    
    if not media_asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Media asset not found"
        )
    
    # Soft delete
    media_asset.is_active = False
    db.commit()
    
    # TODO: Delete physical file in background task
    
    return {"message": "Media asset deleted successfully"}
