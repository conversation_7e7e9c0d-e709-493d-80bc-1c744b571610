import React, { useState } from 'react'
import {
  Box,
  Container,
  <PERSON><PERSON><PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Avatar,
  Rating,
  Paper,
  IconButton,
  Fade,
  Slide,
  useTheme,
  useMediaQuery,
  Stack,
  Divider,
} from '@mui/material'
import {
  PlayArrow,
  School,
  AutoAwesome,
  Speed,
  Group,
  SmartToy,
  VideoLibrary,
  CloudUpload,
  TrendingUp,
  Security,
  Support,
  CheckCircle,
  ArrowForward,
  Star,
  Mic,
  Movie,
  Psychology,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'

import { useAppSelector } from '../hooks/redux'
import { selectIsAuthenticated } from '../store/slices/authSlice'
import AuthModal from '../components/Auth/AuthModal'

const HomePage: React.FC = () => {
  const navigate = useNavigate()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const isAuthenticated = useAppSelector(selectIsAuthenticated)
  const [authModalOpen, setAuthModalOpen] = useState(false)
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login')

  const handleAuthOpen = (mode: 'login' | 'register') => {
    setAuthMode(mode)
    setAuthModalOpen(true)
  }

  const features = [
    {
      icon: <Psychology sx={{ fontSize: 40 }} />,
      title: 'AI Content Generation',
      description: 'Powered by Google Gemini API for intelligent course creation',
      color: '#4285F4',
    },
    {
      icon: <SmartToy sx={{ fontSize: 40 }} />,
      title: 'Avatar Instructors',
      description: 'Lifelike AI avatars using Ecomimic V2 technology',
      color: '#34A853',
    },
    {
      icon: <Mic sx={{ fontSize: 40 }} />,
      title: 'Professional Voice',
      description: 'High-quality TTS with Chatterbox and Kokoro',
      color: '#FBBC04',
    },
    {
      icon: <Movie sx={{ fontSize: 40 }} />,
      title: 'Automated Video',
      description: 'Complete video production with FFmpeg processing',
      color: '#EA4335',
    },
    {
      icon: <CloudUpload sx={{ fontSize: 40 }} />,
      title: 'Smart Media',
      description: 'Stock photos from Pexels & Pixabay integration',
      color: '#9C27B0',
    },
    {
      icon: <TrendingUp sx={{ fontSize: 40 }} />,
      title: 'Analytics & Insights',
      description: 'Track performance and optimize your courses',
      color: '#FF9800',
    },
  ]

  const sampleCourses = [
    {
      id: '1',
      title: 'Introduction to Machine Learning',
      description: 'Learn the fundamentals of machine learning with practical examples',
      thumbnail: '/api/placeholder/300/200',
      duration: '2h 30m',
      rating: 4.8,
      students: 1234,
      instructor: 'AI Instructor',
      tags: ['AI', 'Machine Learning', 'Python'],
    },
    {
      id: '2',
      title: 'Web Development Fundamentals',
      description: 'Master the basics of modern web development',
      thumbnail: '/api/placeholder/300/200',
      duration: '3h 15m',
      rating: 4.9,
      students: 2156,
      instructor: 'AI Instructor',
      tags: ['Web Development', 'HTML', 'CSS', 'JavaScript'],
    },
    {
      id: '3',
      title: 'Digital Marketing Strategy',
      description: 'Comprehensive guide to digital marketing in the modern age',
      thumbnail: '/api/placeholder/300/200',
      duration: '1h 45m',
      rating: 4.7,
      students: 987,
      instructor: 'AI Instructor',
      tags: ['Marketing', 'Digital', 'Strategy'],
    },
  ]

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          py: { xs: 6, md: 10 },
          mb: 8,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background Animation */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            opacity: 0.1,
            background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <Typography
                  variant={isMobile ? "h3" : "h2"}
                  component="h1"
                  gutterBottom
                  sx={{
                    fontWeight: 'bold',
                    lineHeight: 1.2,
                    mb: 3,
                  }}
                >
                  Create Amazing Courses with{' '}
                  <Box component="span" sx={{ color: '#FFD700' }}>
                    AI Power
                  </Box>
                </Typography>

                <Typography
                  variant="h6"
                  paragraph
                  sx={{
                    opacity: 0.9,
                    lineHeight: 1.6,
                    mb: 4,
                  }}
                >
                  Transform your knowledge into professional courses using our AI-powered platform.
                  Generate content, create avatars, and produce high-quality educational videos in minutes.
                </Typography>

                {/* Key Benefits */}
                <Stack spacing={2} sx={{ mb: 4 }}>
                  {[
                    'AI-generated content with Gemini API',
                    'Professional avatar instructors',
                    'Automated video production',
                    'Multi-language TTS support'
                  ].map((benefit, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center' }}>
                      <CheckCircle sx={{ mr: 2, color: '#4CAF50' }} />
                      <Typography variant="body1">{benefit}</Typography>
                    </Box>
                  ))}
                </Stack>

                <Stack direction={isMobile ? 'column' : 'row'} spacing={2}>
                  {isAuthenticated ? (
                    <Button
                      variant="contained"
                      size="large"
                      onClick={() => navigate('/create-course')}
                      endIcon={<ArrowForward />}
                      sx={{
                        bgcolor: 'white',
                        color: 'primary.main',
                        py: 1.5,
                        px: 4,
                        fontSize: '1.1rem',
                        fontWeight: 'bold',
                        '&:hover': {
                          bgcolor: 'grey.100',
                          transform: 'translateY(-2px)',
                        },
                        transition: 'all 0.3s ease',
                      }}
                    >
                      Create Your First Course
                    </Button>
                  ) : (
                    <>
                      <Button
                        variant="contained"
                        size="large"
                        onClick={() => handleAuthOpen('register')}
                        endIcon={<ArrowForward />}
                        sx={{
                          bgcolor: 'white',
                          color: 'primary.main',
                          py: 1.5,
                          px: 4,
                          fontSize: '1.1rem',
                          fontWeight: 'bold',
                          '&:hover': {
                            bgcolor: 'grey.100',
                            transform: 'translateY(-2px)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        Start Free Trial
                      </Button>
                      <Button
                        variant="outlined"
                        size="large"
                        onClick={() => navigate('/pricing')}
                        sx={{
                          borderColor: 'white',
                          color: 'white',
                          py: 1.5,
                          px: 4,
                          fontSize: '1.1rem',
                          fontWeight: 'bold',
                          '&:hover': {
                            borderColor: 'white',
                            bgcolor: 'rgba(255,255,255,0.1)',
                            transform: 'translateY(-2px)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        View Pricing
                      </Button>
                    </>
                  )}
                </Stack>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <Box
                  sx={{
                    position: 'relative',
                    textAlign: 'center',
                  }}
                >
                  {/* Hero Video/Demo Placeholder */}
                  <Paper
                    elevation={20}
                    sx={{
                      borderRadius: 4,
                      overflow: 'hidden',
                      background: 'linear-gradient(45deg, #667eea 30%, #764ba2 90%)',
                      p: 4,
                      minHeight: 350,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative',
                    }}
                  >
                    <PlayArrow sx={{ fontSize: 80, mb: 2, opacity: 0.8 }} />
                    <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold' }}>
                      Watch Demo
                    </Typography>
                    <Typography variant="body1" sx={{ opacity: 0.8 }}>
                      See how AI creates professional courses in minutes
                    </Typography>

                    {/* Floating Elements */}
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 20,
                        right: 20,
                        bgcolor: 'rgba(255,255,255,0.2)',
                        borderRadius: 2,
                        p: 1,
                        backdropFilter: 'blur(10px)',
                      }}
                    >
                      <SmartToy sx={{ fontSize: 24 }} />
                    </Box>

                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 20,
                        left: 20,
                        bgcolor: 'rgba(255,255,255,0.2)',
                        borderRadius: 2,
                        p: 1,
                        backdropFilter: 'blur(10px)',
                      }}
                    >
                      <Psychology sx={{ fontSize: 24 }} />
                    </Box>
                  </Paper>
                </Box>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </Box>

      <Container maxWidth="lg">
        {/* Features Section */}
        <Box sx={{ mb: 10 }}>
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h3"
              component="h2"
              textAlign="center"
              gutterBottom
              sx={{ fontWeight: 'bold', mb: 2 }}
            >
              Powered by Advanced AI
            </Typography>
            <Typography
              variant="h6"
              textAlign="center"
              color="text.secondary"
              paragraph
              sx={{ mb: 6, maxWidth: 600, mx: 'auto' }}
            >
              Experience the future of course creation with cutting-edge AI technologies
            </Typography>
          </motion.div>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card
                    sx={{
                      height: '100%',
                      textAlign: 'center',
                      p: 3,
                      border: '1px solid',
                      borderColor: 'divider',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: theme.shadows[20],
                        borderColor: feature.color,
                      },
                    }}
                  >
                    <CardContent>
                      <Box
                        sx={{
                          width: 80,
                          height: 80,
                          borderRadius: '50%',
                          bgcolor: `${feature.color}15`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mx: 'auto',
                          mb: 3,
                          color: feature.color,
                        }}
                      >
                        {feature.icon}
                      </Box>
                      <Typography
                        variant="h6"
                        component="h3"
                        gutterBottom
                        sx={{ fontWeight: 'bold' }}
                      >
                        {feature.title}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ lineHeight: 1.6 }}
                      >
                        {feature.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Stats Section */}
        <Box sx={{ mb: 10 }}>
          <Paper
            sx={{
              background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
              p: 6,
              borderRadius: 4,
              textAlign: 'center',
            }}
          >
            <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', mb: 4 }}>
              Trusted by Educators Worldwide
            </Typography>

            <Grid container spacing={4}>
              {[
                { number: '10,000+', label: 'Courses Created' },
                { number: '50,000+', label: 'Students Reached' },
                { number: '95%', label: 'Satisfaction Rate' },
                { number: '24/7', label: 'AI Availability' },
              ].map((stat, index) => (
                <Grid item xs={6} md={3} key={index}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.5 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Typography
                      variant="h3"
                      sx={{
                        fontWeight: 'bold',
                        color: 'primary.main',
                        mb: 1,
                      }}
                    >
                      {stat.number}
                    </Typography>
                    <Typography variant="h6" color="text.secondary">
                      {stat.label}
                    </Typography>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Box>

        {/* Sample Courses Section */}
        <Box sx={{ mb: 8 }}>
          <Typography variant="h3" component="h2" textAlign="center" gutterBottom>
            Featured Courses
          </Typography>
          <Typography variant="h6" textAlign="center" color="text.secondary" paragraph>
            Explore courses created with our AI platform
          </Typography>
          
          <Grid container spacing={4} sx={{ mt: 4 }}>
            {sampleCourses.map((course) => (
              <Grid item xs={12} sm={6} md={4} key={course.id}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                    },
                  }}
                >
                  <Box
                    sx={{
                      height: 200,
                      bgcolor: 'grey.200',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative',
                    }}
                  >
                    <PlayArrow sx={{ fontSize: 48, color: 'white' }} />
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 8,
                        right: 8,
                        bgcolor: 'rgba(0,0,0,0.7)',
                        color: 'white',
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                      }}
                    >
                      {course.duration}
                    </Box>
                  </Box>
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" component="h3" gutterBottom>
                      {course.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      {course.description}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Rating value={course.rating} precision={0.1} size="small" readOnly />
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        {course.rating} ({course.students} students)
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {course.tags.map((tag) => (
                        <Chip key={tag} label={tag} size="small" />
                      ))}
                    </Box>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      onClick={() => navigate(`/courses/${course.id}`)}
                    >
                      View Course
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* CTA Section */}
        {!isAuthenticated && (
          <Box
            sx={{
              textAlign: 'center',
              py: 6,
              bgcolor: 'grey.50',
              borderRadius: 2,
              mb: 4,
            }}
          >
            <Typography variant="h4" component="h2" gutterBottom>
              Ready to Create Your First Course?
            </Typography>
            <Typography variant="h6" color="text.secondary" paragraph>
              Join thousands of educators using AI to create amazing learning experiences
            </Typography>
            <Button
              variant="contained"
              size="large"
              onClick={() => navigate('/register')}
              sx={{ mt: 2 }}
            >
              Start Creating Today
            </Button>
          </Box>
        )}
      </Container>

      {/* Auth Modal */}
      <AuthModal
        open={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        initialMode={authMode}
      />
    </Box>
  )
}

export default HomePage
