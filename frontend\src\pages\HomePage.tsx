import React from 'react'
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Avatar,
  Rating,
} from '@mui/material'
import {
  PlayArrow,
  School,
  AutoAwesome,
  Speed,
  Group,
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'

import { useAppSelector } from '../hooks/redux'
import { selectIsAuthenticated } from '../store/slices/authSlice'

const HomePage: React.FC = () => {
  const navigate = useNavigate()
  const isAuthenticated = useAppSelector(selectIsAuthenticated)

  const features = [
    {
      icon: <AutoAwesome />,
      title: 'AI-Powered Content Generation',
      description: 'Generate comprehensive course content using advanced AI technology',
    },
    {
      icon: <Speed />,
      title: 'Rapid Course Creation',
      description: 'Create professional courses in minutes, not hours',
    },
    {
      icon: <Group />,
      title: 'Avatar Instructors',
      description: 'Bring your courses to life with AI-generated avatar instructors',
    },
    {
      icon: <School />,
      title: 'Professional Quality',
      description: 'Produce high-quality educational content with automated workflows',
    },
  ]

  const sampleCourses = [
    {
      id: '1',
      title: 'Introduction to Machine Learning',
      description: 'Learn the fundamentals of machine learning with practical examples',
      thumbnail: '/api/placeholder/300/200',
      duration: '2h 30m',
      rating: 4.8,
      students: 1234,
      instructor: 'AI Instructor',
      tags: ['AI', 'Machine Learning', 'Python'],
    },
    {
      id: '2',
      title: 'Web Development Fundamentals',
      description: 'Master the basics of modern web development',
      thumbnail: '/api/placeholder/300/200',
      duration: '3h 15m',
      rating: 4.9,
      students: 2156,
      instructor: 'AI Instructor',
      tags: ['Web Development', 'HTML', 'CSS', 'JavaScript'],
    },
    {
      id: '3',
      title: 'Digital Marketing Strategy',
      description: 'Comprehensive guide to digital marketing in the modern age',
      thumbnail: '/api/placeholder/300/200',
      duration: '1h 45m',
      rating: 4.7,
      students: 987,
      instructor: 'AI Instructor',
      tags: ['Marketing', 'Digital', 'Strategy'],
    },
  ]

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          py: 8,
          mb: 6,
          borderRadius: 2,
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography variant="h2" component="h1" gutterBottom>
                Create Amazing Courses with AI
              </Typography>
              <Typography variant="h6" paragraph>
                Transform your knowledge into professional courses using our AI-powered platform. 
                Generate content, create avatars, and produce high-quality educational videos in minutes.
              </Typography>
              <Box sx={{ mt: 4 }}>
                {isAuthenticated ? (
                  <Button
                    variant="contained"
                    size="large"
                    onClick={() => navigate('/create-course')}
                    sx={{
                      mr: 2,
                      bgcolor: 'white',
                      color: 'primary.main',
                      '&:hover': {
                        bgcolor: 'grey.100',
                      },
                    }}
                  >
                    Create Your First Course
                  </Button>
                ) : (
                  <>
                    <Button
                      variant="contained"
                      size="large"
                      onClick={() => navigate('/register')}
                      sx={{
                        mr: 2,
                        bgcolor: 'white',
                        color: 'primary.main',
                        '&:hover': {
                          bgcolor: 'grey.100',
                        },
                      }}
                    >
                      Get Started Free
                    </Button>
                    <Button
                      variant="outlined"
                      size="large"
                      onClick={() => navigate('/courses')}
                      sx={{
                        borderColor: 'white',
                        color: 'white',
                        '&:hover': {
                          borderColor: 'white',
                          bgcolor: 'rgba(255,255,255,0.1)',
                        },
                      }}
                    >
                      Explore Courses
                    </Button>
                  </>
                )}
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  textAlign: 'center',
                  '& img': {
                    maxWidth: '100%',
                    height: 'auto',
                    borderRadius: 2,
                  },
                }}
              >
                {/* Placeholder for hero image */}
                <Box
                  sx={{
                    width: '100%',
                    height: 300,
                    bgcolor: 'rgba(255,255,255,0.1)',
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="h6" sx={{ opacity: 0.8 }}>
                    Course Creation Demo
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      <Container maxWidth="lg">
        {/* Features Section */}
        <Box sx={{ mb: 8 }}>
          <Typography variant="h3" component="h2" textAlign="center" gutterBottom>
            Why Choose Koursia?
          </Typography>
          <Typography variant="h6" textAlign="center" color="text.secondary" paragraph>
            Powerful AI tools to create professional courses effortlessly
          </Typography>
          
          <Grid container spacing={4} sx={{ mt: 4 }}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card
                  sx={{
                    height: '100%',
                    textAlign: 'center',
                    p: 2,
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                    },
                  }}
                >
                  <CardContent>
                    <Avatar
                      sx={{
                        bgcolor: 'primary.main',
                        width: 56,
                        height: 56,
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      {feature.icon}
                    </Avatar>
                    <Typography variant="h6" component="h3" gutterBottom>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Sample Courses Section */}
        <Box sx={{ mb: 8 }}>
          <Typography variant="h3" component="h2" textAlign="center" gutterBottom>
            Featured Courses
          </Typography>
          <Typography variant="h6" textAlign="center" color="text.secondary" paragraph>
            Explore courses created with our AI platform
          </Typography>
          
          <Grid container spacing={4} sx={{ mt: 4 }}>
            {sampleCourses.map((course) => (
              <Grid item xs={12} sm={6} md={4} key={course.id}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                    },
                  }}
                >
                  <Box
                    sx={{
                      height: 200,
                      bgcolor: 'grey.200',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative',
                    }}
                  >
                    <PlayArrow sx={{ fontSize: 48, color: 'white' }} />
                    <Box
                      sx={{
                        position: 'absolute',
                        bottom: 8,
                        right: 8,
                        bgcolor: 'rgba(0,0,0,0.7)',
                        color: 'white',
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                      }}
                    >
                      {course.duration}
                    </Box>
                  </Box>
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" component="h3" gutterBottom>
                      {course.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      {course.description}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Rating value={course.rating} precision={0.1} size="small" readOnly />
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        {course.rating} ({course.students} students)
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {course.tags.map((tag) => (
                        <Chip key={tag} label={tag} size="small" />
                      ))}
                    </Box>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      onClick={() => navigate(`/courses/${course.id}`)}
                    >
                      View Course
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* CTA Section */}
        {!isAuthenticated && (
          <Box
            sx={{
              textAlign: 'center',
              py: 6,
              bgcolor: 'grey.50',
              borderRadius: 2,
              mb: 4,
            }}
          >
            <Typography variant="h4" component="h2" gutterBottom>
              Ready to Create Your First Course?
            </Typography>
            <Typography variant="h6" color="text.secondary" paragraph>
              Join thousands of educators using AI to create amazing learning experiences
            </Typography>
            <Button
              variant="contained"
              size="large"
              onClick={() => navigate('/register')}
              sx={{ mt: 2 }}
            >
              Start Creating Today
            </Button>
          </Box>
        )}
      </Container>
    </Box>
  )
}

export default HomePage
