import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { Provider } from 'react-redux'
import { ThemeProvider } from '@mui/material/styles'
import { configureStore } from '@reduxjs/toolkit'

import HomePage from '../HomePage'
import authSlice from '../../store/slices/authSlice'
import { theme } from '../../theme'

// Mock useNavigate
const mockNavigate = jest.fn()
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}))

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authSlice,
    },
    preloadedState: {
      auth: {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      },
      ...initialState,
    },
  })
}

const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
  const store = createMockStore(initialState)
  
  return render(
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  )
}

describe('HomePage Component', () => {
  beforeEach(() => {
    mockNavigate.mockClear()
  })

  test('renders hero section with main heading', () => {
    renderWithProviders(<HomePage />)
    
    expect(screen.getByText('Create Amazing Courses with AI')).toBeInTheDocument()
    expect(screen.getByText(/Transform your knowledge into professional courses/)).toBeInTheDocument()
  })

  test('shows registration CTA when user is not authenticated', () => {
    renderWithProviders(<HomePage />)
    
    expect(screen.getByText('Get Started Free')).toBeInTheDocument()
    expect(screen.getByText('Explore Courses')).toBeInTheDocument()
  })

  test('shows create course CTA when user is authenticated', () => {
    const authenticatedState = {
      auth: {
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          full_name: 'Test User',
          role: 'user',
          is_active: true,
          is_verified: true,
          created_at: '2023-01-01T00:00:00Z',
        },
        token: 'test-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    }

    renderWithProviders(<HomePage />, authenticatedState)
    
    expect(screen.getByText('Create Your First Course')).toBeInTheDocument()
  })

  test('renders features section', () => {
    renderWithProviders(<HomePage />)
    
    expect(screen.getByText('Why Choose Koursia?')).toBeInTheDocument()
    expect(screen.getByText('AI-Powered Content Generation')).toBeInTheDocument()
    expect(screen.getByText('Rapid Course Creation')).toBeInTheDocument()
    expect(screen.getByText('Avatar Instructors')).toBeInTheDocument()
    expect(screen.getByText('Professional Quality')).toBeInTheDocument()
  })

  test('renders featured courses section', () => {
    renderWithProviders(<HomePage />)
    
    expect(screen.getByText('Featured Courses')).toBeInTheDocument()
    expect(screen.getByText('Introduction to Machine Learning')).toBeInTheDocument()
    expect(screen.getByText('Web Development Fundamentals')).toBeInTheDocument()
    expect(screen.getByText('Digital Marketing Strategy')).toBeInTheDocument()
  })

  test('navigates to registration when Get Started is clicked', () => {
    renderWithProviders(<HomePage />)
    
    const getStartedButton = screen.getByText('Get Started Free')
    fireEvent.click(getStartedButton)
    
    expect(mockNavigate).toHaveBeenCalledWith('/register')
  })

  test('navigates to course creation when authenticated user clicks CTA', () => {
    const authenticatedState = {
      auth: {
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          full_name: 'Test User',
          role: 'user',
          is_active: true,
          is_verified: true,
          created_at: '2023-01-01T00:00:00Z',
        },
        token: 'test-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    }

    renderWithProviders(<HomePage />, authenticatedState)
    
    const createCourseButton = screen.getByText('Create Your First Course')
    fireEvent.click(createCourseButton)
    
    expect(mockNavigate).toHaveBeenCalledWith('/create-course')
  })

  test('navigates to course detail when View Course is clicked', () => {
    renderWithProviders(<HomePage />)
    
    const viewCourseButtons = screen.getAllByText('View Course')
    fireEvent.click(viewCourseButtons[0])
    
    expect(mockNavigate).toHaveBeenCalledWith('/courses/1')
  })

  test('does not show bottom CTA when user is authenticated', () => {
    const authenticatedState = {
      auth: {
        user: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          full_name: 'Test User',
          role: 'user',
          is_active: true,
          is_verified: true,
          created_at: '2023-01-01T00:00:00Z',
        },
        token: 'test-token',
        isAuthenticated: true,
        isLoading: false,
        error: null,
      },
    }

    renderWithProviders(<HomePage />, authenticatedState)
    
    expect(screen.queryByText('Ready to Create Your First Course?')).not.toBeInTheDocument()
  })
})
