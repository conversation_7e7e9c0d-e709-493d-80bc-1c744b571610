/* This file is used to override the default pico.css styles. */

body {
    background-color: #1d1d1d;
}

.container {
    padding: 2rem;
    width: 100%;
    max-width: 100%;
}

[type="checkbox"]:is(:checked, :checked:focus) {
    --pico-border-color: #7fee64;
    --pico-background-color: #7fee64;
}

[type="checkbox"]:not(:checked, :checked:focus) {
    --pico-border-color: rgba(255, 255, 255, 0.2);
    --pico-background-color: rgba(255, 255, 255, 0.05);
}

:where(select, textarea):not([readonly]):focus,
input:not([type=submit], [type=button], [type=reset], [type=range], [type=file], [readonly]):focus {
    --pico-box-shadow: 0 0 0 var(--pico-outline-width) rgba(127, 238, 100, 0.25);
    --pico-border-color: rgba(127, 238, 100, 0.50);
}
