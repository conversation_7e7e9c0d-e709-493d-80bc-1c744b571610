# Koursia Platform - Complete Setup Guide

Welcome to the Koursia AI-powered course creation platform! This guide will help you set up the complete development environment with all services running locally.

## 🚀 Quick Start

### Automated Setup (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd koursia

# Make setup script executable
chmod +x scripts/setup-development.sh

# Run automated setup
./scripts/setup-development.sh
```

### Manual Setup
Follow the detailed instructions in [DEVELOPMENT_SETUP.md](DEVELOPMENT_SETUP.md)

## 📋 What's Included

### Core Services
- **Frontend**: React application with Material-UI (Port 3000)
- **Backend**: FastAPI with async support (Port 8000)
- **Database**: PostgreSQL with sample data (Port 5432)
- **Cache**: Redis for session and caching (Port 6379)

### AI Services Integration
- **Modal GPU Services**: A100 80GB GPU integration for AI processing
- **Whisper**: Speech recognition and transcription
- **TTS**: Text-to-speech with multiple providers
- **Ecomimic**: AI avatar generation
- **Content Generation**: OpenAI GPT and Google Gemini integration

### Development Tools
- **API Documentation**: Swagger UI at http://localhost:8000/docs
- **Database Admin**: pgAdmin (optional, port 5050)
- **Redis Admin**: Redis Commander (optional, port 8081)
- **Testing**: Comprehensive test suites for frontend and backend

## 🔧 Configuration Files

### Environment Configuration
- `.env.template` - Template with all required variables
- `.env` - Your local configuration (created from template)

### Docker Configuration
- `docker-compose.dev.yml` - Development services (PostgreSQL, Redis)
- `docker-compose.yml` - Production configuration

### Service Configuration
- `backend/app/core/config.py` - Backend configuration
- `frontend/src/config/` - Frontend configuration
- `modal-services/` - Modal AI services configuration

## 🛠️ Development Workflow

### Starting Services
```bash
# Start databases
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Start backend (in backend directory)
cd backend
source venv/bin/activate  # or venv\Scripts\activate on Windows
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Start frontend (in frontend directory)
cd frontend
npm start
```

### Running Tests
```bash
# Backend tests
cd backend
pytest --cov=app --cov-report=html

# Frontend tests
cd frontend
npm test -- --coverage --watchAll=false
```

### Validation
```bash
# Validate complete setup
python scripts/validate-setup.py
```

## 🔐 Security Configuration

### Required API Keys
Add these to your `.env` file for full functionality:

```bash
# AI Services
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# Stock Media
PEXELS_API_KEY=your_pexels_api_key_here
PIXABAY_API_KEY=your_pixabay_api_key_here

# TTS Services
CHATTERBOX_API_KEY=your_chatterbox_api_key_here
KOKORO_API_KEY=your_kokoro_api_key_here

# Avatar Generation
ECOMIMIC_API_KEY=your_ecomimic_api_key_here

# AWS S3 Storage
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_S3_BUCKET=your_s3_bucket_name_here

# Modal AI Services
MODAL_TOKEN_ID=your_modal_token_id_here
MODAL_TOKEN_SECRET=your_modal_token_secret_here
```

### Security Best Practices
- ✅ Never commit `.env` files with actual credentials
- ✅ Use placeholder values in version control
- ✅ Rotate credentials regularly
- ✅ Use different credentials for dev/staging/production
- ✅ Enable MFA where possible

## 🌐 Service URLs

### Development URLs
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Interactive API**: http://localhost:8000/redoc

### Admin Interfaces (Optional)
- **pgAdmin**: http://localhost:5050 (<EMAIL> / admin123)
- **Redis Commander**: http://localhost:8081
- **Flower (Celery)**: http://localhost:5555

### Default Login Credentials
- **Email**: <EMAIL>
- **Password**: admin123

## 📊 Testing and Validation

### Health Checks
```bash
# Backend health
curl http://localhost:8000/health

# Database health
curl http://localhost:8000/api/v1/health/db

# Redis health
curl http://localhost:8000/api/v1/health/redis
```

### API Testing
```bash
# User registration
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "full_name": "Test User",
    "password": "testpassword123"
  }'

# User login
curl -X POST http://localhost:8000/api/v1/auth/login/json \
  -H "Content-Type: application/json" \
  -d '{
    "email_or_username": "<EMAIL>",
    "password": "testpassword123"
  }'
```

### Frontend Testing
1. Open http://localhost:3000
2. Navigate through the application
3. Test user registration and login
4. Check browser console for errors

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Find process using port
lsof -i :8000  # macOS/Linux
netstat -ano | findstr :8000  # Windows

# Kill process
kill -9 <PID>  # macOS/Linux
taskkill /PID <PID> /F  # Windows
```

#### Database Connection Issues
```bash
# Check PostgreSQL status
docker-compose -f docker-compose.dev.yml ps postgres

# View PostgreSQL logs
docker-compose -f docker-compose.dev.yml logs postgres

# Restart PostgreSQL
docker-compose -f docker-compose.dev.yml restart postgres
```

#### Frontend Build Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### Backend Import Errors
```bash
# Ensure virtual environment is activated
source venv/bin/activate  # macOS/Linux
venv\Scripts\activate     # Windows

# Reinstall dependencies
pip install -r requirements.txt
```

### Reset Everything
```bash
# Stop all services
docker-compose -f docker-compose.dev.yml down -v

# Remove virtual environment
rm -rf backend/venv

# Remove node_modules
rm -rf frontend/node_modules

# Run setup again
./scripts/setup-development.sh
```

## 📚 Documentation

### API Documentation
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### Code Documentation
- **Backend**: `backend/docs/`
- **Frontend**: `frontend/docs/`
- **Modal Services**: `modal-services/docs/`

### Guides
- [Development Setup](DEVELOPMENT_SETUP.md) - Detailed setup instructions
- [Modal Integration](docs/MODAL_INTEGRATION.md) - AI services deployment
- [API Reference](docs/API.md) - Complete API documentation
- [Frontend Guide](docs/FRONTEND.md) - React application guide
- [Deployment Guide](docs/DEPLOYMENT.md) - Production deployment

## 🤝 Contributing

### Development Workflow
1. Create feature branch from `develop`
2. Make changes and add tests
3. Run validation: `python scripts/validate-setup.py`
4. Submit pull request

### Code Standards
- **Backend**: Follow PEP 8, use type hints
- **Frontend**: Use TypeScript, follow React best practices
- **Testing**: Maintain >80% test coverage
- **Documentation**: Update docs for new features

## 🆘 Support

### Getting Help
- **Documentation**: Check the `/docs` folder
- **Issues**: Create GitHub issue with detailed description
- **Discussions**: Use GitHub Discussions for questions

### Reporting Bugs
1. Check existing issues first
2. Provide detailed reproduction steps
3. Include environment information
4. Add relevant logs and screenshots

## 🎉 Success Checklist

After setup, you should have:
- ✅ Frontend running on http://localhost:3000
- ✅ Backend API running on http://localhost:8000
- ✅ PostgreSQL database with sample data
- ✅ Redis cache running
- ✅ All health checks passing
- ✅ Tests running successfully
- ✅ API documentation accessible
- ✅ Admin user login working

## 🚀 Next Steps

1. **Explore the Platform**: Navigate through the frontend application
2. **Test API Endpoints**: Use the Swagger UI to test API functionality
3. **Configure AI Services**: Add your API keys for full AI functionality
4. **Deploy Modal Services**: Set up GPU-powered AI services
5. **Customize Configuration**: Adjust settings for your specific needs
6. **Start Development**: Begin building your features!

---

**Happy coding! 🎯**

For more detailed information, see [DEVELOPMENT_SETUP.md](DEVELOPMENT_SETUP.md)
