# Modal GPU A100 80GB Integration Guide

This guide provides comprehensive instructions for deploying and managing Koursia's AI services on Modal's GPU infrastructure.

## Overview

Modal provides serverless GPU computing that's perfect for AI workloads. Our integration includes:

- **Whisper Service**: Speech recognition and transcription
- **TTS Service**: Text-to-speech with multiple providers
- **Ecomimic Service**: AI avatar generation
- **Auto-scaling**: Automatic scaling based on demand
- **Cost optimization**: Pay-per-use with intelligent resource management

## Prerequisites

### 1. Modal Account Setup

```bash
# Install Modal CLI
pip install modal

# Authenticate with Modal
modal token new

# Verify authentication
modal token current
```

### 2. API Keys Configuration

Set up your API keys as environment variables:

```bash
export GEMINI_API_KEY="your_gemini_api_key"
export OPENAI_API_KEY="your_openai_api_key"
export CHATTERBOX_API_KEY="your_chatterbox_api_key"
export KOKORO_API_KEY="your_kokoro_api_key"
export ECOMIMIC_API_KEY="your_ecomimic_api_key"
```

### 3. GPU Requirements

- **GPU Type**: NVIDIA A100 80GB
- **CUDA Version**: 11.8+
- **Memory**: 80GB VRAM
- **Compute Capability**: 8.0+

## Deployment

### Quick Deployment

```bash
cd modal-services

# Deploy all services
python deploy.py

# Or deploy individually
modal deploy whisper_service.py
modal deploy tts_service.py
modal deploy ecomimic_service.py
```

### Manual Deployment

```bash
# Setup secrets
modal secret create gemini-api-key GEMINI_API_KEY=$GEMINI_API_KEY
modal secret create openai-api-key OPENAI_API_KEY=$OPENAI_API_KEY
modal secret create chatterbox-api-key CHATTERBOX_API_KEY=$CHATTERBOX_API_KEY
modal secret create kokoro-api-key KOKORO_API_KEY=$KOKORO_API_KEY
modal secret create ecomimic-api-key ECOMIMIC_API_KEY=$ECOMIMIC_API_KEY

# Deploy services
modal deploy whisper_service.py
modal deploy tts_service.py
modal deploy ecomimic_service.py
```

## Service Configuration

### Whisper Service

**Capabilities:**
- Speech-to-text transcription
- Multiple model sizes (base, small, medium, large)
- Subtitle generation (SRT, VTT, ASS)
- Batch processing
- Multi-language support

**API Endpoints:**
```
POST /whisper_webhook
GET /whisper_health
```

**Example Usage:**
```python
import requests

# Transcribe audio
response = requests.post("https://your-modal-url/whisper_webhook", json={
    "action": "transcribe",
    "audio_url": "https://example.com/audio.mp3",
    "model": "base",
    "language": "en"
})

result = response.json()
print(result["text"])
```

### TTS Service

**Capabilities:**
- Multiple TTS providers (Chatterbox, Kokoro, Local)
- High-quality voice synthesis
- Voice customization (speed, pitch, volume)
- Batch text processing
- Multiple output formats

**API Endpoints:**
```
POST /tts_webhook
GET /tts_health
```

**Example Usage:**
```python
# Generate speech
response = requests.post("https://your-modal-url/tts_webhook", json={
    "action": "generate",
    "text": "Hello, welcome to this course!",
    "voice_settings": {
        "provider": "chatterbox",
        "voice_id": "en-US-AriaNeural",
        "speed": 1.0,
        "pitch": 0.0
    },
    "output_format": "mp3"
})

result = response.json()
audio_data = base64.b64decode(result["audio_data"])
```

### Ecomimic Service

**Capabilities:**
- AI avatar video generation
- Multiple avatar styles and backgrounds
- Synchronized speech and animation
- High-quality video output
- Preview generation

**API Endpoints:**
```
POST /ecomimic_webhook
GET /ecomimic_health
```

**Example Usage:**
```python
# Generate avatar video
response = requests.post("https://your-modal-url/ecomimic_webhook", json={
    "action": "generate",
    "lesson_id": "lesson_123",
    "script": "Welcome to this lesson on machine learning!",
    "avatar_settings": {
        "style": "professional",
        "gender": "neutral",
        "background": "office",
        "resolution": "1920x1080"
    }
})

result = response.json()
video_url = result["video_url"]
```

## Performance Optimization

### Auto-scaling Configuration

```python
# In modal_config.py
SCALING_CONFIG = {
    "min_instances": 0,        # Scale to zero when idle
    "max_instances": 10,       # Maximum concurrent instances
    "scale_down_delay": 300,   # 5 minutes before scaling down
    "target_concurrency": 1,   # One request per instance
}
```

### Cost Optimization

```python
# Use preemptible instances for cost savings
COST_OPTIMIZATION = {
    "preemptible": True,       # 50-90% cost reduction
    "auto_shutdown": True,     # Auto-shutdown idle instances
    "resource_limits": {
        "cpu": "8",
        "memory": "32Gi",
        "gpu_memory": "80Gi",
    },
}
```

### Model Caching

```python
# Cache models for faster startup
@modal.enter()
def load_models(self):
    # Models are cached in persistent volumes
    self.whisper_model = whisper.load_model("base", download_root="/models/whisper")
    self.tts_model = TTS(model_name="tts_models/en/ljspeech/tacotron2-DDC")
```

## Monitoring and Logging

### Health Checks

```bash
# Check service health
curl https://your-modal-url/whisper_health
curl https://your-modal-url/tts_health
curl https://your-modal-url/ecomimic_health
```

### Usage Monitoring

```bash
# View Modal usage
modal usage

# Monitor costs
modal usage --json | jq '.credits_used'

# View function logs
modal logs whisper_service.py::whisper_webhook
```

### Custom Monitoring

```python
# monitoring.py
import modal

@modal.Function.from_name("koursia-ai-services", "monitor")
def check_service_health():
    # Custom health check logic
    pass

# Run monitoring
modal run monitoring.py::check_service_health
```

## Security

### API Key Management

```bash
# Rotate API keys
modal secret update gemini-api-key GEMINI_API_KEY=$NEW_GEMINI_KEY

# List secrets
modal secret list

# Delete old secrets
modal secret delete old-api-key
```

### Network Security

```python
# Configure network isolation
SECURITY_CONFIG = {
    "network_isolation": True,
    "encrypted_volumes": True,
    "secure_secrets": True,
    "audit_logging": True,
}
```

### Access Control

```python
# Implement API authentication
@modal.web_endpoint(method="POST")
def protected_endpoint(request_data: Dict[str, Any]):
    api_key = request_data.get("api_key")
    if not validate_api_key(api_key):
        return {"error": "Unauthorized"}
    
    # Process request
    return process_request(request_data)
```

## Troubleshooting

### Common Issues

#### 1. GPU Memory Errors

```python
# Reduce batch size
MODEL_CONFIGS = {
    "whisper": {
        "batch_size": 1,  # Reduce from default
    }
}

# Monitor GPU memory
nvidia-smi
```

#### 2. Cold Start Delays

```python
# Keep instances warm
@app.function(keep_warm=2)  # Keep 2 instances warm
def your_function():
    pass
```

#### 3. API Rate Limits

```python
# Implement retry logic
import time
import random

def retry_with_backoff(func, max_retries=3):
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            wait_time = (2 ** attempt) + random.uniform(0, 1)
            time.sleep(wait_time)
```

#### 4. Model Loading Issues

```bash
# Check model downloads
modal run whisper_service.py::load_models

# Clear model cache
modal volume delete koursia-models
```

### Debugging

```bash
# View function logs
modal logs whisper_service.py::whisper_webhook --follow

# Run function locally for debugging
modal run whisper_service.py::transcribe_audio --audio-url="test.mp3"

# Check container status
modal container list
```

## Cost Management

### Pricing Overview

- **A100 80GB**: ~$4.00/hour
- **Function calls**: $0.0001 per call
- **Storage**: $0.10/GB/month
- **Network**: $0.10/GB

### Cost Optimization Strategies

1. **Use Preemptible Instances**: 50-90% cost reduction
2. **Optimize Keep-Warm**: Balance performance vs cost
3. **Batch Processing**: Process multiple items together
4. **Smart Caching**: Cache frequently used models
5. **Auto-scaling**: Scale to zero when idle

### Budget Alerts

```python
# Set up budget monitoring
def monitor_costs():
    usage = modal.usage()
    if usage.credits_used > BUDGET_THRESHOLD:
        send_alert(f"Budget threshold exceeded: {usage.credits_used}")
```

## Integration with Backend

### Update Backend Configuration

```python
# backend/app/core/config.py
MODAL_SERVICES = {
    "whisper_url": "https://your-modal-url/whisper_webhook",
    "tts_url": "https://your-modal-url/tts_webhook",
    "ecomimic_url": "https://your-modal-url/ecomimic_webhook",
    "api_key": "your-modal-api-key",
}
```

### Service Client

```python
# backend/app/services/modal_client.py
import httpx
from app.core.config import settings

class ModalClient:
    def __init__(self):
        self.base_url = settings.MODAL_BASE_URL
        self.api_key = settings.MODAL_API_KEY
    
    async def transcribe_audio(self, audio_url: str, **kwargs):
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/whisper_webhook",
                json={
                    "action": "transcribe",
                    "audio_url": audio_url,
                    **kwargs
                }
            )
            return response.json()
```

## Best Practices

1. **Resource Management**: Use appropriate GPU sizes for workloads
2. **Error Handling**: Implement robust error handling and retries
3. **Monitoring**: Set up comprehensive monitoring and alerting
4. **Testing**: Test services thoroughly before production deployment
5. **Documentation**: Keep API documentation updated
6. **Security**: Regularly rotate API keys and monitor access
7. **Cost Control**: Monitor usage and set budget alerts

## Support

For Modal-specific issues:
- Modal Documentation: https://modal.com/docs
- Modal Discord: https://discord.gg/modal
- Modal Support: <EMAIL>

For Koursia integration issues:
- GitHub Issues: https://github.com/your-org/koursia/issues
- Email: <EMAIL>
