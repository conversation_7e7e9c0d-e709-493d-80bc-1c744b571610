"""
Test configuration and fixtures
"""

import pytest
import asyncio
from typing import Generator, AsyncGenerator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import Base, get_db
from app.core.security import create_access_token
from app.models.user import User
from app.models.course import Course, Category
from app.models.media import MediaAsset


# Test database URL
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test"""
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Drop tables after test
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(db_session) -> Generator[TestClient, None, None]:
    """Create a test client"""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
def test_user(db_session) -> User:
    """Create a test user"""
    user = User(
        email="<EMAIL>",
        username="testuser",
        full_name="Test User",
        hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # secret
        is_active=True,
        is_verified=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_admin_user(db_session) -> User:
    """Create a test admin user"""
    user = User(
        email="<EMAIL>",
        username="admin",
        full_name="Admin User",
        hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # secret
        is_active=True,
        is_verified=True,
        is_superuser=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def auth_headers(test_user: User) -> dict:
    """Create authentication headers for test user"""
    token = create_access_token(data={"sub": str(test_user.id)})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_auth_headers(test_admin_user: User) -> dict:
    """Create authentication headers for admin user"""
    token = create_access_token(data={"sub": str(test_admin_user.id)})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def test_category(db_session) -> Category:
    """Create a test category"""
    category = Category(
        name="Test Category",
        description="A test category",
        slug="test-category"
    )
    db_session.add(category)
    db_session.commit()
    db_session.refresh(category)
    return category


@pytest.fixture
def test_course(db_session, test_user: User, test_category: Category) -> Course:
    """Create a test course"""
    course = Course(
        title="Test Course",
        description="A test course",
        slug="test-course",
        creator_id=test_user.id,
        category_id=test_category.id,
        status="draft"
    )
    db_session.add(course)
    db_session.commit()
    db_session.refresh(course)
    return course


@pytest.fixture
def test_media_asset(db_session, test_user: User) -> MediaAsset:
    """Create a test media asset"""
    asset = MediaAsset(
        filename="test.jpg",
        original_filename="test_image.jpg",
        title="Test Image",
        file_path="/test/path/test.jpg",
        file_url="/media/test.jpg",
        file_size=1024,
        mime_type="image/jpeg",
        media_type="image",
        uploaded_by=test_user.id
    )
    db_session.add(asset)
    db_session.commit()
    db_session.refresh(asset)
    return asset


# Sample data for testing
@pytest.fixture
def sample_course_data():
    """Sample course creation data"""
    return {
        "title": "Sample Course",
        "description": "A sample course for testing",
        "slug": "sample-course",
        "difficulty": "beginner",
        "language": "en",
        "target_audience": "Beginners",
        "learning_objectives": ["Learn basics", "Understand concepts"],
        "tags": ["test", "sample"]
    }


@pytest.fixture
def sample_user_data():
    """Sample user registration data"""
    return {
        "email": "<EMAIL>",
        "username": "newuser",
        "full_name": "New User",
        "password": "testpassword123"
    }


@pytest.fixture
def sample_generation_request():
    """Sample course generation request"""
    return {
        "title": "AI Generated Course",
        "description": "A course generated by AI",
        "target_audience": "Developers",
        "difficulty": "intermediate",
        "duration_minutes": 60,
        "language": "en",
        "learning_objectives": ["Learn AI", "Build projects"],
        "topics": ["Machine Learning", "Neural Networks"],
        "voice_settings": {
            "provider": "chatterbox",
            "voice_id": "en-US-AriaNeural",
            "speed": 1.0,
            "pitch": 0.0,
            "volume": 1.0
        },
        "slide_template": "default"
    }
