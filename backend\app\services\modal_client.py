"""
Modal AI services client for Koursia platform
Handles communication with Modal-deployed AI services
"""

import httpx
import asyncio
import json
import base64
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


class ModalServiceError(Exception):
    """Custom exception for Modal service errors"""
    pass


class ModalClient:
    """Client for communicating with Modal AI services"""
    
    def __init__(self):
        self.base_timeout = 300  # 5 minutes default timeout
        self.whisper_url = settings.MODAL_WHISPER_URL
        self.tts_url = settings.MODAL_TTS_URL
        self.ecomimic_url = settings.MODAL_ECOMIMIC_URL
        
        # HTTP client configuration
        self.client_config = {
            "timeout": httpx.Timeout(self.base_timeout),
            "limits": httpx.Limits(max_connections=10, max_keepalive_connections=5),
            "follow_redirects": True,
        }
    
    async def _make_request(
        self,
        url: str,
        data: Dict[str, Any],
        timeout: Optional[int] = None,
        retries: int = 3,
    ) -> Dict[str, Any]:
        """Make HTTP request to Modal service with retry logic"""
        
        request_timeout = timeout or self.base_timeout
        
        for attempt in range(retries):
            try:
                async with httpx.AsyncClient(**self.client_config) as client:
                    response = await client.post(
                        url,
                        json=data,
                        timeout=request_timeout,
                    )
                    
                    if response.status_code == 200:
                        return response.json()
                    else:
                        error_msg = f"Modal service error: {response.status_code} - {response.text}"
                        logger.error(error_msg)
                        
                        if attempt == retries - 1:
                            raise ModalServiceError(error_msg)
                        
                        # Exponential backoff
                        await asyncio.sleep(2 ** attempt)
                        
            except httpx.TimeoutException:
                error_msg = f"Modal service timeout after {request_timeout}s"
                logger.error(error_msg)
                
                if attempt == retries - 1:
                    raise ModalServiceError(error_msg)
                
                await asyncio.sleep(2 ** attempt)
                
            except Exception as e:
                error_msg = f"Modal service request failed: {str(e)}"
                logger.error(error_msg)
                
                if attempt == retries - 1:
                    raise ModalServiceError(error_msg)
                
                await asyncio.sleep(2 ** attempt)
        
        raise ModalServiceError("All retry attempts failed")
    
    async def transcribe_audio(
        self,
        audio_url: str,
        model: str = "base",
        language: Optional[str] = None,
        task: str = "transcribe",
        options: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Transcribe audio using Modal Whisper service
        
        Args:
            audio_url: URL to audio file
            model: Whisper model size (base, small, medium, large)
            language: Source language (auto-detect if None)
            task: transcribe or translate
            options: Additional Whisper options
        
        Returns:
            Transcription result with text, segments, and metadata
        """
        
        if not self.whisper_url:
            raise ModalServiceError("Whisper service URL not configured")
        
        request_data = {
            "action": "transcribe",
            "audio_url": audio_url,
            "model": model,
            "language": language,
            "task": task,
            "options": options or {},
        }
        
        logger.info(f"Transcribing audio with model {model}")
        
        try:
            result = await self._make_request(
                self.whisper_url,
                request_data,
                timeout=600,  # 10 minutes for transcription
            )
            
            if "error" in result:
                raise ModalServiceError(f"Whisper service error: {result['error']}")
            
            logger.info(f"Transcription completed: {len(result.get('text', ''))} characters")
            return result
            
        except Exception as e:
            logger.error(f"Audio transcription failed: {e}")
            raise ModalServiceError(f"Audio transcription failed: {e}")
    
    async def generate_subtitles(
        self,
        audio_url: str,
        format: str = "srt",
        model: str = "base",
        language: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Generate subtitles from audio using Modal Whisper service
        
        Args:
            audio_url: URL to audio file
            format: Subtitle format (srt, vtt, ass)
            model: Whisper model size
            language: Source language
        
        Returns:
            Subtitle content and metadata
        """
        
        if not self.whisper_url:
            raise ModalServiceError("Whisper service URL not configured")
        
        request_data = {
            "action": "subtitles",
            "audio_url": audio_url,
            "format": format,
            "model": model,
            "language": language,
        }
        
        logger.info(f"Generating {format} subtitles")
        
        try:
            result = await self._make_request(
                self.whisper_url,
                request_data,
                timeout=600,
            )
            
            if "error" in result:
                raise ModalServiceError(f"Subtitle generation error: {result['error']}")
            
            logger.info(f"Subtitles generated in {format} format")
            return result
            
        except Exception as e:
            logger.error(f"Subtitle generation failed: {e}")
            raise ModalServiceError(f"Subtitle generation failed: {e}")
    
    async def generate_speech(
        self,
        text: str,
        voice_settings: Dict[str, Any],
        output_format: str = "mp3",
    ) -> Dict[str, Any]:
        """
        Generate speech from text using Modal TTS service
        
        Args:
            text: Text to convert to speech
            voice_settings: Voice configuration
            output_format: Output audio format
        
        Returns:
            Audio data and metadata
        """
        
        if not self.tts_url:
            raise ModalServiceError("TTS service URL not configured")
        
        request_data = {
            "action": "generate",
            "text": text,
            "voice_settings": voice_settings,
            "output_format": output_format,
        }
        
        logger.info(f"Generating speech for {len(text)} characters")
        
        try:
            result = await self._make_request(
                self.tts_url,
                request_data,
                timeout=300,  # 5 minutes for TTS
            )
            
            if "error" in result:
                raise ModalServiceError(f"TTS service error: {result['error']}")
            
            logger.info(f"Speech generated in {output_format} format")
            return result
            
        except Exception as e:
            logger.error(f"Speech generation failed: {e}")
            raise ModalServiceError(f"Speech generation failed: {e}")
    
    async def get_available_voices(self, provider: str = "chatterbox") -> Dict[str, Any]:
        """
        Get available voices from TTS service
        
        Args:
            provider: TTS provider name
        
        Returns:
            List of available voices
        """
        
        if not self.tts_url:
            raise ModalServiceError("TTS service URL not configured")
        
        request_data = {
            "action": "voices",
            "provider": provider,
        }
        
        try:
            result = await self._make_request(self.tts_url, request_data)
            
            if "error" in result:
                raise ModalServiceError(f"Voice list error: {result['error']}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get available voices: {e}")
            raise ModalServiceError(f"Failed to get available voices: {e}")
    
    async def generate_avatar_video(
        self,
        lesson_id: str,
        script: str,
        avatar_settings: Dict[str, Any],
        audio_url: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Generate avatar video using Modal Ecomimic service
        
        Args:
            lesson_id: Unique lesson identifier
            script: Text script for avatar
            avatar_settings: Avatar configuration
            audio_url: Pre-generated audio URL
        
        Returns:
            Avatar video generation result
        """
        
        if not self.ecomimic_url:
            raise ModalServiceError("Ecomimic service URL not configured")
        
        request_data = {
            "action": "generate",
            "lesson_id": lesson_id,
            "script": script,
            "avatar_settings": avatar_settings,
            "audio_url": audio_url,
        }
        
        logger.info(f"Generating avatar video for lesson {lesson_id}")
        
        try:
            result = await self._make_request(
                self.ecomimic_url,
                request_data,
                timeout=3600,  # 1 hour for avatar generation
            )
            
            if "error" in result:
                raise ModalServiceError(f"Avatar generation error: {result['error']}")
            
            logger.info(f"Avatar video generated for lesson {lesson_id}")
            return result
            
        except Exception as e:
            logger.error(f"Avatar video generation failed: {e}")
            raise ModalServiceError(f"Avatar video generation failed: {e}")
    
    async def get_available_avatars(self) -> Dict[str, Any]:
        """Get available avatar styles from Ecomimic service"""
        
        if not self.ecomimic_url:
            raise ModalServiceError("Ecomimic service URL not configured")
        
        request_data = {"action": "avatars"}
        
        try:
            result = await self._make_request(self.ecomimic_url, request_data)
            
            if "error" in result:
                raise ModalServiceError(f"Avatar list error: {result['error']}")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get available avatars: {e}")
            raise ModalServiceError(f"Failed to get available avatars: {e}")
    
    async def generate_avatar_preview(
        self,
        avatar_settings: Dict[str, Any],
        sample_text: str = "Hello, welcome to this course!",
    ) -> Dict[str, Any]:
        """Generate avatar preview"""
        
        if not self.ecomimic_url:
            raise ModalServiceError("Ecomimic service URL not configured")
        
        request_data = {
            "action": "preview",
            "avatar_settings": avatar_settings,
            "sample_text": sample_text,
        }
        
        try:
            result = await self._make_request(
                self.ecomimic_url,
                request_data,
                timeout=600,  # 10 minutes for preview
            )
            
            if "error" in result:
                raise ModalServiceError(f"Avatar preview error: {result['error']}")
            
            return result
            
        except Exception as e:
            logger.error(f"Avatar preview generation failed: {e}")
            raise ModalServiceError(f"Avatar preview generation failed: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of all Modal services"""
        
        services = {
            "whisper": self.whisper_url,
            "tts": self.tts_url,
            "ecomimic": self.ecomimic_url,
        }
        
        health_status = {}
        
        for service_name, service_url in services.items():
            if not service_url:
                health_status[service_name] = {
                    "status": "not_configured",
                    "error": "Service URL not configured",
                }
                continue
            
            try:
                # Try to make a simple health check request
                async with httpx.AsyncClient(timeout=10.0) as client:
                    response = await client.get(f"{service_url.replace('/webhook', '/health')}")
                    
                    if response.status_code == 200:
                        health_status[service_name] = {
                            "status": "healthy",
                            "response_time": response.elapsed.total_seconds(),
                        }
                    else:
                        health_status[service_name] = {
                            "status": "unhealthy",
                            "error": f"HTTP {response.status_code}",
                        }
                        
            except Exception as e:
                health_status[service_name] = {
                    "status": "error",
                    "error": str(e),
                }
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "services": health_status,
            "overall_status": "healthy" if all(
                s.get("status") == "healthy" for s in health_status.values()
            ) else "degraded",
        }


# Global client instance
modal_client = ModalClient()
