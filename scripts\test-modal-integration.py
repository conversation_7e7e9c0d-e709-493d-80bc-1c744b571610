#!/usr/bin/env python3
"""
Comprehensive test script for Modal AI services integration
Tests all deployed services and validates end-to-end functionality
"""

import asyncio
import json
import time
import sys
from pathlib import Path
from typing import Dict, Any, List
import httpx
import logging

# Add backend to path for imports
sys.path.append(str(Path(__file__).parent.parent / "backend"))

from app.services.modal_client import ModalClient, ModalServiceError

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModalIntegrationTester:
    """Comprehensive tester for Modal AI services integration"""
    
    def __init__(self):
        self.client = ModalClient()
        self.test_results = {}
        self.start_time = time.time()
    
    async def test_service_health(self) -> Dict[str, Any]:
        """Test health of all Modal services"""
        logger.info("🏥 Testing Modal services health...")
        
        try:
            health_status = await self.client.health_check()
            
            for service, status in health_status["services"].items():
                if status["status"] == "healthy":
                    logger.info(f"✅ {service.capitalize()} service is healthy")
                else:
                    logger.error(f"❌ {service.capitalize()} service is unhealthy: {status.get('error', 'Unknown error')}")
            
            self.test_results["health_check"] = health_status
            return health_status
            
        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
            self.test_results["health_check"] = {"error": str(e)}
            return {"error": str(e)}
    
    async def test_whisper_service(self) -> Dict[str, Any]:
        """Test Whisper speech recognition service"""
        logger.info("🎤 Testing Whisper service...")
        
        # Test data - using a public test audio file
        test_audio_url = "https://www2.cs.uic.edu/~i101/SoundFiles/BabyElephantWalk60.wav"
        
        tests = [
            {
                "name": "Basic Transcription",
                "params": {
                    "audio_url": test_audio_url,
                    "model": "base",
                    "language": "en"
                }
            },
            {
                "name": "Subtitle Generation",
                "method": "generate_subtitles",
                "params": {
                    "audio_url": test_audio_url,
                    "format": "srt",
                    "model": "base"
                }
            }
        ]
        
        whisper_results = {}
        
        for test in tests:
            try:
                logger.info(f"  Testing: {test['name']}")
                
                if test.get("method") == "generate_subtitles":
                    result = await self.client.generate_subtitles(**test["params"])
                else:
                    result = await self.client.transcribe_audio(**test["params"])
                
                if "error" not in result:
                    logger.info(f"  ✅ {test['name']} passed")
                    whisper_results[test["name"]] = {
                        "status": "success",
                        "result_length": len(str(result.get("text", result.get("content", ""))))
                    }
                else:
                    logger.error(f"  ❌ {test['name']} failed: {result['error']}")
                    whisper_results[test["name"]] = {
                        "status": "error",
                        "error": result["error"]
                    }
                    
            except Exception as e:
                logger.error(f"  ❌ {test['name']} failed with exception: {e}")
                whisper_results[test["name"]] = {
                    "status": "exception",
                    "error": str(e)
                }
        
        self.test_results["whisper"] = whisper_results
        return whisper_results
    
    async def test_tts_service(self) -> Dict[str, Any]:
        """Test TTS service"""
        logger.info("🔊 Testing TTS service...")
        
        tests = [
            {
                "name": "Basic Speech Generation",
                "params": {
                    "text": "Hello, this is a test of the text-to-speech service.",
                    "voice_settings": {
                        "provider": "chatterbox",
                        "voice_id": "en-US-AriaNeural",
                        "speed": 1.0
                    },
                    "output_format": "mp3"
                }
            },
            {
                "name": "Get Available Voices",
                "method": "get_available_voices",
                "params": {
                    "provider": "chatterbox"
                }
            }
        ]
        
        tts_results = {}
        
        for test in tests:
            try:
                logger.info(f"  Testing: {test['name']}")
                
                if test.get("method") == "get_available_voices":
                    result = await self.client.get_available_voices(**test["params"])
                else:
                    result = await self.client.generate_speech(**test["params"])
                
                if "error" not in result:
                    logger.info(f"  ✅ {test['name']} passed")
                    tts_results[test["name"]] = {
                        "status": "success",
                        "has_audio_data": "audio_data" in result,
                        "format": result.get("format", "unknown")
                    }
                else:
                    logger.error(f"  ❌ {test['name']} failed: {result['error']}")
                    tts_results[test["name"]] = {
                        "status": "error",
                        "error": result["error"]
                    }
                    
            except Exception as e:
                logger.error(f"  ❌ {test['name']} failed with exception: {e}")
                tts_results[test["name"]] = {
                    "status": "exception",
                    "error": str(e)
                }
        
        self.test_results["tts"] = tts_results
        return tts_results
    
    async def test_ecomimic_service(self) -> Dict[str, Any]:
        """Test Ecomimic avatar generation service"""
        logger.info("🎭 Testing Ecomimic service...")
        
        tests = [
            {
                "name": "Get Available Avatars",
                "method": "get_available_avatars",
                "params": {}
            },
            {
                "name": "Generate Avatar Preview",
                "method": "generate_avatar_preview",
                "params": {
                    "avatar_settings": {
                        "style": "professional",
                        "gender": "neutral",
                        "background": "office"
                    },
                    "sample_text": "Welcome to this test lesson!"
                }
            }
        ]
        
        ecomimic_results = {}
        
        for test in tests:
            try:
                logger.info(f"  Testing: {test['name']}")
                
                if test["method"] == "get_available_avatars":
                    result = await self.client.get_available_avatars()
                elif test["method"] == "generate_avatar_preview":
                    result = await self.client.generate_avatar_preview(**test["params"])
                else:
                    continue
                
                if "error" not in result:
                    logger.info(f"  ✅ {test['name']} passed")
                    ecomimic_results[test["name"]] = {
                        "status": "success",
                        "has_video_url": "video_url" in result
                    }
                else:
                    logger.error(f"  ❌ {test['name']} failed: {result['error']}")
                    ecomimic_results[test["name"]] = {
                        "status": "error",
                        "error": result["error"]
                    }
                    
            except Exception as e:
                logger.error(f"  ❌ {test['name']} failed with exception: {e}")
                ecomimic_results[test["name"]] = {
                    "status": "exception",
                    "error": str(e)
                }
        
        self.test_results["ecomimic"] = ecomimic_results
        return ecomimic_results
    
    async def test_end_to_end_workflow(self) -> Dict[str, Any]:
        """Test complete course creation workflow using Modal services"""
        logger.info("🔄 Testing end-to-end workflow...")
        
        workflow_results = {}
        
        try:
            # Step 1: Generate speech from text
            logger.info("  Step 1: Generating speech...")
            speech_result = await self.client.generate_speech(
                text="Welcome to this AI-generated course lesson!",
                voice_settings={
                    "provider": "chatterbox",
                    "voice_id": "en-US-AriaNeural"
                }
            )
            
            if "error" in speech_result:
                raise Exception(f"Speech generation failed: {speech_result['error']}")
            
            workflow_results["speech_generation"] = "success"
            logger.info("  ✅ Speech generation completed")
            
            # Step 2: Create avatar preview (simplified)
            logger.info("  Step 2: Creating avatar preview...")
            avatar_result = await self.client.generate_avatar_preview(
                avatar_settings={
                    "style": "professional",
                    "background": "office"
                },
                sample_text="Welcome to this AI-generated course lesson!"
            )
            
            if "error" in avatar_result:
                logger.warning(f"  ⚠️ Avatar generation failed: {avatar_result['error']}")
                workflow_results["avatar_generation"] = "failed"
            else:
                workflow_results["avatar_generation"] = "success"
                logger.info("  ✅ Avatar preview completed")
            
            # Step 3: Test transcription (simulate feedback loop)
            logger.info("  Step 3: Testing transcription...")
            # This would use the generated audio in a real scenario
            # For testing, we'll use a sample audio file
            transcription_result = await self.client.transcribe_audio(
                audio_url="https://www2.cs.uic.edu/~i101/SoundFiles/BabyElephantWalk60.wav",
                model="base"
            )
            
            if "error" in transcription_result:
                logger.warning(f"  ⚠️ Transcription failed: {transcription_result['error']}")
                workflow_results["transcription"] = "failed"
            else:
                workflow_results["transcription"] = "success"
                logger.info("  ✅ Transcription completed")
            
            workflow_results["overall_status"] = "success"
            logger.info("✅ End-to-end workflow test completed successfully")
            
        except Exception as e:
            logger.error(f"❌ End-to-end workflow failed: {e}")
            workflow_results["overall_status"] = "failed"
            workflow_results["error"] = str(e)
        
        self.test_results["end_to_end"] = workflow_results
        return workflow_results
    
    async def test_performance(self) -> Dict[str, Any]:
        """Test performance metrics of Modal services"""
        logger.info("⚡ Testing performance...")
        
        performance_results = {}
        
        # Test response times
        services = [
            ("health_check", self.client.health_check, {}),
            ("get_voices", self.client.get_available_voices, {"provider": "chatterbox"}),
            ("get_avatars", self.client.get_available_avatars, {})
        ]
        
        for service_name, service_method, params in services:
            try:
                start_time = time.time()
                result = await service_method(**params)
                end_time = time.time()
                
                response_time = end_time - start_time
                
                if "error" not in result:
                    performance_results[service_name] = {
                        "response_time": response_time,
                        "status": "success"
                    }
                    logger.info(f"  ✅ {service_name}: {response_time:.2f}s")
                else:
                    performance_results[service_name] = {
                        "response_time": response_time,
                        "status": "error",
                        "error": result["error"]
                    }
                    logger.error(f"  ❌ {service_name}: {response_time:.2f}s (error)")
                    
            except Exception as e:
                performance_results[service_name] = {
                    "status": "exception",
                    "error": str(e)
                }
                logger.error(f"  ❌ {service_name}: Exception - {e}")
        
        self.test_results["performance"] = performance_results
        return performance_results
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_time = time.time() - self.start_time
        
        # Count successes and failures
        total_tests = 0
        successful_tests = 0
        
        for category, tests in self.test_results.items():
            if category == "health_check":
                continue
                
            if isinstance(tests, dict):
                for test_name, result in tests.items():
                    total_tests += 1
                    if result.get("status") == "success":
                        successful_tests += 1
        
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_execution_time": f"{total_time:.2f}s",
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "success_rate": f"{success_rate:.1f}%",
            "test_results": self.test_results,
            "summary": {
                "overall_status": "PASS" if success_rate >= 80 else "FAIL",
                "recommendations": []
            }
        }
        
        # Add recommendations based on results
        if success_rate < 100:
            report["summary"]["recommendations"].append("Some tests failed - check service configurations")
        
        if success_rate >= 80:
            report["summary"]["recommendations"].append("Modal integration is ready for production")
        else:
            report["summary"]["recommendations"].append("Modal integration needs attention before production")
        
        return report
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and generate report"""
        logger.info("🚀 Starting comprehensive Modal integration tests...")
        
        # Run all test categories
        await self.test_service_health()
        await self.test_whisper_service()
        await self.test_tts_service()
        await self.test_ecomimic_service()
        await self.test_end_to_end_workflow()
        await self.test_performance()
        
        # Generate and save report
        report = self.generate_report()
        
        # Save report to file
        report_file = Path("modal_integration_test_report.json")
        with open(report_file, "w") as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"📊 Test report saved to {report_file}")
        
        # Print summary
        logger.info("=" * 60)
        logger.info("🎯 TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {report['total_tests']}")
        logger.info(f"Successful: {report['successful_tests']}")
        logger.info(f"Success Rate: {report['success_rate']}")
        logger.info(f"Overall Status: {report['summary']['overall_status']}")
        logger.info(f"Execution Time: {report['total_execution_time']}")
        
        if report["summary"]["recommendations"]:
            logger.info("\n📋 Recommendations:")
            for rec in report["summary"]["recommendations"]:
                logger.info(f"  • {rec}")
        
        return report


async def main():
    """Main test execution function"""
    tester = ModalIntegrationTester()
    
    try:
        report = await tester.run_all_tests()
        
        # Exit with appropriate code
        if report["summary"]["overall_status"] == "PASS":
            logger.info("🎉 All tests completed successfully!")
            sys.exit(0)
        else:
            logger.error("❌ Some tests failed. Check the report for details.")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
