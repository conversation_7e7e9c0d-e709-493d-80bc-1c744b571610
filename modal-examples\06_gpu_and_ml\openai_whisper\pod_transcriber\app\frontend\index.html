<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link
      rel="icon"
      href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔊</text></svg>"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Modal Podcast Transcriber</title>
  </head>

  <body>
    <script>
      /* Empty but possibly addresses https://bugzilla.mozilla.org/show_bug.cgi?id=1404468 */
    </script>
    <a
      class="github-corner"
      href="https://github.com/modal-labs/modal-examples/tree/main/06_gpu_and_ml/openai_whisper/pod_transcriber"
      target="_blank"
      id="fork-corner"
      title="Fork me on GitHub"
    >
      <svg width="80" height="80" viewbox="0 0 250 250">
        <title>Fork on GitHub</title>
        <path d="M0 0h250v250"></path>
        <path
          class="octo-arm"
          d="M127.4 110c-14.6-9.2-9.4-19.5-9.4-19.5 3-7 1.5-11 1.5-11-1-6.2 3-2 3-2 4 4.7 2 11 2 11-2.2 10.4 5 14.8 9 16.2"
          fill="currentColor"
          style="transform-origin: 130px 110px"
        ></path>
        <path
          class="octo-body"
          d="M113.2 114.3s3.6 1.6 4.7.6l15-13.7c3-2.4 6-3 8.2-2.7-8-11.2-14-25 3-41 4.7-4.4 10.6-6.4 16.2-6.4.6-1.6 3.6-7.3 11.8-10.7 0 0 4.5 2.7 6.8 16.5 4.3 2.7 8.3 6 12 9.8 3.3 3.5 6.7 8 8.6 12.3 14 3 16.8 8 16.8 8-3.4 8-9.4 11-11.4 11 0 5.8-2.3 11-7.5 15.5-16.4 16-30 9-40 .2 0 3-1 7-5.2 11l-13.3 11c-1 1 .5 5.3.8 5z"
          fill="currentColor"
        ></path>
      </svg>
      <style>
        .github-corner svg {
          position: absolute;
          right: 0;
          top: 0;
          mix-blend-mode: darken;
          color: #ffffff;
          fill: #242424;
        }
        .github-corner:hover .octo-arm {
          animation: octocat-wave 0.56s;
        }
        @keyframes octocat-wave {
          0%,
          100% {
            transform: rotate(0);
          }
          20%,
          60% {
            transform: rotate(-20deg);
          }
          40%,
          80% {
            transform: rotate(10deg);
          }
        }
      </style>
    </a>
    <div id="root" class=""></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
