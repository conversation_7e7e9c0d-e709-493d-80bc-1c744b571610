#!/usr/bin/env python3
"""
Koursia Platform Setup Validation Script
Comprehensive validation of the development environment setup
"""

import asyncio
import json
import os
import sys
import time
import subprocess
from pathlib import Path
from typing import Dict, List, Any
import httpx
import psycopg2
import redis
from datetime import datetime

class SetupValidator:
    """Validates the complete Koursia platform setup"""
    
    def __init__(self):
        self.results = {}
        self.start_time = time.time()
        self.base_dir = Path(__file__).parent.parent
        
        # Configuration
        self.frontend_url = "http://localhost:3000"
        self.backend_url = "http://localhost:8000"
        self.postgres_config = {
            "host": "localhost",
            "port": 5432,
            "database": "koursia",
            "user": "koursia_user",
            "password": "koursia_dev_password"
        }
        self.redis_config = {
            "host": "localhost",
            "port": 6379,
            "db": 0
        }
    
    def print_status(self, message: str, status: str = "info"):
        """Print colored status messages"""
        colors = {
            "info": "\033[0;34m",      # Blue
            "success": "\033[0;32m",   # Green
            "warning": "\033[1;33m",   # Yellow
            "error": "\033[0;31m",     # Red
            "reset": "\033[0m"         # Reset
        }
        
        icons = {
            "info": "ℹ️ ",
            "success": "✅",
            "warning": "⚠️ ",
            "error": "❌"
        }
        
        color = colors.get(status, colors["info"])
        icon = icons.get(status, "")
        reset = colors["reset"]
        
        print(f"{color}{icon} {message}{reset}")
    
    def validate_environment_file(self) -> Dict[str, Any]:
        """Validate .env file exists and has required variables"""
        self.print_status("Validating environment configuration...", "info")
        
        env_file = self.base_dir / ".env"
        result = {
            "status": "success",
            "details": {},
            "errors": []
        }
        
        if not env_file.exists():
            result["status"] = "error"
            result["errors"].append(".env file not found")
            return result
        
        # Required environment variables
        required_vars = [
            "SECRET_KEY",
            "JWT_SECRET_KEY",
            "DATABASE_URL",
            "REDIS_URL",
            "FRONTEND_URL",
            "BACKEND_URL"
        ]
        
        # Load environment variables
        with open(env_file, 'r') as f:
            env_content = f.read()
        
        missing_vars = []
        placeholder_vars = []
        
        for var in required_vars:
            if f"{var}=" not in env_content:
                missing_vars.append(var)
            elif f"{var}=your_" in env_content or f"{var}=placeholder" in env_content:
                placeholder_vars.append(var)
        
        if missing_vars:
            result["status"] = "error"
            result["errors"].extend([f"Missing variable: {var}" for var in missing_vars])
        
        if placeholder_vars:
            result["status"] = "warning" if result["status"] != "error" else "error"
            result["details"]["placeholder_vars"] = placeholder_vars
        
        result["details"]["env_file_size"] = env_file.stat().st_size
        
        if result["status"] == "success":
            self.print_status("Environment configuration is valid", "success")
        elif result["status"] == "warning":
            self.print_status("Environment configuration has warnings", "warning")
        else:
            self.print_status("Environment configuration has errors", "error")
        
        return result
    
    def validate_database_connection(self) -> Dict[str, Any]:
        """Validate PostgreSQL database connection and structure"""
        self.print_status("Validating database connection...", "info")
        
        result = {
            "status": "success",
            "details": {},
            "errors": []
        }
        
        try:
            # Test connection
            conn = psycopg2.connect(**self.postgres_config)
            cursor = conn.cursor()
            
            # Test basic query
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            result["details"]["postgres_version"] = version
            
            # Check if tables exist
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
            """)
            tables = [row[0] for row in cursor.fetchall()]
            result["details"]["tables"] = tables
            
            # Check for required tables
            required_tables = ["users", "courses", "plans", "subscriptions"]
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                result["status"] = "warning"
                result["details"]["missing_tables"] = missing_tables
            
            # Check for admin user
            cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
            admin_count = cursor.fetchone()[0]
            result["details"]["admin_users"] = admin_count
            
            cursor.close()
            conn.close()
            
            self.print_status("Database connection successful", "success")
            
        except Exception as e:
            result["status"] = "error"
            result["errors"].append(f"Database connection failed: {str(e)}")
            self.print_status(f"Database connection failed: {str(e)}", "error")
        
        return result
    
    def validate_redis_connection(self) -> Dict[str, Any]:
        """Validate Redis connection"""
        self.print_status("Validating Redis connection...", "info")
        
        result = {
            "status": "success",
            "details": {},
            "errors": []
        }
        
        try:
            r = redis.Redis(**self.redis_config)
            
            # Test connection
            pong = r.ping()
            if not pong:
                raise Exception("Redis ping failed")
            
            # Get Redis info
            info = r.info()
            result["details"]["redis_version"] = info.get("redis_version")
            result["details"]["used_memory"] = info.get("used_memory_human")
            result["details"]["connected_clients"] = info.get("connected_clients")
            
            # Test set/get
            test_key = "koursia_test"
            r.set(test_key, "test_value", ex=10)
            test_value = r.get(test_key)
            
            if test_value.decode() != "test_value":
                raise Exception("Redis set/get test failed")
            
            r.delete(test_key)
            
            self.print_status("Redis connection successful", "success")
            
        except Exception as e:
            result["status"] = "error"
            result["errors"].append(f"Redis connection failed: {str(e)}")
            self.print_status(f"Redis connection failed: {str(e)}", "error")
        
        return result
    
    async def validate_backend_api(self) -> Dict[str, Any]:
        """Validate backend API endpoints"""
        self.print_status("Validating backend API...", "info")
        
        result = {
            "status": "success",
            "details": {},
            "errors": []
        }
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Test health endpoint
                health_response = await client.get(f"{self.backend_url}/health")
                if health_response.status_code != 200:
                    raise Exception(f"Health endpoint returned {health_response.status_code}")
                
                result["details"]["health_check"] = health_response.json()
                
                # Test API documentation
                docs_response = await client.get(f"{self.backend_url}/docs")
                if docs_response.status_code != 200:
                    result["status"] = "warning"
                    result["details"]["docs_available"] = False
                else:
                    result["details"]["docs_available"] = True
                
                # Test database health endpoint
                try:
                    db_health_response = await client.get(f"{self.backend_url}/api/v1/health/db")
                    result["details"]["db_health"] = db_health_response.status_code == 200
                except:
                    result["details"]["db_health"] = False
                
                # Test Redis health endpoint
                try:
                    redis_health_response = await client.get(f"{self.backend_url}/api/v1/health/redis")
                    result["details"]["redis_health"] = redis_health_response.status_code == 200
                except:
                    result["details"]["redis_health"] = False
                
                # Test authentication endpoints
                try:
                    auth_response = await client.get(f"{self.backend_url}/api/v1/auth/me")
                    result["details"]["auth_endpoint"] = auth_response.status_code in [401, 422]  # Expected for unauthenticated
                except:
                    result["details"]["auth_endpoint"] = False
                
                self.print_status("Backend API validation successful", "success")
                
        except Exception as e:
            result["status"] = "error"
            result["errors"].append(f"Backend API validation failed: {str(e)}")
            self.print_status(f"Backend API validation failed: {str(e)}", "error")
        
        return result
    
    async def validate_frontend(self) -> Dict[str, Any]:
        """Validate frontend application"""
        self.print_status("Validating frontend application...", "info")
        
        result = {
            "status": "success",
            "details": {},
            "errors": []
        }
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Test frontend accessibility
                frontend_response = await client.get(self.frontend_url)
                if frontend_response.status_code != 200:
                    raise Exception(f"Frontend returned {frontend_response.status_code}")
                
                # Check if it's a React app
                content = frontend_response.text
                is_react = "react" in content.lower() or "root" in content
                result["details"]["is_react_app"] = is_react
                
                # Check for common assets
                try:
                    static_response = await client.get(f"{self.frontend_url}/static/js/")
                    result["details"]["static_assets"] = static_response.status_code == 200
                except:
                    result["details"]["static_assets"] = False
                
                result["details"]["content_length"] = len(content)
                
                self.print_status("Frontend validation successful", "success")
                
        except Exception as e:
            result["status"] = "error"
            result["errors"].append(f"Frontend validation failed: {str(e)}")
            self.print_status(f"Frontend validation failed: {str(e)}", "error")
        
        return result
    
    def validate_docker_services(self) -> Dict[str, Any]:
        """Validate Docker services are running"""
        self.print_status("Validating Docker services...", "info")
        
        result = {
            "status": "success",
            "details": {},
            "errors": []
        }
        
        try:
            # Check if Docker is running
            docker_result = subprocess.run(
                ["docker", "ps", "--format", "table {{.Names}}\t{{.Status}}"],
                capture_output=True,
                text=True,
                check=True
            )
            
            containers = docker_result.stdout.strip().split('\n')[1:]  # Skip header
            running_containers = []
            
            for container in containers:
                if container.strip():
                    name, status = container.split('\t', 1)
                    running_containers.append({
                        "name": name.strip(),
                        "status": status.strip()
                    })
            
            result["details"]["running_containers"] = running_containers
            
            # Check for required containers
            required_containers = ["koursia_postgres", "koursia_redis"]
            running_names = [c["name"] for c in running_containers]
            
            missing_containers = [name for name in required_containers if name not in running_names]
            if missing_containers:
                result["status"] = "warning"
                result["details"]["missing_containers"] = missing_containers
            
            self.print_status("Docker services validation successful", "success")
            
        except subprocess.CalledProcessError as e:
            result["status"] = "error"
            result["errors"].append(f"Docker command failed: {str(e)}")
            self.print_status(f"Docker validation failed: {str(e)}", "error")
        except Exception as e:
            result["status"] = "error"
            result["errors"].append(f"Docker validation failed: {str(e)}")
            self.print_status(f"Docker validation failed: {str(e)}", "error")
        
        return result
    
    def validate_file_structure(self) -> Dict[str, Any]:
        """Validate project file structure"""
        self.print_status("Validating project file structure...", "info")
        
        result = {
            "status": "success",
            "details": {},
            "errors": []
        }
        
        # Required files and directories
        required_paths = [
            ".env",
            "backend/",
            "frontend/",
            "backend/app/",
            "backend/requirements.txt",
            "frontend/package.json",
            "frontend/src/",
            "docker-compose.dev.yml",
        ]
        
        missing_paths = []
        existing_paths = []
        
        for path in required_paths:
            full_path = self.base_dir / path
            if full_path.exists():
                existing_paths.append(path)
            else:
                missing_paths.append(path)
        
        result["details"]["existing_paths"] = existing_paths
        
        if missing_paths:
            result["status"] = "error"
            result["details"]["missing_paths"] = missing_paths
            result["errors"].extend([f"Missing: {path}" for path in missing_paths])
        
        if result["status"] == "success":
            self.print_status("File structure validation successful", "success")
        else:
            self.print_status("File structure validation failed", "error")
        
        return result
    
    async def run_integration_test(self) -> Dict[str, Any]:
        """Run a simple integration test"""
        self.print_status("Running integration test...", "info")
        
        result = {
            "status": "success",
            "details": {},
            "errors": []
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Test user registration
                test_user = {
                    "email": f"test_{int(time.time())}@example.com",
                    "username": f"testuser_{int(time.time())}",
                    "full_name": "Test User",
                    "password": "testpassword123"
                }
                
                register_response = await client.post(
                    f"{self.backend_url}/api/v1/auth/register",
                    json=test_user
                )
                
                if register_response.status_code == 200:
                    result["details"]["user_registration"] = "success"
                    
                    # Test user login
                    login_data = {
                        "email_or_username": test_user["email"],
                        "password": test_user["password"]
                    }
                    
                    login_response = await client.post(
                        f"{self.backend_url}/api/v1/auth/login/json",
                        json=login_data
                    )
                    
                    if login_response.status_code == 200:
                        result["details"]["user_login"] = "success"
                        token_data = login_response.json()
                        
                        # Test authenticated endpoint
                        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
                        me_response = await client.get(
                            f"{self.backend_url}/api/v1/auth/me",
                            headers=headers
                        )
                        
                        if me_response.status_code == 200:
                            result["details"]["authenticated_request"] = "success"
                        else:
                            result["details"]["authenticated_request"] = "failed"
                    else:
                        result["details"]["user_login"] = "failed"
                else:
                    result["details"]["user_registration"] = "failed"
                    if register_response.status_code == 400:
                        # User might already exist, which is okay
                        result["details"]["registration_note"] = "User might already exist"
                
                self.print_status("Integration test completed", "success")
                
        except Exception as e:
            result["status"] = "error"
            result["errors"].append(f"Integration test failed: {str(e)}")
            self.print_status(f"Integration test failed: {str(e)}", "error")
        
        return result
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        total_time = time.time() - self.start_time
        
        # Count results
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results.values() if r["status"] == "success")
        warning_tests = sum(1 for r in self.results.values() if r["status"] == "warning")
        failed_tests = sum(1 for r in self.results.values() if r["status"] == "error")
        
        success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            "timestamp": datetime.utcnow().isoformat(),
            "total_execution_time": f"{total_time:.2f}s",
            "summary": {
                "total_tests": total_tests,
                "successful": successful_tests,
                "warnings": warning_tests,
                "failed": failed_tests,
                "success_rate": f"{success_rate:.1f}%",
                "overall_status": "PASS" if failed_tests == 0 else "FAIL"
            },
            "test_results": self.results,
            "recommendations": []
        }
        
        # Add recommendations
        if failed_tests == 0 and warning_tests == 0:
            report["recommendations"].append("✅ Setup is complete and ready for development!")
        elif failed_tests == 0:
            report["recommendations"].append("⚠️ Setup is functional but has some warnings to address")
        else:
            report["recommendations"].append("❌ Setup has critical issues that need to be resolved")
        
        if warning_tests > 0:
            report["recommendations"].append("📝 Review warnings and consider updating configuration")
        
        return report
    
    async def run_all_validations(self) -> Dict[str, Any]:
        """Run all validation tests"""
        self.print_status("🚀 Starting Koursia platform validation...", "info")
        print("=" * 60)
        
        # Run all validation tests
        self.results["environment"] = self.validate_environment_file()
        self.results["file_structure"] = self.validate_file_structure()
        self.results["docker_services"] = self.validate_docker_services()
        self.results["database"] = self.validate_database_connection()
        self.results["redis"] = self.validate_redis_connection()
        self.results["backend_api"] = await self.validate_backend_api()
        self.results["frontend"] = await self.validate_frontend()
        self.results["integration"] = await self.run_integration_test()
        
        # Generate report
        report = self.generate_report()
        
        # Save report
        report_file = self.base_dir / "validation_report.json"
        with open(report_file, "w") as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        print("\n" + "=" * 60)
        self.print_status("🎯 VALIDATION SUMMARY", "info")
        print("=" * 60)
        print(f"Total Tests: {report['summary']['total_tests']}")
        print(f"Successful: {report['summary']['successful']}")
        print(f"Warnings: {report['summary']['warnings']}")
        print(f"Failed: {report['summary']['failed']}")
        print(f"Success Rate: {report['summary']['success_rate']}")
        print(f"Overall Status: {report['summary']['overall_status']}")
        print(f"Execution Time: {report['total_execution_time']}")
        
        if report["recommendations"]:
            print("\n📋 Recommendations:")
            for rec in report["recommendations"]:
                print(f"  {rec}")
        
        print(f"\n📊 Detailed report saved to: {report_file}")
        
        return report


async def main():
    """Main validation function"""
    validator = SetupValidator()
    
    try:
        report = await validator.run_all_validations()
        
        # Exit with appropriate code
        if report["summary"]["overall_status"] == "PASS":
            print("\n🎉 All validations passed! Your Koursia development environment is ready!")
            sys.exit(0)
        else:
            print("\n❌ Some validations failed. Please check the report and fix the issues.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Validation failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
