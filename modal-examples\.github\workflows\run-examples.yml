name: Run

on:
  pull_request:
    branches:
      - main
    paths:
      - "**.py"
  push:
    branches:
      - main
    paths:
      - "**.py"
  workflow_dispatch:

# Cancel previous runs of the same PR but do not cancel previous runs on main
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

env:
  TERM: linux
  TERMINFO: /etc/terminfo
  MODAL_ENVIRONMENT: examples

jobs:
  # Output all changed files in a JSON format compatible with GitHub Actions job matrices
  diff-matrix:
    name: Generate matrix of changed examples
    runs-on: ubuntu-24.04
    outputs:
      matrix: ${{ steps.diff.outputs.all_changed_files }}

    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Find changed examples
        id: diff
        run: python3 -m internal.generate_diff_matrix

  # Run each changed example, using the output of the previous step as a job matrix
  run-changed:
    name: Run changed example
    needs: [diff-matrix]
    if:
      ${{ needs.diff-matrix.outputs.matrix != '[]' &&
      needs.diff-matrix.outputs.matrix != '' }}
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        file: ${{ fromJson(needs.diff-matrix.outputs.matrix) }}
      fail-fast: false

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 1
      - uses: ./.github/actions/setup

      - name: Run example
        run: |
          echo "Running ${{ matrix.file }}"
          stem=$(basename "${{ matrix.file }}" .py)
          python3 -m internal.run_example $stem || exit $?
        env:
          MODAL_TOKEN_ID: ${{ secrets.MODAL_MODAL_LABS_TOKEN_ID }}
          MODAL_TOKEN_SECRET: ${{ secrets.MODAL_MODAL_LABS_TOKEN_SECRET }}
