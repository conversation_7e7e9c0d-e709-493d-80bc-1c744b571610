"""
Slide generation service using Marp
"""

import asyncio
import subprocess
from typing import Dict, Any, List, Optional
from pathlib import Path
import json
import aiofiles
import uuid

from app.core.config import settings
from app.core.database import SessionLocal
from app.models.course import Course, Module, Lesson


class SlideService:
    """Service for generating slides using Marp"""
    
    def __init__(self):
        self.output_dir = Path("media/generated/slides")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.templates_dir = Path("templates/slides")
        self.templates_dir.mkdir(parents=True, exist_ok=True)
    
    async def generate_course_slides(
        self,
        course_id: str,
        template: str = "default"
    ) -> List[str]:
        """Generate slides for all lessons in a course"""
        
        db = SessionLocal()
        try:
            course = db.query(Course).filter(Course.id == course_id).first()
            if not course:
                raise ValueError(f"Course {course_id} not found")
            
            slide_files = []
            
            # Process each module
            for module in course.modules:
                for lesson in module.lessons:
                    if lesson.content:
                        slide_file = await self.generate_lesson_slides(
                            lesson.id,
                            lesson.title,
                            lesson.content,
                            template
                        )
                        
                        if slide_file:
                            slide_files.append(slide_file)
                            
                            # Update lesson with slides URL
                            lesson.slides_url = f"/media/generated/slides/{Path(slide_file).name}"
                            db.commit()
            
            return slide_files
            
        finally:
            db.close()
    
    async def generate_lesson_slides(
        self,
        lesson_id: str,
        lesson_title: str,
        lesson_content: str,
        template: str = "default"
    ) -> Optional[str]:
        """Generate slides for a single lesson"""
        
        try:
            # Parse lesson content to extract slide information
            slide_data = await self._parse_lesson_content(lesson_content, lesson_title)
            
            # Generate Marp markdown
            marp_content = await self._generate_marp_content(slide_data, template)
            
            # Create markdown file
            markdown_filename = f"lesson_{lesson_id}_{uuid.uuid4().hex[:8]}.md"
            markdown_path = self.output_dir / markdown_filename
            
            async with aiofiles.open(markdown_path, 'w', encoding='utf-8') as f:
                await f.write(marp_content)
            
            # Convert to PDF using Marp CLI
            pdf_filename = f"lesson_{lesson_id}_{uuid.uuid4().hex[:8]}.pdf"
            pdf_path = self.output_dir / pdf_filename
            
            success = await self._convert_to_pdf(markdown_path, pdf_path)
            
            if success:
                return str(pdf_path)
            else:
                # Fallback: return markdown file
                return str(markdown_path)
                
        except Exception as e:
            print(f"Error generating slides for lesson {lesson_id}: {e}")
            return None
    
    async def _parse_lesson_content(
        self,
        content: str,
        title: str
    ) -> List[Dict[str, Any]]:
        """Parse lesson content to extract slide information"""
        
        # This is a simplified parser - in a real implementation,
        # you might use the structured content from Gemini API
        
        slides = [
            {
                "title": title,
                "content": "# " + title + "\n\nWelcome to this lesson",
                "type": "title"
            }
        ]
        
        # Split content into logical sections
        sections = content.split('\n\n')
        
        for i, section in enumerate(sections):
            if len(section.strip()) > 50:  # Only create slides for substantial content
                slides.append({
                    "title": f"Section {i + 1}",
                    "content": self._format_slide_content(section),
                    "type": "content"
                })
        
        # Add summary slide
        slides.append({
            "title": "Summary",
            "content": "# Summary\n\n- Key points covered\n- Next steps\n- Questions?",
            "type": "summary"
        })
        
        return slides
    
    def _format_slide_content(self, content: str) -> str:
        """Format content for slide display"""
        
        # Clean up content and format for slides
        lines = content.split('\n')
        formatted_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                if len(line) > 80:  # Break long lines
                    words = line.split()
                    current_line = ""
                    for word in words:
                        if len(current_line + word) > 80:
                            if current_line:
                                formatted_lines.append("- " + current_line.strip())
                                current_line = word + " "
                            else:
                                formatted_lines.append("- " + word)
                                current_line = ""
                        else:
                            current_line += word + " "
                    if current_line:
                        formatted_lines.append("- " + current_line.strip())
                else:
                    formatted_lines.append("- " + line)
        
        return "\n".join(formatted_lines)
    
    async def _generate_marp_content(
        self,
        slides: List[Dict[str, Any]],
        template: str
    ) -> str:
        """Generate Marp markdown content"""
        
        # Load template
        template_content = await self._load_template(template)
        
        marp_content = [template_content]
        
        for slide in slides:
            marp_content.append("---")
            marp_content.append("")
            
            if slide["type"] == "title":
                marp_content.append("<!-- _class: title -->")
                marp_content.append("")
            elif slide["type"] == "summary":
                marp_content.append("<!-- _class: summary -->")
                marp_content.append("")
            
            marp_content.append(slide["content"])
            marp_content.append("")
        
        return "\n".join(marp_content)
    
    async def _load_template(self, template_name: str) -> str:
        """Load Marp template"""
        
        template_path = self.templates_dir / f"{template_name}.md"
        
        if template_path.exists():
            async with aiofiles.open(template_path, 'r', encoding='utf-8') as f:
                return await f.read()
        else:
            # Return default template
            return self._get_default_template()
    
    def _get_default_template(self) -> str:
        """Get default Marp template"""
        
        return """---
marp: true
theme: default
class: lead
paginate: true
backgroundColor: #fff
backgroundImage: url('https://marp.app/assets/hero-background.svg')
---

<style>
.title {
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.summary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

h1 {
  font-size: 2.5em;
  margin-bottom: 0.5em;
}

h2 {
  font-size: 2em;
  margin-bottom: 0.5em;
}

ul {
  font-size: 1.2em;
  line-height: 1.6;
}
</style>"""
    
    async def _convert_to_pdf(
        self,
        markdown_path: Path,
        pdf_path: Path
    ) -> bool:
        """Convert Marp markdown to PDF"""
        
        try:
            # Check if Marp CLI is available
            result = await asyncio.create_subprocess_exec(
                "marp", "--version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await result.communicate()
            
            if result.returncode != 0:
                print("Marp CLI not available, skipping PDF conversion")
                return False
            
            # Convert to PDF
            result = await asyncio.create_subprocess_exec(
                "marp",
                str(markdown_path),
                "--pdf",
                "--output", str(pdf_path),
                "--allow-local-files",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                return True
            else:
                print(f"Marp conversion error: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"Error converting to PDF: {e}")
            return False
    
    async def create_template(
        self,
        template_name: str,
        template_content: str
    ) -> bool:
        """Create a new slide template"""
        
        try:
            template_path = self.templates_dir / f"{template_name}.md"
            
            async with aiofiles.open(template_path, 'w', encoding='utf-8') as f:
                await f.write(template_content)
            
            return True
            
        except Exception as e:
            print(f"Error creating template: {e}")
            return False
    
    async def get_available_templates(self) -> List[str]:
        """Get list of available slide templates"""
        
        templates = ["default"]  # Always include default
        
        try:
            if self.templates_dir.exists():
                for template_file in self.templates_dir.glob("*.md"):
                    template_name = template_file.stem
                    if template_name != "default":
                        templates.append(template_name)
        except Exception as e:
            print(f"Error listing templates: {e}")
        
        return templates
