import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import type { RootState } from '../index'

export interface Course {
  id: string
  title: string
  description: string
  status: 'draft' | 'generating' | 'ready' | 'published' | 'archived'
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  duration_minutes?: number
  thumbnail_url?: string
  creator_id: string
  created_at: string
  updated_at: string
}

interface CoursesState {
  courses: Course[]
  currentCourse: Course | null
  isLoading: boolean
  error: string | null
}

const initialState: CoursesState = {
  courses: [],
  currentCourse: null,
  isLoading: false,
  error: null,
}

const coursesSlice = createSlice({
  name: 'courses',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setCourses: (state, action: PayloadAction<Course[]>) => {
      state.courses = action.payload
      state.isLoading = false
      state.error = null
    },
    setCurrentCourse: (state, action: PayloadAction<Course | null>) => {
      state.currentCourse = action.payload
    },
    addCourse: (state, action: PayloadAction<Course>) => {
      state.courses.unshift(action.payload)
    },
    updateCourse: (state, action: PayloadAction<Course>) => {
      const index = state.courses.findIndex(course => course.id === action.payload.id)
      if (index !== -1) {
        state.courses[index] = action.payload
      }
      if (state.currentCourse?.id === action.payload.id) {
        state.currentCourse = action.payload
      }
    },
    removeCourse: (state, action: PayloadAction<string>) => {
      state.courses = state.courses.filter(course => course.id !== action.payload)
      if (state.currentCourse?.id === action.payload) {
        state.currentCourse = null
      }
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload
      state.isLoading = false
    },
    clearError: (state) => {
      state.error = null
    },
  },
})

export const {
  setLoading,
  setCourses,
  setCurrentCourse,
  addCourse,
  updateCourse,
  removeCourse,
  setError,
  clearError,
} = coursesSlice.actions

// Selectors
export const selectCourses = (state: RootState) => state.courses.courses
export const selectCurrentCourse = (state: RootState) => state.courses.currentCourse
export const selectCoursesLoading = (state: RootState) => state.courses.isLoading
export const selectCoursesError = (state: RootState) => state.courses.error

export default coursesSlice.reducer
