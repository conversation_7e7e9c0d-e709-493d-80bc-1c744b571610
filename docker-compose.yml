version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: koursia-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-koursia}
      POSTGRES_USER: ${POSTGRES_USER:-koursia_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-koursia_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - koursia-network

  # Redis for caching and job queue
  redis:
    image: redis:7-alpine
    container_name: koursia-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - koursia-network

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: koursia-backend
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-koursia_user}:${POSTGRES_PASSWORD:-koursia_password}@postgres:5432/${POSTGRES_DB:-koursia}
      - REDIS_URL=redis://redis:6379
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_S3_BUCKET=${AWS_S3_BUCKET}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - CHATTERBOX_API_KEY=${CHATTERBOX_API_KEY}
      - PEXELS_API_KEY=${PEXELS_API_KEY}
      - PIXABAY_API_KEY=${PIXABAY_API_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    volumes:
      - ./backend:/app
      - media_storage:/app/media
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - koursia-network
    restart: unless-stopped

  # Celery Worker for background tasks
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: koursia-celery-worker
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-koursia_user}:${POSTGRES_PASSWORD:-koursia_password}@postgres:5432/${POSTGRES_DB:-koursia}
      - REDIS_URL=redis://redis:6379
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_S3_BUCKET=${AWS_S3_BUCKET}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - CHATTERBOX_API_KEY=${CHATTERBOX_API_KEY}
    volumes:
      - ./backend:/app
      - media_storage:/app/media
    depends_on:
      - postgres
      - redis
    networks:
      - koursia-network
    restart: unless-stopped

  # Celery Beat for scheduled tasks
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: koursia-celery-beat
    command: celery -A app.core.celery beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-koursia_user}:${POSTGRES_PASSWORD:-koursia_password}@postgres:5432/${POSTGRES_DB:-koursia}
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - redis
    networks:
      - koursia-network
    restart: unless-stopped

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: koursia-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - koursia-network
    restart: unless-stopped

  # AI Services Container (for GPU-intensive tasks)
  ai-services:
    build:
      context: ./ai-services
      dockerfile: Dockerfile
    container_name: koursia-ai-services
    environment:
      - REDIS_URL=redis://redis:6379
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - CHATTERBOX_API_KEY=${CHATTERBOX_API_KEY}
    volumes:
      - ./ai-services:/app
      - ai_models:/app/models
      - media_storage:/app/media
    ports:
      - "8001:8001"
    depends_on:
      - redis
    networks:
      - koursia-network
    restart: unless-stopped
    # Uncomment for GPU support
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # Media Processing Service
  media-processor:
    build:
      context: ./media-processing
      dockerfile: Dockerfile
    container_name: koursia-media-processor
    environment:
      - REDIS_URL=redis://redis:6379
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_S3_BUCKET=${AWS_S3_BUCKET}
      - PEXELS_API_KEY=${PEXELS_API_KEY}
      - PIXABAY_API_KEY=${PIXABAY_API_KEY}
    volumes:
      - ./media-processing:/app
      - media_storage:/app/media
    ports:
      - "8002:8002"
    depends_on:
      - redis
    networks:
      - koursia-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  media_storage:
  ai_models:

networks:
  koursia-network:
    driver: bridge
