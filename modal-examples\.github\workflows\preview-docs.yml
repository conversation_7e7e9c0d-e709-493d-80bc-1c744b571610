# deploys a preview version of the frontend including example changes
name: Preview Docs
on:
  pull_request:
  workflow_dispatch:

jobs:
  build-preview:
    name: Build and deploy preview
    runs-on: ubuntu-24.04
    env:
      MODAL_TOKEN_ID: ${{ secrets.MODAL_MODAL_LABS_TOKEN_ID }}
      MODAL_TOKEN_SECRET: ${{ secrets.MODAL_MODAL_LABS_TOKEN_SECRET }}
      MODAL_ENVIRONMENT: examples

    steps:
      - name: Checkout modal repo
        uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3
        with:
          repository: modal-labs/modal
          token: ${{ secrets.GH_PAT }}
          fetch-depth: 1
          path: modal
          persist-credentials: false

      - name: Install Python
        uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5
        with:
          python-version: "3.11"

      - name: Install base packages
        shell: bash
        run: |
          pip install uv
          uv pip install --system setuptools wheel

      - name: Install modal development packages
        run: |
          uv pip install --system -r modal/requirements.dev.txt

      - name: Install client
        run: |
          uv pip install --system modal

      - name: Install node
        uses: actions/setup-node@cdca7365b2dadb8aad0a33bc7601856ffabcc48e # v4
        with:
          node-version-file: modal/.nvmrc

      - name: Install node packages
        run: npm ci --include=dev
        working-directory: modal/frontend

      - name: Compile protos
        run: |
          inv protoc
        working-directory: modal

      - name: Checkout examples repo
        uses: actions/checkout@f43a0e5ff2bd294095638e18286ca9a3d1956744 # v3
        with:
          fetch-depth: 1
          path: modal/examples

      - name: Build and deploy preview
        id: deploy_preview
        working-directory: modal
        run: |
          set -o pipefail
          export DEPLOYMENT_ID=${GITHUB_SHA::7}
          inv frontend-preview --skip-update --deployment-id $DEPLOYMENT_ID | tee output.txt
          DEPLOYMENT_URL=$(cat output.txt | grep "$DEPLOYMENT_ID" | grep "modal.run" | tail -n 1)
          echo "DEPLOYMENT_URL=$DEPLOYMENT_URL" >> $GITHUB_OUTPUT

      - name: Post a comment with the preview URL
        if: github.event_name == 'pull_request'
        uses: actions/github-script@d7906e4ad0b1822421a7e6a35d5ca353c962f410 # v6
        with:
          github-token: ${{ secrets.GH_PAT }}
          script: |
            const deploymentUrl = `${{ steps.deploy_preview.outputs.DEPLOYMENT_URL }}`;
            const success_message = `🚀 The docs preview is ready! Check it out here: ${deploymentUrl}`;
            const failure_message = "Something went wrong with the preview deployment.";

            let comment = {
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
            }
            comment["body"] = deploymentUrl ? success_message : failure_message;
            github.rest.issues.createComment(comment)
