import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import type { RootState } from '../index'

export interface GenerationJob {
  id: string
  job_type: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress_percentage: number
  input_data?: any
  output_data?: any
  error_message?: string
  created_at: string
  updated_at: string
}

interface GenerationState {
  jobs: GenerationJob[]
  currentJob: GenerationJob | null
  isLoading: boolean
  error: string | null
}

const initialState: GenerationState = {
  jobs: [],
  currentJob: null,
  isLoading: false,
  error: null,
}

const generationSlice = createSlice({
  name: 'generation',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setJobs: (state, action: PayloadAction<GenerationJob[]>) => {
      state.jobs = action.payload
      state.isLoading = false
      state.error = null
    },
    setCurrentJob: (state, action: PayloadAction<GenerationJob | null>) => {
      state.currentJob = action.payload
    },
    addJob: (state, action: PayloadAction<GenerationJob>) => {
      state.jobs.unshift(action.payload)
    },
    updateJob: (state, action: PayloadAction<GenerationJob>) => {
      const index = state.jobs.findIndex(job => job.id === action.payload.id)
      if (index !== -1) {
        state.jobs[index] = action.payload
      }
      if (state.currentJob?.id === action.payload.id) {
        state.currentJob = action.payload
      }
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload
      state.isLoading = false
    },
    clearError: (state) => {
      state.error = null
    },
  },
})

export const {
  setLoading,
  setJobs,
  setCurrentJob,
  addJob,
  updateJob,
  setError,
  clearError,
} = generationSlice.actions

// Selectors
export const selectGenerationJobs = (state: RootState) => state.generation.jobs
export const selectCurrentJob = (state: RootState) => state.generation.currentJob
export const selectGenerationLoading = (state: RootState) => state.generation.isLoading
export const selectGenerationError = (state: RootState) => state.generation.error

export default generationSlice.reducer
