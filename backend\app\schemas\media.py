"""
Media schemas
"""

from pydantic import BaseModel, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID

from app.models.media import MediaType, MediaSource


class MediaAssetBase(BaseModel):
    """Base media asset schema"""
    title: str
    description: Optional[str] = None
    alt_text: Optional[str] = None
    tags: Optional[List[str]] = None


class MediaAssetCreate(MediaAssetBase):
    """Media asset creation schema"""
    pass


class MediaAssetUpdate(BaseModel):
    """Media asset update schema"""
    title: Optional[str] = None
    description: Optional[str] = None
    alt_text: Optional[str] = None
    tags: Optional[List[str]] = None


class MediaAssetResponse(MediaAssetBase):
    """Media asset response schema"""
    id: UUID
    filename: str
    original_filename: str
    file_path: str
    file_url: str
    file_size: int
    mime_type: str
    media_type: MediaType
    width: Optional[int] = None
    height: Optional[int] = None
    duration_seconds: Optional[float] = None
    source: MediaSource
    source_url: Optional[str] = None
    license_info: Optional[Dict[str, Any]] = None
    attribution: Optional[str] = None
    is_processed: bool
    processing_status: Optional[str] = None
    processing_error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    uploaded_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class MediaThumbnailResponse(BaseModel):
    """Media thumbnail response schema"""
    id: UUID
    size_name: str
    width: int
    height: int
    file_path: str
    file_url: str
    file_size: int
    media_asset_id: UUID
    
    class Config:
        from_attributes = True


class StockMediaSearchRequest(BaseModel):
    """Stock media search request schema"""
    query: str
    provider: str  # 'pexels' or 'pixabay'
    category: Optional[str] = None
    per_page: int = 20
    page: int = 1
    
    @validator('provider')
    def validate_provider(cls, v):
        if v not in ['pexels', 'pixabay']:
            raise ValueError('Provider must be either "pexels" or "pixabay"')
        return v
    
    @validator('per_page')
    def validate_per_page(cls, v):
        if v < 1 or v > 200:
            raise ValueError('per_page must be between 1 and 200')
        return v


class StockMediaItem(BaseModel):
    """Stock media item schema"""
    id: str
    url: str
    preview_url: str
    download_url: str
    title: str
    description: Optional[str] = None
    tags: List[str]
    width: int
    height: int
    file_size: Optional[int] = None
    photographer: Optional[str] = None
    photographer_url: Optional[str] = None
    license: str
    source: str


class StockMediaSearchResponse(BaseModel):
    """Stock media search response schema"""
    items: List[StockMediaItem]
    total: int
    page: int
    per_page: int
    pages: int
    query: str
    provider: str
